<?php
// Authentication and Authorization System

// เริ่ม session เฉพาะเมื่อยังไม่มี session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
require_once 'config/database.php';
require_once 'classes/AssetManager.php';

class Auth {
    private $pdo;
    private $availableFields = [];

    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->detectAvailableFields();
    }

    // ตรวจสอบฟิลด์ที่มีอยู่ในตาราง users
    private function detectAvailableFields() {
        try {
            $stmt = $this->pdo->query("DESCRIBE users");
            $columns = $stmt->fetchAll();
            foreach ($columns as $column) {
                $this->availableFields[] = $column['Field'];
            }
        } catch (PDOException $e) {
            error_log("Error detecting available fields: " . $e->getMessage());
        }
    }

    // ตรวจสอบว่ามีฟิลด์นี้หรือไม่
    private function hasField($fieldName) {
        return in_array($fieldName, $this->availableFields);
    }

    // ล็อกอิน
    public function login($username, $password) {
        try {
            // สร้าง SQL query ตามฟิลด์ที่มีอยู่
            $whereConditions = ["username = ?"];
            $params = [$username];

            // เพิ่มเงื่อนไข status ถ้ามีฟิลด์นี้
            if ($this->hasField('status')) {
                $whereConditions[] = "status = 'Active'";
            }

            $sql = "SELECT * FROM users WHERE " . implode(' AND ', $whereConditions);
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            $user = $stmt->fetch();

            if ($user) {
                // ตรวจสอบรหัสผ่าน
                if ($this->hasField('password') && isset($user['password']) && !empty($user['password'])) {
                    $passwordMatch = password_verify($password, $user['password']);

                    if ($passwordMatch) {
                        // อัพเดท last_login (ถ้ามีฟิลด์)
                        if ($this->hasField('last_login')) {
                            $this->updateLastLogin($user['id']);
                        }

                        // เก็บข้อมูลใน session
                        $_SESSION['user_id'] = $user['id'];
                        $_SESSION['username'] = $user['username'];
                        $_SESSION['full_name'] = $this->hasField('full_name') ? ($user['full_name'] ?? $user['username']) : $user['username'];
                        $_SESSION['role'] = $this->hasField('role') ? ($user['role'] ?? 'User') : 'User';
                        $_SESSION['logged_in'] = true;

                        return true;
                    }
                }
            }
            return false;
        } catch (PDOException $e) {
            error_log("Login error: " . $e->getMessage());
            return false;
        }
    }

    // ล็อกเอาท์
    public function logout() {
        session_destroy();
        header('Location: login.php');
        exit;
    }

    // ตรวจสอบการล็อกอิน
    public function isLoggedIn() {
        return isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true;
    }

    // ตรวจสอบ Role
    public function hasRole($role) {
        return isset($_SESSION['role']) && $_SESSION['role'] === $role;
    }

    // ตรวจสอบสิทธิ์ Admin
    public function isAdmin() {
        return $this->hasRole('Admin');
    }

    // ตรวจสอบสิทธิ์ User
    public function isUser() {
        return $this->hasRole('User');
    }

    // บังคับให้ล็อกอิน
    public function requireLogin() {
        if (!$this->isLoggedIn()) {
            header('Location: login.php');
            exit;
        }
    }

    // บังคับให้เป็น Admin
    public function requireAdmin() {
        $this->requireLogin();
        if (!$this->isAdmin()) {
            header('Location: index.php?error=access_denied');
            exit;
        }
    }

    // ดึงข้อมูลผู้ใช้ปัจจุบัน
    public function getCurrentUser() {
        if (!$this->isLoggedIn()) {
            return null;
        }
        
        $stmt = $this->pdo->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        return $stmt->fetch();
    }



    // อัพเดท last_login
    private function updateLastLogin($userId) {
        try {
            $stmt = $this->pdo->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
            $stmt->execute([$userId]);
        } catch (PDOException $e) {
            // ไม่ต้องทำอะไรถ้า column ไม่มี
        }
    }

    // ดึงรายการผู้ใช้ทั้งหมด (Admin เท่านั้น)
    public function getAllUsers() {
        // สร้าง SQL query ตามฟิลด์ที่มีอยู่
        $columns = ['id', 'username'];

        if ($this->hasField('full_name')) $columns[] = 'full_name';
        if ($this->hasField('email')) $columns[] = 'email';
        if ($this->hasField('role')) $columns[] = 'role';
        if ($this->hasField('status')) $columns[] = 'status';
        if ($this->hasField('last_login')) $columns[] = 'last_login';
        if ($this->hasField('created_date')) $columns[] = 'created_date';

        $sql = "SELECT " . implode(', ', $columns) . " FROM users ORDER BY id DESC";
        $stmt = $this->pdo->query($sql);
        return $stmt->fetchAll();
    }

    // สร้างผู้ใช้ใหม่ (Admin เท่านั้น)
    public function createUser($data) {
        try {
            // ตรวจสอบว่า username ซ้ำหรือไม่
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM users WHERE username = ?");
            $stmt->execute([$data['username']]);
            if ($stmt->fetchColumn() > 0) {
                return false; // username ซ้ำ
            }

            // สร้าง SQL query ตามฟิลด์ที่มีอยู่
            $columns = ['username'];
            $values = [$data['username']];
            $placeholders = ['?'];

            // เพิ่ม password ถ้ามีฟิลด์และข้อมูล
            if ($this->hasField('password') && !empty($data['password'])) {
                $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT);
                $columns[] = 'password';
                $values[] = $hashedPassword;
                $placeholders[] = '?';
            }

            // เพิ่มฟิลด์อื่นๆ ตามที่มีอยู่
            if ($this->hasField('full_name') && !empty($data['full_name'])) {
                $columns[] = 'full_name';
                $values[] = $data['full_name'];
                $placeholders[] = '?';
            }

            if ($this->hasField('email') && !empty($data['email'])) {
                $columns[] = 'email';
                $values[] = $data['email'];
                $placeholders[] = '?';
            }

            if ($this->hasField('role')) {
                $columns[] = 'role';
                $values[] = $data['role'] ?? 'User';
                $placeholders[] = '?';
            }

            if ($this->hasField('status')) {
                $columns[] = 'status';
                $values[] = $data['status'] ?? 'Active';
                $placeholders[] = '?';
            }

            $sql = "INSERT INTO users (" . implode(', ', $columns) . ") VALUES (" . implode(', ', $placeholders) . ")";
            $stmt = $this->pdo->prepare($sql);
            return $stmt->execute($values);
        } catch (PDOException $e) {
            error_log("Create user error: " . $e->getMessage());
            return false;
        }
    }

    // อัพเดทผู้ใช้ (Admin เท่านั้น)
    public function updateUser($id, $data) {
        try {
            // ตรวจสอบว่า username ซ้ำหรือไม่ (ยกเว้นตัวเอง)
            $stmt = $this->pdo->prepare("SELECT COUNT(*) FROM users WHERE username = ? AND id != ?");
            $stmt->execute([$data['username'], $id]);
            if ($stmt->fetchColumn() > 0) {
                return false; // username ซ้ำ
            }

            // สร้าง SQL query ตามฟิลด์ที่มีอยู่
            $setParts = ['username = ?'];
            $params = [$data['username']];

            if ($this->hasField('full_name')) {
                $setParts[] = 'full_name = ?';
                $params[] = $data['full_name'] ?? '';
            }

            if ($this->hasField('email')) {
                $setParts[] = 'email = ?';
                $params[] = $data['email'] ?? '';
            }

            if ($this->hasField('role')) {
                $setParts[] = 'role = ?';
                $params[] = $data['role'] ?? 'User';
            }

            if ($this->hasField('status')) {
                $setParts[] = 'status = ?';
                $params[] = $data['status'] ?? 'Active';
            }

            // ถ้ามีการเปลี่ยนรหัสผ่าน
            if (!empty($data['password'])) {
                $setParts[] = 'password = ?';
                $params[] = password_hash($data['password'], PASSWORD_DEFAULT);
            }

            $sql = "UPDATE users SET " . implode(', ', $setParts) . " WHERE id = ?";
            $params[] = $id;

            $stmt = $this->pdo->prepare($sql);
            return $stmt->execute($params);
        } catch (PDOException $e) {
            error_log("Update user error: " . $e->getMessage());
            return false;
        }
    }

    // ลบผู้ใช้ (Admin เท่านั้น)
    public function deleteUser($id) {
        // ป้องกันการลบ admin ตัวเอง
        if ($id == $_SESSION['user_id']) {
            return false;
        }
        
        $stmt = $this->pdo->prepare("DELETE FROM users WHERE id = ?");
        return $stmt->execute([$id]);
    }

    // ดึงข้อมูลผู้ใช้ตาม ID
    public function getUserById($id) {
        $stmt = $this->pdo->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->fetch();
    }

    // ตรวจสอบว่าผู้ใช้มี password ในฐานข้อมูลหรือไม่
    public function hasPassword($userId) {
        try {
            $user = $this->getUserById($userId);
            return $user && isset($user['password']) && !empty($user['password']);
        } catch (PDOException $e) {
            error_log("Check password error: " . $e->getMessage());
            return false;
        }
    }

    // เปลี่ยนรหัสผ่าน
    public function changePassword($userId, $oldPassword, $newPassword) {
        try {
            $user = $this->getUserById($userId);

            if ($user && isset($user['password']) && !empty($user['password'])) {
                // ตรวจสอบรหัสผ่านเดิมจากฐานข้อมูลเท่านั้น
                if (password_verify($oldPassword, $user['password'])) {
                    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
                    $stmt = $this->pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
                    return $stmt->execute([$hashedPassword, $userId]);
                }
            }

            return false;
        } catch (PDOException $e) {
            error_log("Change password error: " . $e->getMessage());
            return false;
        }
    }

    // ตั้งรหัสผ่านใหม่ (สำหรับผู้ใช้ที่ไม่มี password)
    public function setPassword($userId, $newPassword) {
        try {
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
            $stmt = $this->pdo->prepare("UPDATE users SET password = ? WHERE id = ?");
            return $stmt->execute([$hashedPassword, $userId]);
        } catch (PDOException $e) {
            error_log("Set password error: " . $e->getMessage());
            return false;
        }
    }
}

// สร้าง instance ของ Auth
$auth = new Auth($pdo);

// Helper functions
function getCurrentUsername() {
    return $_SESSION['username'] ?? 'guest';
}

function getCurrentUserRole() {
    return $_SESSION['role'] ?? 'Guest';
}

function getCurrentUserFullName() {
    return $_SESSION['full_name'] ?? 'Guest User';
}

function isLoggedIn() {
    global $auth;
    return $auth->isLoggedIn();
}

function isAdmin() {
    global $auth;
    return $auth->isAdmin();
}

function requireLogin() {
    global $auth;
    $auth->requireLogin();
}

function requireAdmin() {
    global $auth;
    $auth->requireAdmin();
}
?>
