<?php
// ทดสอบ add_asset.php ขั้นสุดท้าย
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🎯 ทดสอบ add_asset.php ขั้นสุดท้าย</h2>";

// เริ่ม session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// ตั้งค่า session
if (!isset($_SESSION['username'])) {
    $_SESSION['username'] = 'admin';
    $_SESSION['role'] = 'Admin';
    $_SESSION['user_id'] = 1;
    echo "<p style='color: blue;'>ℹ️ ตั้งค่า session: admin/Admin</p>";
}

// ตรวจสอบการเชื่อมต่อฐานข้อมูล
echo "<h3>1. ตรวจสอบการเชื่อมต่อฐานข้อมูล:</h3>";
try {
    $pdo = new PDO("mysql:host=localhost;dbname=asset_management;charset=utf8", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✅ เชื่อมต่อฐานข้อมูลสำเร็จ</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ ไม่สามารถเชื่อมต่อฐานข้อมูล: " . $e->getMessage() . "</p>";
    die();
}

// ตรวจสอบตาราง assets
echo "<h3>2. ตรวจสอบตาราง assets:</h3>";
try {
    $stmt = $pdo->query("DESCRIBE assets");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "<p style='color: green;'>✅ ตาราง assets มีอยู่</p>";
    echo "<p><strong>คอลัมน์:</strong> " . implode(', ', $columns) . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ ตาราง assets ไม่มี: " . $e->getMessage() . "</p>";
    die();
}

// ทดสอบการส่งข้อมูลไป add_asset.php
echo "<h3>3. ทดสอบการส่งข้อมูลไป add_asset.php:</h3>";
if ($_POST && isset($_POST['test_add_asset'])) {
    echo "<h4>กำลังส่งข้อมูลไป add_asset.php...</h4>";
    
    // ข้อมูลที่จะส่ง
    $postData = [
        'type' => trim($_POST['type'] ?? ''),
        'brand' => trim($_POST['brand'] ?? ''),
        'model' => trim($_POST['model'] ?? ''),
        'tag' => trim($_POST['tag'] ?? ''),
        'department' => trim($_POST['department'] ?? ''),
        'status' => trim($_POST['status'] ?? 'ใช้งาน'),
        'hostname' => trim($_POST['hostname'] ?? ''),
        'operating_system' => trim($_POST['operating_system'] ?? ''),
        'serial_number' => trim($_POST['serial_number'] ?? ''),
        'asset_id' => trim($_POST['asset_id'] ?? ''),
        'warranty_expire' => !empty($_POST['warranty_expire']) ? $_POST['warranty_expire'] : '',
        'description' => trim($_POST['description'] ?? ''),
        'set_name' => trim($_POST['set_name'] ?? '')
    ];
    
    echo "<h4>ข้อมูลที่ส่ง:</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'><th style='padding: 8px;'>Field</th><th style='padding: 8px;'>Value</th></tr>";
    foreach ($postData as $key => $value) {
        echo "<tr><td style='padding: 8px;'><strong>$key</strong></td><td style='padding: 8px;'>$value</td></tr>";
    }
    echo "</table>";
    
    if (empty($postData['type'])) {
        echo "<p style='color: red;'>❌ Type เป็นฟิลด์ที่จำเป็น</p>";
    } else {
        // ส่งข้อมูลไป add_asset.php
        $postString = http_build_query($postData);
        
        $context = stream_context_create([
            'http' => [
                'method' => 'POST',
                'header' => 'Content-type: application/x-www-form-urlencoded',
                'content' => $postString
            ]
        ]);
        
        try {
            $result = file_get_contents('http://localhost/asset/add_asset.php', false, $context);
            
            if ($result !== false) {
                echo "<p style='color: green;'>✅ ส่งข้อมูลไป add_asset.php สำเร็จ</p>";
                
                // ตรวจสอบผลลัพธ์
                if (strpos($result, 'เพิ่ม Asset สำเร็จ') !== false) {
                    echo "<p style='color: green;'>✅ พบข้อความสำเร็จ: เพิ่ม Asset สำเร็จ</p>";
                    
                    // ดึง ID ที่เพิ่ม
                    if (preg_match('/ID: (\d+)/', $result, $matches)) {
                        $newId = $matches[1];
                        echo "<p style='color: green;'>✅ Asset ID ที่เพิ่ม: <strong>$newId</strong></p>";
                        
                        // ตรวจสอบข้อมูลในฐานข้อมูล
                        try {
                            $checkStmt = $pdo->prepare("SELECT * FROM assets WHERE id = ?");
                            $checkStmt->execute([$newId]);
                            $newAsset = $checkStmt->fetch();
                            
                            if ($newAsset) {
                                echo "<h4>ข้อมูล Asset ที่เพิ่มในฐานข้อมูล:</h4>";
                                echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
                                echo "<tr style='background: #f8f9fa;'><th style='padding: 8px;'>Field</th><th style='padding: 8px;'>Value</th></tr>";
                                foreach ($newAsset as $key => $value) {
                                    if (!is_numeric($key)) {
                                        echo "<tr><td style='padding: 8px;'><strong>$key</strong></td><td style='padding: 8px;'>$value</td></tr>";
                                    }
                                }
                                echo "</table>";
                                echo "<p style='color: green;'>✅ ข้อมูลถูกบันทึกในฐานข้อมูลเรียบร้อย</p>";
                            } else {
                                echo "<p style='color: red;'>❌ ไม่พบข้อมูลในฐานข้อมูล</p>";
                            }
                        } catch (Exception $e) {
                            echo "<p style='color: red;'>❌ ข้อผิดพลาดในการตรวจสอบฐานข้อมูล: " . $e->getMessage() . "</p>";
                        }
                    }
                    
                } elseif (strpos($result, 'error') !== false || strpos($result, 'Error') !== false || strpos($result, 'Fatal') !== false) {
                    echo "<p style='color: red;'>❌ พบข้อผิดพลาดในผลลัพธ์</p>";
                    
                    // แสดงข้อผิดพลาด
                    if (preg_match('/(error|Error|Fatal).*?<br>/i', $result, $matches)) {
                        echo "<p style='color: red;'><strong>ข้อผิดพลาด:</strong> " . strip_tags($matches[0]) . "</p>";
                    }
                    
                } else {
                    echo "<p style='color: blue;'>ℹ️ ไม่พบข้อความสำเร็จหรือข้อผิดพลาดที่ชัดเจน</p>";
                }
                
                // แสดงส่วนหนึ่งของผลลัพธ์
                echo "<h5>ส่วนหนึ่งของผลลัพธ์ HTML:</h5>";
                echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; max-height: 300px; overflow-y: auto; font-family: monospace; font-size: 0.8rem;'>";
                echo htmlspecialchars(substr($result, 0, 2000));
                if (strlen($result) > 2000) {
                    echo "...(ตัดทอน)";
                }
                echo "</div>";
                
            } else {
                echo "<p style='color: red;'>❌ ไม่สามารถส่งข้อมูลไป add_asset.php ได้</p>";
            }
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ ข้อผิดพลาดในการส่งข้อมูล: " . $e->getMessage() . "</p>";
        }
    }
}

// นับจำนวน assets ในฐานข้อมูล
echo "<h3>4. สถิติ Assets ในฐานข้อมูล:</h3>";
try {
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM assets");
    $count = $stmt->fetch()['total'];
    echo "<p><strong>จำนวน Assets ทั้งหมด:</strong> $count รายการ</p>";
    
    if ($count > 0) {
        // ตรวจสอบคอลัมน์วันที่ที่มีอยู่
        $stmt = $pdo->query("DESCRIBE assets");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

        // เลือกคอลัมน์วันที่ที่มีอยู่
        $dateColumn = '';
        if (in_array('created_date', $columns)) {
            $dateColumn = 'created_date';
        } elseif (in_array('date_added', $columns)) {
            $dateColumn = 'date_added';
        }

        // สร้าง SQL ตามคอลัมน์ที่มีอยู่
        $selectFields = "id, type, brand, model, status";
        if ($dateColumn) {
            $selectFields .= ", $dateColumn as date_added";
        }

        $stmt = $pdo->query("SELECT $selectFields FROM assets ORDER BY id DESC LIMIT 5");
        $recentAssets = $stmt->fetchAll();

        echo "<h4>Assets 5 รายการล่าสุด:</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>Type</th>";
        echo "<th style='padding: 8px;'>Brand</th>";
        echo "<th style='padding: 8px;'>Model</th>";
        echo "<th style='padding: 8px;'>Status</th>";
        echo "<th style='padding: 8px;'>Date Added</th>";
        echo "</tr>";

        foreach ($recentAssets as $asset) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>{$asset['id']}</td>";
            echo "<td style='padding: 8px;'>{$asset['type']}</td>";
            echo "<td style='padding: 8px;'>{$asset['brand']}</td>";
            echo "<td style='padding: 8px;'>{$asset['model']}</td>";
            echo "<td style='padding: 8px;'>{$asset['status']}</td>";
            $dateAdded = $asset['date_added'] ?? 'N/A';
            echo "<td style='padding: 8px;'>$dateAdded</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ ข้อผิดพลาดในการดูสถิติ: " . $e->getMessage() . "</p>";
}

?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบ add_asset.php ขั้นสุดท้าย</title>
    <style>
        body {
            font-family: 'TH Sarabun New', Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h2 {
            background: #28a745;
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        h3 {
            background: #6c757d;
            color: white;
            padding: 10px;
            border-radius: 5px;
        }
        .form-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
        }
        .form-group {
            flex: 1;
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 16px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>

<div class="form-section">
    <h3>🎯 ทดสอบ add_asset.php</h3>
    <form method="POST">
        <div class="form-row">
            <div class="form-group">
                <label for="type">Type * (จำเป็น)</label>
                <select id="type" name="type" required>
                    <option value="">เลือก Type</option>
                    <option value="Desktop">Desktop</option>
                    <option value="Laptop">Laptop</option>
                    <option value="Monitor">Monitor</option>
                    <option value="All-in-one">All-in-one</option>
                    <option value="Multifunction Laser Printer">Multifunction Laser Printer</option>
                    <option value="Barcode Printer">Barcode Printer</option>
                    <option value="Barcode Scanner">Barcode Scanner</option>
                    <option value="Tablet">Tablet</option>
                    <option value="UPS">UPS</option>
                    <option value="Queue">Queue</option>
                    <option value="IP Phone">IP Phone</option>
                    <option value="Teleconference">Teleconference</option>
                    <option value="Switch">Switch</option>
                    <option value="Access Point">Access Point</option>
                    <option value="Peripheral">Peripheral</option>
                </select>
            </div>
            <div class="form-group">
                <label for="brand">Brand</label>
                <select id="brand" name="brand">
                    <option value="">เลือก Brand</option>
                    <option value="Dell">Dell</option>
                    <option value="Lenovo">Lenovo</option>
                    <option value="Microsoft">Microsoft</option>
                    <option value="Apple">Apple</option>
                    <option value="Zebra">Zebra</option>
                    <option value="HP">HP</option>
                    <option value="Philips">Philips</option>
                    <option value="Acer">Acer</option>
                    <option value="LG">LG</option>
                    <option value="Cisco">Cisco</option>
                </select>
            </div>
        </div>
        
        <div class="form-row">
            <div class="form-group">
                <label for="model">Model</label>
                <input type="text" id="model" name="model" placeholder="ระบุรุ่น">
            </div>
            <div class="form-group">
                <label for="tag">Tag</label>
                <input type="text" id="tag" name="tag" placeholder="ระบุ Tag">
            </div>
        </div>
        
        <div class="form-row">
            <div class="form-group">
                <label for="department">Department</label>
                <input type="text" id="department" name="department" placeholder="ระบุแผนก">
            </div>
            <div class="form-group">
                <label for="status">Status</label>
                <select id="status" name="status">
                    <option value="ใช้งาน">ใช้งาน</option>
                    <option value="ชำรุด">ชำรุด</option>
                    <option value="สำรอง">สำรอง</option>
                </select>
            </div>
        </div>
        
        <div class="form-row">
            <div class="form-group">
                <label for="hostname">Hostname</label>
                <input type="text" id="hostname" name="hostname" placeholder="ระบุ Hostname">
            </div>
            <div class="form-group">
                <label for="operating_system">Operating System</label>
                <select id="operating_system" name="operating_system">
                    <option value="">เลือก Operating System</option>
                    <option value="Windows 7">Windows 7</option>
                    <option value="Windows 10">Windows 10</option>
                    <option value="Windows 11">Windows 11</option>
                    <option value="MacOS">MacOS</option>
                </select>
            </div>
        </div>
        
        <div class="form-row">
            <div class="form-group">
                <label for="serial_number">Serial Number</label>
                <input type="text" id="serial_number" name="serial_number" placeholder="ระบุ Serial Number">
            </div>
            <div class="form-group">
                <label for="asset_id">Asset ID</label>
                <input type="text" id="asset_id" name="asset_id" placeholder="ระบุ Asset ID">
            </div>
        </div>
        
        <div class="form-row">
            <div class="form-group">
                <label for="warranty_expire">Warranty Expire</label>
                <input type="date" id="warranty_expire" name="warranty_expire">
            </div>
            <div class="form-group">
                <label for="set_name">Set</label>
                <input type="text" id="set_name" name="set_name" placeholder="ระบุ Set">
            </div>
        </div>
        
        <div class="form-group">
            <label for="description">Description</label>
            <textarea id="description" name="description" rows="3" placeholder="รายละเอียด"></textarea>
        </div>
        
        <div class="form-group">
            <input type="hidden" name="test_add_asset" value="1">
            <button type="submit">🎯 ทดสอบเพิ่ม Asset</button>
        </div>
    </form>
</div>

<div style="margin: 20px 0;">
    <a href="add_asset.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">➕ ไปหน้าเพิ่ม Asset</a>
    <a href="full_test_add_asset.php" style="background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">🔍 ทดสอบ Full</a>
    <a href="index.php" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">🏠 หน้าหลัก</a>
</div>

</body>
</html>
