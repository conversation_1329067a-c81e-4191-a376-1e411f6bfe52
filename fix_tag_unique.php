<?php
// ไฟล์สำหรับแก้ไข Tag ให้สามารถซ้ำกันได้
// รันไฟล์นี้เพียงครั้งเดียวแล้วลบทิ้ง

require_once 'config/database.php';

try {
    echo "<h2>🔧 กำลังแก้ไข Tag ให้สามารถซ้ำกันได้...</h2>";
    echo "<div style='font-family: monospace; background: #f8f9fa; padding: 20px; border-radius: 8px;'>";
    
    // ตรวจสอบโครงสร้างตารางปัจจุบัน
    echo "<h3>📋 ตรวจสอบโครงสร้างตารางปัจจุบัน:</h3>";
    $stmt = $pdo->query("SHOW CREATE TABLE assets");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<pre style='background: white; padding: 10px; border: 1px solid #ddd;'>";
    echo htmlspecialchars($result['Create Table']);
    echo "</pre>";
    
    // ตรวจสอบ indexes ปัจจุบัน
    echo "<h3>🔍 ตรวจสอบ Indexes ปัจจุบัน:</h3>";
    $stmt = $pdo->query("SHOW INDEX FROM assets WHERE Column_name = 'tag'");
    $indexes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($indexes)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #e9ecef;'>";
        echo "<th style='padding: 8px;'>Key Name</th>";
        echo "<th style='padding: 8px;'>Unique</th>";
        echo "<th style='padding: 8px;'>Column</th>";
        echo "</tr>";
        
        foreach ($indexes as $index) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($index['Key_name']) . "</td>";
            echo "<td style='padding: 8px;'>" . ($index['Non_unique'] == 0 ? 'YES' : 'NO') . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($index['Column_name']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: green;'>✅ ไม่พบ index บน tag column</p>";
    }
    
    // ลบ UNIQUE constraint จาก tag
    echo "<h3>🔧 กำลังลบ UNIQUE constraint จาก tag:</h3>";
    
    $hasUniqueConstraint = false;
    foreach ($indexes as $index) {
        if ($index['Non_unique'] == 0) { // UNIQUE index
            $hasUniqueConstraint = true;
            $keyName = $index['Key_name'];
            
            try {
                $pdo->exec("ALTER TABLE assets DROP INDEX `$keyName`");
                echo "<p style='color: green;'>✅ ลบ UNIQUE constraint '$keyName' จาก tag สำเร็จ</p>";
            } catch (PDOException $e) {
                if (strpos($e->getMessage(), "check that column/key exists") !== false) {
                    echo "<p style='color: orange;'>⚠️ UNIQUE constraint '$keyName' ไม่มีอยู่แล้ว</p>";
                } else {
                    echo "<p style='color: red;'>❌ ข้อผิดพลาด: " . $e->getMessage() . "</p>";
                }
            }
        }
    }
    
    if (!$hasUniqueConstraint) {
        echo "<p style='color: green;'>✅ tag field ไม่มี UNIQUE constraint อยู่แล้ว</p>";
    }
    
    // เพิ่ม index ธรรมดาสำหรับ tag (เพื่อความเร็วในการค้นหา)
    echo "<h3>📊 เพิ่ม Index ธรรมดาสำหรับ tag:</h3>";
    try {
        $pdo->exec("ALTER TABLE assets ADD INDEX idx_tag (tag)");
        echo "<p style='color: green;'>✅ เพิ่ม index ธรรมดาสำหรับ tag สำเร็จ</p>";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), "Duplicate key name") !== false) {
            echo "<p style='color: orange;'>⚠️ Index 'idx_tag' มีอยู่แล้ว</p>";
        } else {
            echo "<p style='color: red;'>❌ ข้อผิดพลาด: " . $e->getMessage() . "</p>";
        }
    }
    
    // ตรวจสอบโครงสร้างตารางหลังการแก้ไข
    echo "<h3>✅ ตรวจสอบโครงสร้างตารางหลังการแก้ไข:</h3>";
    $stmt = $pdo->query("SHOW INDEX FROM assets WHERE Column_name = 'tag'");
    $newIndexes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($newIndexes)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #e9ecef;'>";
        echo "<th style='padding: 8px;'>Key Name</th>";
        echo "<th style='padding: 8px;'>Unique</th>";
        echo "<th style='padding: 8px;'>Column</th>";
        echo "</tr>";
        
        foreach ($newIndexes as $index) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($index['Key_name']) . "</td>";
            echo "<td style='padding: 8px;'>" . ($index['Non_unique'] == 0 ? 'YES' : 'NO') . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($index['Column_name']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // ทดสอบการเพิ่มข้อมูลที่มี tag ซ้ำ
    echo "<h3>🧪 ทดสอบการเพิ่มข้อมูลที่มี tag ซ้ำ:</h3>";
    
    // ตรวจสอบว่ามี tag ซ้ำอยู่แล้วหรือไม่
    $stmt = $pdo->query("SELECT tag, COUNT(*) as count FROM assets WHERE tag IS NOT NULL AND tag != '' GROUP BY tag HAVING count > 1 LIMIT 5");
    $duplicates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($duplicates)) {
        echo "<p style='color: green;'>✅ พบ tag ที่ซ้ำกันในฐานข้อมูลแล้ว:</p>";
        echo "<ul>";
        foreach ($duplicates as $dup) {
            echo "<li>Tag: '{$dup['tag']}' มี {$dup['count']} รายการ</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: blue;'>ℹ️ ยังไม่มี tag ที่ซ้ำกันในฐานข้อมูล</p>";
        
        // ลองเพิ่มข้อมูลทดสอบ
        try {
            $testTag = 'TEST_DUPLICATE_' . date('His');
            
            // เพิ่มรายการแรก
            $stmt = $pdo->prepare("INSERT INTO assets (type, tag, created_by, updated_by) VALUES (?, ?, 'system', 'system')");
            $stmt->execute(['Test', $testTag]);
            echo "<p style='color: green;'>✅ เพิ่มรายการทดสอบแรกสำเร็จ (tag: $testTag)</p>";
            
            // เพิ่มรายการที่สอง (tag ซ้ำ)
            $stmt->execute(['Test', $testTag]);
            echo "<p style='color: green;'>✅ เพิ่มรายการทดสอบที่สอง (tag ซ้ำ) สำเร็จ!</p>";
            
            // ลบข้อมูลทดสอบ
            $stmt = $pdo->prepare("DELETE FROM assets WHERE tag = ?");
            $stmt->execute([$testTag]);
            echo "<p style='color: blue;'>🗑️ ลบข้อมูลทดสอบแล้ว</p>";
            
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ ไม่สามารถเพิ่มข้อมูลที่มี tag ซ้ำได้: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h3>🎉 สรุปผลการแก้ไข:</h3>";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border: 1px solid #c3e6cb;'>";
    echo "<p><strong>✅ Tag field สามารถซ้ำกันได้แล้ว!</strong></p>";
    echo "<p>• ลบ UNIQUE constraint จาก tag field</p>";
    echo "<p>• เพิ่ม index ธรรมดาเพื่อความเร็วในการค้นหา</p>";
    echo "<p>• ทดสอบการเพิ่มข้อมูลที่มี tag ซ้ำสำเร็จ</p>";
    echo "</div>";
    
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; border: 1px solid #f5c6cb;'>";
    echo "<h3 style='color: #721c24;'>❌ เกิดข้อผิดพลาด:</h3>";
    echo "<p style='color: #721c24;'>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<br><h3>🔗 <a href='index.php'>กลับไปหน้าหลัก</a></h3>";
?>
