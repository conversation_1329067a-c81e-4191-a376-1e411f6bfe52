<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// ตั้งค่า timezone
date_default_timezone_set('Asia/Bangkok');

echo "<h2>🚀 Starting Import Process for sample_import.csv</h2>\n";
echo "<pre>\n";

// ตรวจสอบไฟล์
$csvFile = 'sample_import.csv';
if (!file_exists($csvFile)) {
    echo "❌ Error: File $csvFile not found!\n";
    exit;
}

echo "✅ File found: $csvFile\n";
echo "📊 File size: " . number_format(filesize($csvFile)) . " bytes\n\n";

// อ่านไฟล์ CSV
$content = file_get_contents($csvFile);
if ($content === false) {
    echo "❌ Error: Cannot read CSV file!\n";
    exit;
}

// ลบ BOM ถ้ามี
$content = str_replace("\xEF\xBB\xBF", '', $content);

// แยกบรรทัด
$lines = preg_split('/\r\n|\r|\n/', $content);
$lines = array_filter($lines, function($line) {
    return trim($line) !== '';
});

echo "📋 Total lines: " . count($lines) . "\n";

if (empty($lines)) {
    echo "❌ Error: CSV file is empty!\n";
    exit;
}

// อ่าน header
$headerLine = array_shift($lines);
$headers = str_getcsv($headerLine);

// ทำความสะอาด headers
$headers = array_map(function($header) {
    return trim($header, " \t\n\r\0\x0B\"'");
}, $headers);

echo "📝 Headers found: " . implode(', ', $headers) . "\n";
echo "📊 Data rows: " . count($lines) . "\n\n";

// สร้าง column mapping
$columnMap = [
    'Type' => 'type',
    'Brand' => 'brand', 
    'Model' => 'model',
    'Tag' => 'tag',
    'Department' => 'department',
    'Status' => 'status',
    'Hostname' => 'hostname',
    'Operating System' => 'operating_system',
    'Serial Number' => 'serial_number',
    'Asset ID' => 'asset_id',
    'Warranty Expire' => 'warranty_expire',
    'Description' => 'description',
    'Set' => 'set_name'
];

// สร้าง index mapping
$indexMap = [];
foreach ($headers as $index => $header) {
    if (isset($columnMap[$header])) {
        $indexMap[$index] = $columnMap[$header];
    }
}

echo "🗺️ Column mapping:\n";
foreach ($indexMap as $index => $dbColumn) {
    echo "   $index: {$headers[$index]} → $dbColumn\n";
}
echo "\n";

// เริ่ม transaction
try {
    $pdo->beginTransaction();
    
    $imported = 0;
    $errors = 0;
    $skipped = 0;
    $errorDetails = [];
    
    echo "🔄 Processing rows...\n\n";
    
    foreach ($lines as $lineNumber => $line) {
        $rowNumber = $lineNumber + 2; // +2 เพราะ header และ 0-based index
        
        // ข้ามบรรทัดว่าง
        if (trim($line) === '') {
            $skipped++;
            continue;
        }
        
        // แปลงบรรทัดเป็น array
        $data = str_getcsv($line);
        
        // ปรับจำนวน column ให้เท่ากับ header
        if (count($data) < count($headers)) {
            $data = array_pad($data, count($headers), '');
        } else if (count($data) > count($headers)) {
            $data = array_slice($data, 0, count($headers));
        }
        
        // ข้ามแถวที่ไม่มีข้อมูล
        if (empty(array_filter($data, function($value) { return trim($value) !== ''; }))) {
            $skipped++;
            continue;
        }
        
        // เตรียมข้อมูลสำหรับ insert
        $insertData = [];
        
        foreach ($indexMap as $index => $dbColumn) {
            $value = isset($data[$index]) ? trim($data[$index]) : '';
            
            if (!empty($value)) {
                // ประมวลผลข้อมูลตามประเภท
                if ($dbColumn === 'warranty_expire') {
                    $value = parseDate($value);
                }
                $insertData[$dbColumn] = $value;
            }
        }
        
        // ตรวจสอบข้อมูลที่จำเป็น
        if (empty($insertData['type'])) {
            $errors++;
            $errorDetails[] = "Row $rowNumber: Missing Type";
            continue;
        }
        
        // Tag สามารถซ้ำกันได้ - ไม่ต้องตรวจสอบ
        // Tag duplicate check removed - tags can be duplicated
        
        // เพิ่มข้อมูลระบบ
        $insertData['created_by'] = 'system';
        $insertData['updated_by'] = 'system';
        $insertData['created_date'] = date('Y-m-d H:i:s');
        $insertData['updated_date'] = date('Y-m-d H:i:s');
        
        try {
            // Insert ข้อมูล
            $columns = array_keys($insertData);
            $placeholders = ':' . implode(', :', $columns);
            $sql = "INSERT INTO assets (`" . implode("`, `", $columns) . "`) VALUES ($placeholders)";
            
            $stmt = $pdo->prepare($sql);
            if ($stmt->execute($insertData)) {
                $assetId = $pdo->lastInsertId();
                
                // บันทึก log
                $logStmt = $pdo->prepare("INSERT INTO asset_logs (asset_id, action_type, changed_by, description, changed_date) VALUES (?, 'CREATE', 'system', 'Imported from CSV', ?)");
                $logStmt->execute([$assetId, date('Y-m-d H:i:s')]);
                
                $imported++;
                
                // แสดงความคืบหน้า
                if ($imported % 50 == 0) {
                    echo "✅ Imported: $imported records\n";
                }
            } else {
                $errors++;
                $errorDetails[] = "Row $rowNumber: Database insert failed";
            }
            
        } catch (Exception $e) {
            $errors++;
            $errorDetails[] = "Row $rowNumber: " . $e->getMessage();
        }
    }
    
    $pdo->commit();
    
    echo "\n🎉 Import completed!\n";
    echo "✅ Successfully imported: $imported records\n";
    echo "❌ Errors: $errors records\n";
    echo "⏭️ Skipped: $skipped records\n";
    
    if (!empty($errorDetails)) {
        echo "\n📋 Error details (first 20):\n";
        foreach (array_slice($errorDetails, 0, 20) as $detail) {
            echo "   • $detail\n";
        }
        if (count($errorDetails) > 20) {
            echo "   ... and " . (count($errorDetails) - 20) . " more errors\n";
        }
    }
    
} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollback();
    }
    echo "❌ Fatal error: " . $e->getMessage() . "\n";
}

// ฟังก์ชันแปลงวันที่
function parseDate($dateString) {
    // ลองหลายรูปแบบ
    $formats = ['d-m-Y', 'Y-m-d', 'm/d/Y', 'd/m/Y', 'Y/m/d'];
    
    foreach ($formats as $format) {
        $date = DateTime::createFromFormat($format, $dateString);
        if ($date !== false) {
            return $date->format('Y-m-d');
        }
    }
    
    // ถ้าเป็นตัวเลข (Excel date)
    if (is_numeric($dateString)) {
        $excelDate = intval($dateString);
        if ($excelDate > 25569) { // Excel epoch
            $unixDate = ($excelDate - 25569) * 86400;
            return date('Y-m-d', $unixDate);
        }
    }
    
    return null;
}

echo "\n</pre>\n";
echo "<h3>🔗 <a href='index.php'>กลับไปหน้าหลัก</a></h3>\n";
?>
