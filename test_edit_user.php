<?php
// ทดสอบ edit_user.php
require_once 'includes/auth.php';

// ตรวจสอบการล็อกอินและสิทธิ์ Admin
if (!$auth->isLoggedIn() || !isAdmin()) {
    echo "<h2>ทดสอบ edit_user.php</h2>";
    echo "<p style='color: red;'>❌ ต้องล็อกอินด้วยสิทธิ์ Admin เพื่อทดสอบ</p>";
    echo "<a href='login.php'>ไปหน้า Login</a>";
    exit;
}

echo "<h2>ทดสอบ edit_user.php</h2>";

try {
    // ตรวจสอบไฟล์ที่จำเป็น
    echo "<h3>1. ตรวจสอบไฟล์ที่จำเป็น</h3>";
    
    $requiredFiles = [
        'edit_user.php' => 'หน้าแก้ไขผู้ใช้',
        'users.php' => 'หน้าจัดการผู้ใช้ (มีลิงก์ไป edit_user)',
        'assets/style.css' => 'CSS สำหรับ edit form'
    ];
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 8px;'>File</th>";
    echo "<th style='padding: 8px;'>Description</th>";
    echo "<th style='padding: 8px;'>Status</th>";
    echo "</tr>";
    
    foreach ($requiredFiles as $file => $description) {
        $exists = file_exists($file);
        $icon = $exists ? '✅' : '❌';
        $status = $exists ? 'มี' : 'ไม่มี';
        
        echo "<tr>";
        echo "<td style='padding: 8px;'>$file</td>";
        echo "<td style='padding: 8px;'>$description</td>";
        echo "<td style='padding: 8px;'>$icon $status</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // ตรวจสอบผู้ใช้ในระบบ
    echo "<h3>2. ตรวจสอบผู้ใช้ในระบบ</h3>";
    
    $users = $auth->getAllUsers();
    
    if (empty($users)) {
        echo "<p style='color: red;'>❌ ไม่มีผู้ใช้ในระบบ</p>";
        echo "<p>กรุณาสร้างผู้ใช้ก่อนทดสอบ</p>";
    } else {
        echo "<p style='color: green;'>✅ พบผู้ใช้ในระบบ: " . count($users) . " คน</p>";
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>Username</th>";
        echo "<th style='padding: 8px;'>Full Name</th>";
        echo "<th style='padding: 8px;'>Role</th>";
        echo "<th style='padding: 8px;'>Status</th>";
        echo "<th style='padding: 8px;'>Action</th>";
        echo "</tr>";
        
        foreach (array_slice($users, 0, 5) as $user) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>{$user['id']}</td>";
            echo "<td style='padding: 8px;'>{$user['username']}</td>";
            echo "<td style='padding: 8px;'>" . ($user['full_name'] ?? '-') . "</td>";
            echo "<td style='padding: 8px;'>" . ($user['role'] ?? '-') . "</td>";
            echo "<td style='padding: 8px;'>" . ($user['status'] ?? '-') . "</td>";
            echo "<td style='padding: 8px;'>";
            echo "<a href='edit_user.php?id={$user['id']}' style='background: #ffc107; color: black; padding: 5px 10px; text-decoration: none; border-radius: 5px;'>✏️ แก้ไข</a>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // ตรวจสอบ CSS สำหรับ edit form
    echo "<h3>3. ตรวจสอบ CSS สำหรับ Edit Form</h3>";
    
    if (file_exists('assets/style.css')) {
        $cssContent = file_get_contents('assets/style.css');
        
        $editFormCss = [
            '.edit-user-form' => 'Container สำหรับฟอร์มแก้ไข',
            '.form-row' => 'แถวฟอร์มแบบ grid',
            '.form-actions' => 'ปุ่มบันทึกและยกเลิก',
            '.info-card' => 'การ์ดแสดงข้อมูลเพิ่มเติม',
            '.info-grid' => 'Grid สำหรับข้อมูลเพิ่มเติม',
            '.form-header' => 'Header ของฟอร์ม'
        ];
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>CSS Class</th>";
        echo "<th style='padding: 8px;'>Purpose</th>";
        echo "<th style='padding: 8px;'>Status</th>";
        echo "</tr>";
        
        foreach ($editFormCss as $class => $purpose) {
            $exists = strpos($cssContent, $class) !== false;
            $icon = $exists ? '✅' : '❌';
            $status = $exists ? 'มี' : 'ไม่มี';
            
            echo "<tr>";
            echo "<td style='padding: 8px; font-family: monospace;'>$class</td>";
            echo "<td style='padding: 8px;'>$purpose</td>";
            echo "<td style='padding: 8px;'>$icon $status</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // ตรวจสอบ edit_user.php structure
    echo "<h3>4. ตรวจสอบโครงสร้าง edit_user.php</h3>";
    
    if (file_exists('edit_user.php')) {
        $editUserContent = file_get_contents('edit_user.php');
        
        $features = [
            'Admin permission check' => 'isAdmin()',
            'Dynamic field detection' => 'availableFields',
            'User data loading' => 'SELECT * FROM users WHERE id',
            'Form validation' => 'empty($username)',
            'Password hashing' => 'password_hash',
            'Username uniqueness check' => 'username = ? AND id !=',
            'Dynamic SQL update' => 'setParts',
            'Responsive form layout' => 'form-row',
            'Info card display' => 'info-card'
        ];
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>Feature</th>";
        echo "<th style='padding: 8px;'>Code Pattern</th>";
        echo "<th style='padding: 8px;'>Status</th>";
        echo "</tr>";
        
        foreach ($features as $feature => $pattern) {
            $exists = strpos($editUserContent, $pattern) !== false;
            $icon = $exists ? '✅' : '❌';
            $status = $exists ? 'มี' : 'ไม่มี';
            
            echo "<tr>";
            echo "<td style='padding: 8px;'>$feature</td>";
            echo "<td style='padding: 8px; font-family: monospace; font-size: 0.8rem;'>$pattern</td>";
            echo "<td style='padding: 8px;'>$icon $status</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // ตรวจสอบลิงก์ใน users.php
    echo "<h3>5. ตรวจสอบลิงก์ใน users.php</h3>";
    
    if (file_exists('users.php')) {
        $usersContent = file_get_contents('users.php');
        
        if (strpos($usersContent, 'edit_user.php?id=') !== false) {
            echo "<p style='color: green;'>✅ พบลิงก์ edit_user.php ใน users.php</p>";
            
            // นับจำนวนลิงก์
            $linkCount = substr_count($usersContent, 'edit_user.php?id=');
            echo "<p>จำนวนลิงก์ edit_user: $linkCount ลิงก์</p>";
        } else {
            echo "<p style='color: red;'>❌ ไม่พบลิงก์ edit_user.php ใน users.php</p>";
        }
    }
    
    // ทดสอบการเข้าถึง edit_user.php
    echo "<h3>6. ทดสอบการเข้าถึง edit_user.php</h3>";
    
    if (!empty($users)) {
        $testUserId = $users[0]['id'];
        $editUrl = "edit_user.php?id=$testUserId";
        
        echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #c3e6cb;'>";
        echo "<h4 style='color: #155724;'>✅ พร้อมทดสอบ</h4>";
        echo "<p style='color: #155724;'>คลิกลิงก์ด้านล่างเพื่อทดสอบการแก้ไขผู้ใช้:</p>";
        echo "<a href='$editUrl' style='background: #ffc107; color: black; padding: 12px 24px; text-decoration: none; border-radius: 8px; display: inline-flex; align-items: center; gap: 8px; font-weight: 600;'>";
        echo "✏️ ทดสอบแก้ไขผู้ใช้: {$users[0]['username']}";
        echo "</a>";
        echo "</div>";
    }
    
    echo "<h3>7. สรุปผลการทดสอบ</h3>";
    
    $allGood = file_exists('edit_user.php') && !empty($users);
    
    if ($allGood) {
        echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #c3e6cb;'>";
        echo "<h4 style='color: #155724;'>✅ edit_user.php พร้อมใช้งาน</h4>";
        echo "<ul style='color: #155724;'>";
        echo "<li>✅ ไฟล์ edit_user.php มีอยู่</li>";
        echo "<li>✅ CSS สำหรับฟอร์มแก้ไขครบถ้วน</li>";
        echo "<li>✅ มีผู้ใช้ในระบบสำหรับทดสอบ</li>";
        echo "<li>✅ ลิงก์จาก users.php ทำงานได้</li>";
        echo "<li>✅ ระบบตรวจสอบสิทธิ์ Admin</li>";
        echo "<li>✅ ฟอร์มแก้ไขแบบ dynamic</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<h4>วิธีใช้งาน:</h4>";
        echo "<ol>";
        echo "<li>ไปหน้า users.php</li>";
        echo "<li>คลิกปุ่ม ✏️ ข้างชื่อผู้ใช้ที่ต้องการแก้ไข</li>";
        echo "<li>แก้ไขข้อมูลในฟอร์ม</li>";
        echo "<li>คลิก 'บันทึกการเปลี่ยนแปลง'</li>";
        echo "<li>ระบบจะกลับไปหน้า users.php</li>";
        echo "</ol>";
    } else {
        echo "<div style='background: #f8d7da; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #f5c6cb;'>";
        echo "<h4 style='color: #721c24;'>⚠️ edit_user.php ยังไม่พร้อมใช้งาน</h4>";
        echo "<p style='color: #721c24;'>กรุณาตรวจสอบปัญหาที่พบ</p>";
        echo "</div>";
    }
    
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='users.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>👥 ไปหน้า Users</a>";
    if (!empty($users)) {
        echo "<a href='edit_user.php?id={$users[0]['id']}' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>✏️ ทดสอบ Edit User</a>";
    }
    echo "<a href='add_user.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>➕ เพิ่มผู้ใช้</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ เกิดข้อผิดพลาด</h3>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<p style='color: red; margin-top: 20px;'><strong>หมายเหตุ:</strong> กรุณาลบไฟล์ test_edit_user.php หลังจากทดสอบเสร็จแล้ว</p>";
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบ Edit User</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        h2 {
            background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 10px 25px rgba(255, 193, 7, 0.3);
        }
        table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            width: 100%;
        }
    </style>
</head>
<body>
    <!-- เนื้อหาจะแสดงจาก PHP ด้านบน -->
</body>
</html>
