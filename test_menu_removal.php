<?php
// ทดสอบการลบเมนู "เพิ่ม Asset" จาก users.php
require_once 'includes/auth.php';

// ตรวจสอบการล็อกอิน
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

echo "<h2>ทดสอบการลบเมนู 'เพิ่ม Asset' จาก users.php</h2>";

try {
    // ตรวจสอบไฟล์ users.php
    echo "<h3>1. ตรวจสอบการแก้ไข users.php</h3>";
    
    if (file_exists('users.php')) {
        $usersContent = file_get_contents('users.php');
        
        // ตรวจสอบว่าลบเมนู "เพิ่ม Asset" แล้วหรือไม่
        $hasAddAssetMenu = strpos($usersContent, 'เพิ่ม Asset') !== false;
        $hasAddAssetLink = strpos($usersContent, 'add_asset.php') !== false;
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>การตรวจสอบ</th>";
        echo "<th style='padding: 8px;'>ผลลัพธ์</th>";
        echo "<th style='padding: 8px;'>สถานะ</th>";
        echo "</tr>";
        
        echo "<tr>";
        echo "<td style='padding: 8px;'>ข้อความ 'เพิ่ม Asset'</td>";
        echo "<td style='padding: 8px;'>" . ($hasAddAssetMenu ? 'ยังมีอยู่' : 'ลบแล้ว') . "</td>";
        echo "<td style='padding: 8px;'>" . ($hasAddAssetMenu ? '❌ ยังไม่ลบ' : '✅ ลบสำเร็จ') . "</td>";
        echo "</tr>";
        
        echo "<tr>";
        echo "<td style='padding: 8px;'>ลิงก์ 'add_asset.php'</td>";
        echo "<td style='padding: 8px;'>" . ($hasAddAssetLink ? 'ยังมีอยู่' : 'ลบแล้ว') . "</td>";
        echo "<td style='padding: 8px;'>" . ($hasAddAssetLink ? '❌ ยังไม่ลบ' : '✅ ลบสำเร็จ') . "</td>";
        echo "</tr>";
        
        echo "</table>";
        
        // แสดงเมนูปัจจุบัน
        echo "<h4>เมนูปัจจุบันใน users.php:</h4>";
        
        // ดึงส่วน navigation
        preg_match('/<nav class="nav">(.*?)<\/nav>/s', $usersContent, $navMatches);
        if (!empty($navMatches[1])) {
            $navContent = $navMatches[1];
            preg_match_all('/<li><a[^>]*>([^<]+)<\/a><\/li>/', $navContent, $menuMatches);
            
            if (!empty($menuMatches[1])) {
                echo "<ul style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
                foreach ($menuMatches[1] as $index => $menuItem) {
                    echo "<li style='margin: 5px 0;'>📋 " . trim($menuItem) . "</li>";
                }
                echo "</ul>";
            }
        }
        
    } else {
        echo "<p style='color: red;'>❌ ไม่พบไฟล์ users.php</p>";
    }
    
    // เปรียบเทียบกับ index.php
    echo "<h3>2. เปรียบเทียบเมนูกับ index.php</h3>";
    
    if (file_exists('index.php')) {
        $indexContent = file_get_contents('index.php');
        
        // ดึงเมนูจาก index.php
        preg_match('/<nav class="nav">(.*?)<\/nav>/s', $indexContent, $indexNavMatches);
        if (!empty($indexNavMatches[1])) {
            $indexNavContent = $indexNavMatches[1];
            preg_match_all('/<li><a[^>]*>([^<]+)<\/a><\/li>/', $indexNavContent, $indexMenuMatches);
            
            echo "<div style='display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;'>";
            
            // เมนู index.php
            echo "<div style='background: white; padding: 20px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);'>";
            echo "<h4>เมนูใน index.php:</h4>";
            if (!empty($indexMenuMatches[1])) {
                echo "<ul>";
                foreach ($indexMenuMatches[1] as $menuItem) {
                    echo "<li style='margin: 5px 0;'>📋 " . trim($menuItem) . "</li>";
                }
                echo "</ul>";
            }
            echo "</div>";
            
            // เมนู users.php
            echo "<div style='background: white; padding: 20px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);'>";
            echo "<h4>เมนูใน users.php:</h4>";
            if (!empty($menuMatches[1])) {
                echo "<ul>";
                foreach ($menuMatches[1] as $menuItem) {
                    echo "<li style='margin: 5px 0;'>📋 " . trim($menuItem) . "</li>";
                }
                echo "</ul>";
            }
            echo "</div>";
            
            echo "</div>";
        }
    }
    
    // ตรวจสอบความสอดคล้องของเมนู
    echo "<h3>3. ตรวจสอบความสอดคล้องของเมนู</h3>";
    
    $expectedMenus = [
        'รายการ Assets' => 'เมนูหลักสำหรับดู Assets',
        'จัดการผู้ใช้' => 'เมนูสำหรับ Admin จัดการผู้ใช้',
        'โปรไฟล์' => 'เมนูโปรไฟล์ผู้ใช้',
        'ออกจากระบบ' => 'เมนู logout'
    ];
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 8px;'>เมนูที่คาดหวัง</th>";
    echo "<th style='padding: 8px;'>คำอธิบาย</th>";
    echo "<th style='padding: 8px;'>สถานะใน users.php</th>";
    echo "</tr>";
    
    foreach ($expectedMenus as $menu => $description) {
        $exists = isset($menuMatches[1]) && in_array($menu, array_map('trim', $menuMatches[1]));
        $icon = $exists ? '✅' : '❌';
        $status = $exists ? 'มี' : 'ไม่มี';
        
        echo "<tr>";
        echo "<td style='padding: 8px;'>$menu</td>";
        echo "<td style='padding: 8px;'>$description</td>";
        echo "<td style='padding: 8px;'>$icon $status</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // ตรวจสอบเมนูที่ไม่ควรมี
    $unwantedMenus = [
        'เพิ่ม Asset' => 'ไม่ควรมีใน users.php'
    ];
    
    echo "<h4>เมนูที่ไม่ควรมีใน users.php:</h4>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 8px;'>เมนูที่ไม่ควรมี</th>";
    echo "<th style='padding: 8px;'>เหตุผล</th>";
    echo "<th style='padding: 8px;'>สถานะ</th>";
    echo "</tr>";
    
    foreach ($unwantedMenus as $menu => $reason) {
        $exists = isset($menuMatches[1]) && in_array($menu, array_map('trim', $menuMatches[1]));
        $icon = $exists ? '❌ ยังมี' : '✅ ลบแล้ว';
        
        echo "<tr>";
        echo "<td style='padding: 8px;'>$menu</td>";
        echo "<td style='padding: 8px;'>$reason</td>";
        echo "<td style='padding: 8px;'>$icon</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // สรุปผลการแก้ไข
    echo "<h3>4. สรุปผลการแก้ไข</h3>";
    
    $allGood = !$hasAddAssetMenu && !$hasAddAssetLink;
    
    if ($allGood) {
        echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #c3e6cb;'>";
        echo "<h4 style='color: #155724;'>✅ ลบเมนู 'เพิ่ม Asset' สำเร็จ</h4>";
        echo "<ul style='color: #155724;'>";
        echo "<li>✅ ลบข้อความ 'เพิ่ม Asset' แล้ว</li>";
        echo "<li>✅ ลบลิงก์ 'add_asset.php' แล้ว</li>";
        echo "<li>✅ เมนูใน users.php เหมาะสมแล้ว</li>";
        echo "<li>✅ ไม่มีเมนูที่ไม่เกี่ยวข้องกับการจัดการผู้ใช้</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<h4>เหตุผลในการลบเมนู 'เพิ่ม Asset':</h4>";
        echo "<ul>";
        echo "<li>🎯 <strong>Focus:</strong> หน้า users.php มุ่งเน้นการจัดการผู้ใช้เท่านั้น</li>";
        echo "<li>🧹 <strong>Clean UI:</strong> ลดความซับซ้อนของเมนู</li>";
        echo "<li>👥 <strong>User Context:</strong> ผู้ใช้ที่เข้ามาจัดการผู้ใช้ไม่จำเป็นต้องเพิ่ม Asset</li>";
        echo "<li>🔄 <strong>Navigation:</strong> สามารถไปเพิ่ม Asset ได้จากหน้า index.php</li>";
        echo "</ul>";
    } else {
        echo "<div style='background: #f8d7da; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #f5c6cb;'>";
        echo "<h4 style='color: #721c24;'>⚠️ การลบเมนูยังไม่สมบูรณ์</h4>";
        echo "<p style='color: #721c24;'>ยังพบเมนู 'เพิ่ม Asset' ในไฟล์ users.php</p>";
        echo "</div>";
    }
    
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='users.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>👥 ดูหน้า Users</a>";
    echo "<a href='index.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🏠 กลับหน้าหลัก</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ เกิดข้อผิดพลาด</h3>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<p style='color: red; margin-top: 20px;'><strong>หมายเหตุ:</strong> กรุณาลบไฟล์ test_menu_removal.php หลังจากทดสอบเสร็จแล้ว</p>";
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบการลบเมนู</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        h2 {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 10px 25px rgba(220, 53, 69, 0.3);
        }
        table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <!-- เนื้อหาจะแสดงจาก PHP ด้านบน -->
</body>
</html>
