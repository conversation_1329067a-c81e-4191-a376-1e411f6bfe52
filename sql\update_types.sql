-- อัพเดท Type ในฐานข้อมูลให้ตรงกับตัวเลือกใหม่
-- รันไฟล์นี้ใน phpMyAdmin หรือ MySQL command line

USE asset_management;

-- อัพเดท Type ที่มีอยู่แล้วให้ตรงกับตัวเลือกใหม่
UPDATE assets SET type = 'Desktop' WHERE type = 'Computer';
UPDATE assets SET type = 'Desktop' WHERE type = 'PC';
UPDATE assets SET type = 'Desktop' WHERE type = 'Desktop Computer';

-- อัพเดท Operating System ให้ตรงกับตัวเลือกใหม่
UPDATE assets SET operating_system = 'Windows 11' WHERE operating_system = 'Windows 11 Pro';
UPDATE assets SET operating_system = 'Windows 10' WHERE operating_system = 'Windows 10 Pro';
UPDATE assets SET operating_system = 'Windows 7' WHERE operating_system = 'Windows 7 Pro';

-- เพิ่มข้อมูลตัวอย่างใหม่สำหรับ Type ต่างๆ
INSERT INTO assets (type, brand, model, tag, department, status, hostname, operating_system, serial_number, asset_id, warranty_expire, description, asset_set, created_by, updated_by) VALUES 
('All-in-one', 'Apple', 'iMac 24"', 'AIO001', 'Design', 'Active', 'DESIGN-AIO-001', 'MacOS', 'AP123456789', 'AST006', '2025-08-15', 'All-in-one computer for design team', 'Design Set 1', 'admin', 'admin'),
('Tablet', 'Apple', 'iPad Pro 12.9"', 'TAB001', 'Sales', 'Active', NULL, NULL, 'AP987654321', 'AST007', '2024-11-30', 'Tablet for sales presentation', 'Mobile Set 2', 'admin', 'admin'),
('UPS', 'APC', 'Smart-UPS 1500VA', 'UPS001', 'IT Department', 'Active', NULL, NULL, 'APC11223344', 'AST008', '2026-02-28', 'Uninterruptible Power Supply for server room', 'Power Set 1', 'admin', 'admin'),
('Barcode Scanner', 'Zebra', 'DS2208', 'BS001', 'Warehouse', 'Active', NULL, NULL, 'ZB556677889', 'AST009', '2025-05-10', 'Handheld barcode scanner for inventory', 'Warehouse Set 1', 'admin', 'admin'),
('IP Phone', 'Cisco', 'IP Phone 8841', 'PH001', 'Reception', 'Active', 'RECEPTION-PH-001', NULL, 'CS334455667', 'AST010', '2025-09-20', 'IP phone for reception desk', 'Communication Set 1', 'admin', 'admin'),
('Access Point', 'Ubiquiti', 'UniFi AP AC Pro', 'AP001', 'IT Department', 'Active', 'IT-AP-001', NULL, 'UB778899001', 'AST011', '2025-12-05', 'Wireless access point for office', 'Network Set 2', 'admin', 'admin'),
('Teleconference', 'Logitech', 'MeetUp Conference Cam', 'TC001', 'Meeting Room A', 'Active', 'MEETING-TC-001', NULL, 'LG445566778', 'AST012', '2025-07-18', 'Conference camera for video meetings', 'Meeting Set 1', 'admin', 'admin'),
('Queue', 'Custom', 'Digital Queue Display', 'Q001', 'Customer Service', 'Active', 'CS-QUEUE-001', NULL, 'CQ123456789', 'AST013', '2025-04-25', 'Digital queue management system', 'Service Set 1', 'admin', 'admin'),
('Barcode Printer', 'Zebra', 'ZD420', 'BP001', 'Warehouse', 'Active', NULL, NULL, 'ZB998877665', 'AST014', '2025-06-12', 'Thermal barcode label printer', 'Warehouse Set 2', 'admin', 'admin'),
('Peripheral', 'Logitech', 'MX Master 3 Mouse', 'PER001', 'IT Department', 'Active', NULL, NULL, 'LG112233445', 'AST015', '2024-10-30', 'Wireless mouse for workstation', 'Peripheral Set 1', 'admin', 'admin');

-- แสดงข้อมูล Type ทั้งหมดหลังการอัพเดท
SELECT DISTINCT type FROM assets ORDER BY type;

-- แสดงจำนวน Asset แต่ละ Type
SELECT type, COUNT(*) as count FROM assets GROUP BY type ORDER BY type;
