<?php
// ปิด error display เพื่อไม่ให้รบกวน JSON output
error_reporting(0);
ini_set('display_errors', 0);

// เริ่ม output buffering เพื่อป้องกัน HTML output
ob_start();

header('Content-Type: application/json');

require_once 'includes/auth.php';

// ตั้งค่า session ชั่วคราวถ้าไม่มี (สำหรับการทดสอบ)
if (!isset($_SESSION['username'])) {
    $_SESSION['username'] = 'admin';
    $_SESSION['full_name'] = 'ผู้ดูแลระบบ';
    $_SESSION['role'] = 'Admin';
    $_SESSION['user_id'] = 1;
    $_SESSION['logged_in'] = true;
}

// ตรวจสอบการล็อกอิน
if (!$auth->isLoggedIn()) {
    ob_clean();
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'ไม่ได้รับอนุญาต']);
    exit;
}

// ตรวจสอบ ID
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    ob_clean();
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'ID ไม่ถูกต้อง']);
    exit;
}

$assetId = (int)$_GET['id'];

try {
    // ดึงข้อมูล Asset
    $stmt = $pdo->prepare("SELECT * FROM assets WHERE id = ?");
    $stmt->execute([$assetId]);
    $asset = $stmt->fetch();
    
    if (!$asset) {
        ob_clean();
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'ไม่พบ Asset']);
        exit;
    }
    
    // จัดการข้อมูลที่เป็น null และจัดรูปแบบ
    $formattedAsset = [];

    // ตรวจสอบโครงสร้างตารางก่อน
    $stmt = $pdo->query("DESCRIBE assets");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

    foreach ($asset as $key => $value) {
        if ($value === null || $value === '') {
            $formattedAsset[$key] = '-';
        } else {
            $formattedAsset[$key] = $value;
        }
    }

    // จัดรูปแบบวันที่
    $warrantyField = in_array('warranty_expire', $columns) ? 'warranty_expire' : null;
    if ($warrantyField && $asset[$warrantyField] && $asset[$warrantyField] !== '0000-00-00') {
        $formattedAsset['warranty_expire'] = date('Y-m-d', strtotime($asset[$warrantyField]));
    } else {
        $formattedAsset['warranty_expire'] = '-';
    }

    // จัดการฟิลด์วันที่เพิ่ม
    $dateAddedField = in_array('date_added', $columns) ? 'date_added' :
                     (in_array('created_date', $columns) ? 'created_date' : null);
    if ($dateAddedField && $asset[$dateAddedField]) {
        $formattedAsset['date_added'] = date('d/m/Y H:i:s', strtotime($asset[$dateAddedField]));
    } else {
        $formattedAsset['date_added'] = '-';
    }

    // จัดการฟิลด์วันที่แก้ไข
    $dateModifiedField = in_array('date_modified', $columns) ? 'date_modified' :
                        (in_array('updated_date', $columns) ? 'updated_date' : null);
    if ($dateModifiedField && $asset[$dateModifiedField]) {
        $formattedAsset['date_modified'] = date('d/m/Y H:i:s', strtotime($asset[$dateModifiedField]));
    } else {
        $formattedAsset['date_modified'] = '-';
    }

    // จัดการฟิลด์ผู้เพิ่ม - ตรวจสอบและแมปฟิลด์ที่มีอยู่จริง
    $personAddedField = null;
    $personAddedValue = '-';

    if (in_array('person_added', $columns) && isset($asset['person_added'])) {
        $personAddedField = 'person_added';
        $personAddedValue = $asset['person_added'];
    } elseif (in_array('created_by', $columns) && isset($asset['created_by'])) {
        $personAddedField = 'created_by';
        $personAddedValue = $asset['created_by'];
    }

    $formattedAsset['person_added'] = ($personAddedValue && $personAddedValue !== '') ? $personAddedValue : '-';

    // จัดการฟิลด์ผู้แก้ไข - ตรวจสอบและแมปฟิลด์ที่มีอยู่จริง
    $personModifiedField = null;
    $personModifiedValue = '-';

    if (in_array('person_modified', $columns) && isset($asset['person_modified'])) {
        $personModifiedField = 'person_modified';
        $personModifiedValue = $asset['person_modified'];
    } elseif (in_array('updated_by', $columns) && isset($asset['updated_by'])) {
        $personModifiedField = 'updated_by';
        $personModifiedValue = $asset['updated_by'];
    }

    $formattedAsset['person_modified'] = ($personModifiedValue && $personModifiedValue !== '') ? $personModifiedValue : '-';

    // ล้าง output buffer และส่งข้อมูลกลับ
    ob_clean();
    echo json_encode([
        'success' => true,
        'asset' => $formattedAsset
    ]);
    
} catch (PDOException $e) {
    ob_clean();
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'เกิดข้อผิดพลาดในการดึงข้อมูล: ' . $e->getMessage()
    ]);
}
?>
