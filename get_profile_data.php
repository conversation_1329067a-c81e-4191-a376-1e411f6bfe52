<?php
require_once 'includes/auth.php';

// ตรวจสอบการล็อกอิน
if (!$auth->isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'ไม่ได้ล็อกอิน']);
    exit;
}

try {
    // ดึงข้อมูลผู้ใช้ปัจจุบัน
    $user = $auth->getCurrentUser();
    
    if (!$user) {
        echo json_encode(['success' => false, 'message' => 'ไม่พบข้อมูลผู้ใช้']);
        exit;
    }
    
    // ตรวจสอบฟิลด์ที่มีอยู่ในฐานข้อมูล
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll();
    $availableFields = [];
    foreach ($columns as $column) {
        $availableFields[] = $column['Field'];
    }
    
    // สร้างข้อมูลผู้ใช้ตามฟิลด์ที่มีอยู่
    $userData = [
        'id' => $user['id'],
        'username' => $user['username']
    ];
    
    // เพิ่มฟิลด์อื่นๆ ถ้ามีอยู่
    if (in_array('full_name', $availableFields)) {
        $userData['full_name'] = $user['full_name'] ?? '';
    }
    
    if (in_array('email', $availableFields)) {
        $userData['email'] = $user['email'] ?? '';
    }
    
    if (in_array('role', $availableFields)) {
        $userData['role'] = $user['role'] ?? '';
    }
    
    if (in_array('status', $availableFields)) {
        $userData['status'] = $user['status'] ?? '';
    }
    
    if (in_array('last_login', $availableFields)) {
        $userData['last_login'] = $user['last_login'] ? 
            date('d/m/Y H:i:s', strtotime($user['last_login'])) : '';
    }
    
    if (in_array('created_date', $availableFields)) {
        $userData['created_date'] = $user['created_date'] ? 
            date('d/m/Y H:i:s', strtotime($user['created_date'])) : '';
    }
    
    if (in_array('updated_date', $availableFields)) {
        $userData['updated_date'] = $user['updated_date'] ? 
            date('d/m/Y H:i:s', strtotime($user['updated_date'])) : '';
    }
    
    echo json_encode([
        'success' => true,
        'user' => $userData,
        'available_fields' => $availableFields
    ]);
    
} catch (Exception $e) {
    error_log("Get profile data error: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'เกิดข้อผิดพลาดในการดึงข้อมูลโปรไฟล์'
    ]);
}
?>
