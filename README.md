# ระบบจัดการ Asset (Asset Management System)

ระบบจัดการ Asset ที่พัฒนาด้วย PHP และ MySQL สำหรับจัดเก็บและติดตามข้อมูล Asset ต่างๆ ในองค์กร

## ฟีเจอร์หลัก

### การจัดการ Asset
- เพิ่ม/แก้ไข/ลบ Asset
- ค้นหาและกรองข้อมูล Asset
- ดูรายละเอียด Asset แบบครบถ้วน

### ข้อมูลที่เก็บ
- **Type** - ประเภทของ Asset
- **Brand** - ยี่ห้อ
- **Model** - รุ่น
- **Tag** - แท็กสำหรับระบุ
- **Department** - แผนก
- **Status** - สถานะ (Active, Inactive, Maintenance, Disposed)
- **Hostname** - ชื่อเครื่อง
- **Operating System** - ระบบปฏิบัติการ
- **Serial Number** - หมายเลขซีเรียล
- **Asset ID** - รหัส Asset
- **Warranty Expire** - วันหมดอายุการรับประกัน
- **Description** - รายละเอียด
- **Set** - ชุดของ Asset
- **วันที่เพิ่ม** - วันที่สร้างข้อมูล
- **คนเพิ่ม** - ผู้สร้างข้อมูล
- **วันที่แก้ไข** - วันที่แก้ไขล่าสุด
- **คนแก้ไข** - ผู้แก้ไขล่าสุด

### ระบบ Logging
- บันทึกการเปลี่ยนแปลงทุกครั้ง (CREATE, UPDATE, DELETE)
- ติดตามการเปลี่ยนแปลงแต่ละ field
- แสดงประวัติการเปลี่ยนแปลงแบบละเอียด

## การติดตั้ง

### ความต้องการของระบบ
- XAMPP (Apache, MySQL, PHP 7.4+)
- Web Browser

### ขั้นตอนการติดตั้ง

1. **คัดลอกไฟล์**
   ```bash
   # คัดลอกโฟลเดอร์ asset ไปยัง htdocs ของ XAMPP
   # เช่น: C:\xampp\htdocs\asset\
   ```

2. **เริ่มต้น XAMPP**
   - เปิด XAMPP Control Panel
   - Start Apache และ MySQL

3. **สร้างฐานข้อมูล**
   - เปิด phpMyAdmin (http://localhost/phpmyadmin)
   - Import ไฟล์ `sql/setup.sql` หรือรันคำสั่ง SQL ในไฟล์

4. **ตั้งค่าการเชื่อมต่อฐานข้อมูล**
   - แก้ไขไฟล์ `config/database.php` ตามการตั้งค่าของคุณ
   ```php
   private $host = 'localhost';
   private $db_name = 'asset_management';
   private $username = 'root';
   private $password = '';
   ```

5. **เข้าใช้งานระบบ**
   - เปิด Web Browser ไปที่ http://localhost/asset

## การใช้งาน

### หน้าหลัก (index.php)
- แสดงรายการ Asset ทั้งหมด
- ค้นหาและกรองข้อมูล
- เข้าถึงฟังก์ชันต่างๆ

### เพิ่ม Asset (add_asset.php)
- กรอกข้อมูล Asset ใหม่
- ข้อมูลที่จำเป็น: Type และ Asset ID

### แก้ไข Asset (edit_asset.php)
- แก้ไขข้อมูล Asset ที่มีอยู่
- ระบบจะบันทึก log การเปลี่ยนแปลงอัตโนมัติ

### ดู Asset (view_asset.php)
- ดูรายละเอียด Asset แบบครบถ้วน
- ดูประวัติการเปลี่ยนแปลง (Logs)

### ลบ Asset (delete_asset.php)
- ลบ Asset พร้อมการยืนยัน
- บันทึก log การลบ

## โครงสร้างไฟล์

```
asset/
├── config/
│   └── database.php          # การตั้งค่าฐานข้อมูล
├── includes/
│   └── functions.php         # ฟังก์ชันหลักของระบบ
├── assets/
│   └── style.css            # CSS สำหรับตกแต่ง
├── sql/
│   └── setup.sql            # SQL สำหรับสร้างฐานข้อมูล
├── index.php                # หน้าหลัก
├── add_asset.php           # หน้าเพิ่ม Asset
├── edit_asset.php          # หน้าแก้ไข Asset
├── view_asset.php          # หน้าดู Asset
├── delete_asset.php        # หน้าลบ Asset
└── README.md               # คู่มือการใช้งาน
```

## ฐานข้อมูล

### ตาราง assets
เก็บข้อมูล Asset หลัก

### ตาราง asset_logs
เก็บ log การเปลี่ยนแปลง

### ตาราง users
เก็บข้อมูลผู้ใช้ (เบื้องต้น)

## การปรับแต่ง

### เพิ่ม Status ใหม่
แก้ไขใน `sql/setup.sql` และ form ต่างๆ:
```sql
status ENUM('Active', 'Inactive', 'Maintenance', 'Disposed', 'NewStatus')
```

### เพิ่ม Field ใหม่
1. เพิ่ม column ในตาราง assets
2. แก้ไข functions.php
3. แก้ไข form ในหน้าต่างๆ

### เปลี่ยนธีม
แก้ไขไฟล์ `assets/style.css`

## การแก้ไขปัญหา

### ไม่สามารถเชื่อมต่อฐานข้อมูลได้
- ตรวจสอบการตั้งค่าใน `config/database.php`
- ตรวจสอบว่า MySQL ทำงานอยู่

### ข้อผิดพลาด SQL
- ตรวจสอบว่าได้ import `sql/setup.sql` แล้ว
- ตรวจสอบชื่อฐานข้อมูลและตาราง

### หน้าเว็บไม่แสดงผล
- ตรวจสอบว่า Apache ทำงานอยู่
- ตรวจสอบ path ของไฟล์

## การพัฒนาต่อ

### ฟีเจอร์ที่แนะนำ
- ระบบ Authentication
- การ Export ข้อมูล (Excel, PDF)
- การ Upload รูปภาพ Asset
- ระบบแจ้งเตือนการหมดอายุการรับประกัน
- Dashboard และ Reports
- API สำหรับ Mobile App

### การปรับปรุงความปลอดภัย
- เพิ่ม Input Validation
- ป้องกัน SQL Injection
- เพิ่ม CSRF Protection
- ระบบ Session Management

## ผู้พัฒนา

ระบบนี้พัฒนาโดย Augment Agent สำหรับการจัดการ Asset ในองค์กร

## License

MIT License - สามารถใช้งานและแก้ไขได้อย่างอิสระ
