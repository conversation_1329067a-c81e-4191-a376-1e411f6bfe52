<?php
// ตรวจสอบโครงสร้างตาราง users
require_once 'config/database.php';

echo "<h2>ตรวจสอบโครงสร้างตาราง users</h2>";

try {
    // ตรวจสอบว่าตาราง users มีอยู่หรือไม่
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: red;'>❌ ไม่พบตาราง users</p>";
        echo "<p>กรุณาสร้างตาราง users ก่อน</p>";
        exit;
    }
    
    echo "<p style='color: green;'>✅ พบตาราง users</p>";
    
    // แสดงโครงสร้างตาราง
    echo "<h3>โครงสร้างตาราง users:</h3>";
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 10px; text-align: left;'>Field</th>";
    echo "<th style='padding: 10px; text-align: left;'>Type</th>";
    echo "<th style='padding: 10px; text-align: left;'>Null</th>";
    echo "<th style='padding: 10px; text-align: left;'>Key</th>";
    echo "<th style='padding: 10px; text-align: left;'>Default</th>";
    echo "<th style='padding: 10px; text-align: left;'>Extra</th>";
    echo "</tr>";
    
    $availableFields = [];
    foreach ($columns as $column) {
        $availableFields[] = $column['Field'];
        echo "<tr>";
        echo "<td style='padding: 8px; font-weight: bold;'>{$column['Field']}</td>";
        echo "<td style='padding: 8px;'>{$column['Type']}</td>";
        echo "<td style='padding: 8px;'>{$column['Null']}</td>";
        echo "<td style='padding: 8px;'>{$column['Key']}</td>";
        echo "<td style='padding: 8px;'>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "<td style='padding: 8px;'>{$column['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // ตรวจสอบ fields ที่จำเป็นสำหรับ register
    echo "<h3>ตรวจสอบ fields ที่จำเป็นสำหรับ register:</h3>";
    $requiredFields = [
        'id' => 'Primary key (auto increment)',
        'username' => 'Username สำหรับล็อกอิน',
        'password' => 'รหัสผ่าน (hashed)',
        'full_name' => 'ชื่อ-นามสกุล',
        'email' => 'อีเมล',
        'role' => 'บทบาท (Admin/User)',
        'status' => 'สถานะ (Active/Inactive)',
        'created_date' => 'วันที่สร้าง',
        'updated_date' => 'วันที่แก้ไขล่าสุด',
        'last_login' => 'เวลาล็อกอินล่าสุด'
    ];
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 10px;'>Field</th>";
    echo "<th style='padding: 10px;'>Description</th>";
    echo "<th style='padding: 10px;'>Status</th>";
    echo "<th style='padding: 10px;'>Required for Register</th>";
    echo "</tr>";
    
    foreach ($requiredFields as $field => $description) {
        $exists = in_array($field, $availableFields);
        $icon = $exists ? '✅' : '❌';
        $required = in_array($field, ['username', 'password', 'full_name']) ? 'Yes' : 'No';
        
        echo "<tr>";
        echo "<td style='padding: 8px; font-weight: bold;'>$field</td>";
        echo "<td style='padding: 8px;'>$description</td>";
        echo "<td style='padding: 8px;'>$icon " . ($exists ? 'มี' : 'ไม่มี') . "</td>";
        echo "<td style='padding: 8px;'>$required</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // แสดงข้อมูลตัวอย่างในตาราง (ถ้ามี)
    echo "<h3>ข้อมูลตัวอย่างในตาราง users:</h3>";
    $stmt = $pdo->query("SELECT COUNT(*) FROM users");
    $userCount = $stmt->fetchColumn();
    
    if ($userCount > 0) {
        echo "<p>พบผู้ใช้ในระบบ: $userCount คน</p>";
        
        // แสดงผู้ใช้ 5 คนแรก
        $selectFields = array_intersect(['id', 'username', 'full_name', 'email', 'role', 'status', 'created_date'], $availableFields);
        $sql = "SELECT " . implode(', ', $selectFields) . " FROM users LIMIT 5";
        $stmt = $pdo->query($sql);
        $users = $stmt->fetchAll();
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        foreach ($selectFields as $field) {
            echo "<th style='padding: 8px;'>$field</th>";
        }
        echo "</tr>";
        
        foreach ($users as $user) {
            echo "<tr>";
            foreach ($selectFields as $field) {
                $value = $user[$field] ?? 'NULL';
                if ($field === 'password') {
                    $value = '***hidden***';
                }
                echo "<td style='padding: 8px;'>$value</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ ไม่มีข้อมูลผู้ใช้ในตาราง</p>";
    }
    
    // สร้างโค้ด register.php ที่เหมาะสม
    echo "<h3>แนะนำการแก้ไข register.php:</h3>";
    
    $hasPassword = in_array('password', $availableFields);
    $hasFullName = in_array('full_name', $availableFields);
    $hasEmail = in_array('email', $availableFields);
    $hasRole = in_array('role', $availableFields);
    $hasStatus = in_array('status', $availableFields);
    
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h4>ฟิลด์ที่ควรมีในฟอร์ม register:</h4>";
    echo "<ul>";
    echo "<li><strong>username</strong> - จำเป็น (unique)</li>";
    if ($hasPassword) echo "<li><strong>password</strong> - จำเป็น (จะถูก hash)</li>";
    if ($hasFullName) echo "<li><strong>full_name</strong> - จำเป็น</li>";
    if ($hasEmail) echo "<li><strong>email</strong> - ไม่บังคับ</li>";
    if ($hasRole) echo "<li><strong>role</strong> - ซ่อนไว้ (default: User)</li>";
    if ($hasStatus) echo "<li><strong>status</strong> - ซ่อนไว้ (default: Active)</li>";
    echo "</ul>";
    echo "</div>";
    
    // แสดงปัญหาที่พบ (ถ้ามี)
    $missingFields = [];
    if (!in_array('username', $availableFields)) $missingFields[] = 'username';
    if (!in_array('password', $availableFields)) $missingFields[] = 'password';
    
    if (!empty($missingFields)) {
        echo "<div style='background: #f8d7da; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #f5c6cb;'>";
        echo "<h4 style='color: #721c24;'>⚠️ ปัญหาที่พบ:</h4>";
        echo "<p style='color: #721c24;'>ขาดฟิลด์ที่จำเป็น: " . implode(', ', $missingFields) . "</p>";
        echo "<p style='color: #721c24;'>กรุณาเพิ่มฟิลด์เหล่านี้ในตาราง users ก่อนใช้งาน register</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #c3e6cb;'>";
        echo "<h4 style='color: #155724;'>✅ โครงสร้างตารางพร้อมใช้งาน</h4>";
        echo "<p style='color: #155724;'>ตาราง users มีฟิลด์ที่จำเป็นครบถ้วนสำหรับระบบ register</p>";
        echo "</div>";
    }
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ เกิดข้อผิดพลาด</h3>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<p style='color: red; margin-top: 20px;'><strong>หมายเหตุ:</strong> กรุณาลบไฟล์ check_users_structure.php หลังจากตรวจสอบเสร็จแล้ว</p>";
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ตรวจสอบโครงสร้างตาราง users</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        table {
            font-size: 0.9rem;
        }
        th {
            background-color: #e9ecef !important;
        }
    </style>
</head>
<body>
    <!-- เนื้อหาจะแสดงจาก PHP ด้านบน -->
</body>
</html>
