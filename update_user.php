<?php
require_once 'includes/auth.php';

// ตั้งค่า timezone
date_default_timezone_set('Asia/Bangkok');

// Set content type
header('Content-Type: application/json; charset=utf-8');

// ตรวจสอบการล็อกอินและสิทธิ์ Admin
if (!$auth->isLoggedIn() || !isAdmin()) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'ไม่มีสิทธิ์เข้าถึง'], JSON_UNESCAPED_UNICODE);
    exit;
}

// ตรวจสอบ method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method ไม่ถูกต้อง'], JSON_UNESCAPED_UNICODE);
    exit;
}

// ตรวจสอบข้อมูลที่ส่งมา
if (!isset($_POST['user_id']) || !is_numeric($_POST['user_id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'ID ผู้ใช้ไม่ถูกต้อง'], JSON_UNESCAPED_UNICODE);
    exit;
}

$userId = (int)$_POST['user_id'];
$username = trim($_POST['username'] ?? '');
$fullName = trim($_POST['full_name'] ?? '');
$email = trim($_POST['email'] ?? '');
$role = $_POST['role'] ?? 'User';
$status = $_POST['status'] ?? 'Active';
$password = $_POST['password'] ?? '';

// ตรวจสอบข้อมูล
if (empty($username)) {
    echo json_encode(['success' => false, 'message' => 'กรุณากรอก Username'], JSON_UNESCAPED_UNICODE);
    exit;
}

if (strlen($username) < 3) {
    echo json_encode(['success' => false, 'message' => 'Username ต้องมีอย่างน้อย 3 ตัวอักษร'], JSON_UNESCAPED_UNICODE);
    exit;
}

if (empty($fullName)) {
    echo json_encode(['success' => false, 'message' => 'กรุณากรอกชื่อ-นามสกุล'], JSON_UNESCAPED_UNICODE);
    exit;
}

if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
    echo json_encode(['success' => false, 'message' => 'รูปแบบ Email ไม่ถูกต้อง'], JSON_UNESCAPED_UNICODE);
    exit;
}

if (!empty($password) && strlen($password) < 6) {
    echo json_encode(['success' => false, 'message' => 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร'], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // ตรวจสอบว่าผู้ใช้มีอยู่จริง
    $stmt = $pdo->prepare("SELECT id FROM users WHERE id = ?");
    $stmt->execute([$userId]);
    if (!$stmt->fetch()) {
        echo json_encode(['success' => false, 'message' => 'ไม่พบผู้ใช้']);
        exit;
    }
    
    // ตรวจสอบ username ซ้ำ (ยกเว้นตัวเอง)
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = ? AND id != ?");
    $stmt->execute([$username, $userId]);
    
    if ($stmt->fetchColumn() > 0) {
        echo json_encode(['success' => false, 'message' => 'Username นี้มีอยู่แล้ว']);
        exit;
    }
    
    // สร้าง SQL update
    $setParts = [
        'username = ?',
        'full_name = ?',
        'email = ?',
        'role = ?',
        'status = ?',
        'updated_date = ?'
    ];
    $params = [$username, $fullName, $email, $role, $status, date('Y-m-d H:i:s')];
    
    // เพิ่มรหัสผ่าน (ถ้ามีการกรอก)
    if (!empty($password)) {
        $setParts[] = 'password = ?';
        $params[] = password_hash($password, PASSWORD_DEFAULT);
    }
    
    $params[] = $userId; // สำหรับ WHERE clause
    
    $sql = "UPDATE users SET " . implode(', ', $setParts) . " WHERE id = ?";
    $stmt = $pdo->prepare($sql);
    $result = $stmt->execute($params);

    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => 'อัพเดทข้อมูลผู้ใช้สำเร็จ'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'เกิดข้อผิดพลาดในการอัพเดทข้อมูล'
        ]);
    }
    
} catch (PDOException $e) {
    error_log("Update user error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'เกิดข้อผิดพลาดในการอัพเดทข้อมูล'
    ]);
}
?>
