<?php
// ทดสอบการเพิ่ม Asset แบบง่าย - ไม่ใช้ PHP CLI
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🧪 ทดสอบการเพิ่ม Asset แบบง่าย</h2>";

// เริ่ม session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// ตั้งค่า session
if (!isset($_SESSION['username'])) {
    $_SESSION['username'] = 'admin';
    $_SESSION['role'] = 'Admin';
    $_SESSION['user_id'] = 1;
    echo "<p style='color: blue;'>ℹ️ ตั้งค่า session: admin/Admin</p>";
}

// สร้าง function getCurrentUsername
if (!function_exists('getCurrentUsername')) {
    function getCurrentUsername() {
        return $_SESSION['username'] ?? 'admin';
    }
}

// ตรวจสอบการเชื่อมต่อฐานข้อมูล
echo "<h3>1. ตรวจสอบการเชื่อมต่อฐานข้อมูล:</h3>";
try {
    $pdo = new PDO("mysql:host=localhost;dbname=asset_management;charset=utf8", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✅ เชื่อมต่อฐานข้อมูลสำเร็จ</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ ไม่สามารถเชื่อมต่อฐานข้อมูล: " . $e->getMessage() . "</p>";
    die();
}

// ตรวจสอบตาราง assets
echo "<h3>2. ตรวจสอบตาราง assets:</h3>";
try {
    $stmt = $pdo->query("DESCRIBE assets");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "<p style='color: green;'>✅ ตาราง assets มีอยู่</p>";
    echo "<p><strong>คอลัมน์:</strong> " . implode(', ', $columns) . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ ตาราง assets ไม่มี: " . $e->getMessage() . "</p>";
    die();
}

// ทดสอบการเพิ่ม Asset
echo "<h3>3. ทดสอบการเพิ่ม Asset:</h3>";
if ($_POST && isset($_POST['add_asset'])) {
    try {
        // รับข้อมูลจากฟอร์ม
        $data = [
            'type' => trim($_POST['type'] ?? ''),
            'brand' => trim($_POST['brand'] ?? ''),
            'model' => trim($_POST['model'] ?? ''),
            'tag' => trim($_POST['tag'] ?? ''),
            'department' => trim($_POST['department'] ?? ''),
            'status' => trim($_POST['status'] ?? 'ใช้งาน'),
            'hostname' => trim($_POST['hostname'] ?? ''),
            'operating_system' => trim($_POST['operating_system'] ?? ''),
            'serial_number' => trim($_POST['serial_number'] ?? ''),
            'asset_id' => trim($_POST['asset_id'] ?? ''),
            'warranty_expire' => !empty($_POST['warranty_expire']) ? $_POST['warranty_expire'] : null,
            'description' => trim($_POST['description'] ?? ''),
            'set_name' => trim($_POST['set_name'] ?? ''),
            'created_by' => getCurrentUsername(),
            'updated_by' => getCurrentUsername()
        ];
        
        echo "<h4>ข้อมูลที่รับมา:</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'><th style='padding: 8px;'>Field</th><th style='padding: 8px;'>Value</th></tr>";
        foreach ($data as $key => $value) {
            echo "<tr><td style='padding: 8px;'><strong>$key</strong></td><td style='padding: 8px;'>$value</td></tr>";
        }
        echo "</table>";
        
        if (empty($data['type'])) {
            echo "<p style='color: red;'>❌ Type เป็นฟิลด์ที่จำเป็น</p>";
        } else {
            // ตรวจสอบโครงสร้างตารางก่อน
            $stmt = $pdo->query("DESCRIBE assets");
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            // สร้าง SQL แบบ dynamic ตามโครงสร้างตารางจริง
            $fields = [];
            $values = [];
            $placeholders = [];
            
            // ฟิลด์พื้นฐาน
            $basicFields = [
                'type' => $data['type'],
                'brand' => $data['brand'],
                'model' => $data['model'],
                'tag' => $data['tag'],
                'department' => $data['department'],
                'status' => $data['status'],
                'hostname' => $data['hostname'],
                'operating_system' => $data['operating_system'],
                'serial_number' => $data['serial_number'],
                'asset_id' => $data['asset_id'],
                'warranty_expire' => $data['warranty_expire'],
                'description' => $data['description'],
                'set_name' => $data['set_name']
            ];
            
            // เพิ่มฟิลด์พื้นฐาน
            foreach ($basicFields as $field => $value) {
                if (in_array($field, $columns)) {
                    $fields[] = $field;
                    $values[] = $value;
                    $placeholders[] = '?';
                }
            }
            
            // เพิ่มฟิลด์ user - ตรวจสอบชื่อที่มีอยู่
            if (in_array('created_by', $columns)) {
                $fields[] = 'created_by';
                $values[] = $data['created_by'];
                $placeholders[] = '?';
            } elseif (in_array('person_added', $columns)) {
                $fields[] = 'person_added';
                $values[] = $data['created_by'];
                $placeholders[] = '?';
            }
            
            if (in_array('updated_by', $columns)) {
                $fields[] = 'updated_by';
                $values[] = $data['updated_by'];
                $placeholders[] = '?';
            } elseif (in_array('person_modified', $columns)) {
                $fields[] = 'person_modified';
                $values[] = $data['updated_by'];
                $placeholders[] = '?';
            }
            
            // เพิ่มฟิลด์วันที่ - ตรวจสอบชื่อที่มีอยู่
            if (in_array('created_date', $columns)) {
                $fields[] = 'created_date';
                $values[] = date('Y-m-d H:i:s');
                $placeholders[] = '?';
            } elseif (in_array('date_added', $columns)) {
                $fields[] = 'date_added';
                $values[] = date('Y-m-d H:i:s');
                $placeholders[] = '?';
            }
            
            if (in_array('updated_date', $columns)) {
                $fields[] = 'updated_date';
                $values[] = date('Y-m-d H:i:s');
                $placeholders[] = '?';
            } elseif (in_array('date_modified', $columns)) {
                $fields[] = 'date_modified';
                $values[] = date('Y-m-d H:i:s');
                $placeholders[] = '?';
            }
            
            // สร้าง SQL
            $sql = "INSERT INTO assets (" . implode(', ', $fields) . ") VALUES (" . implode(', ', $placeholders) . ")";
            
            echo "<h4>SQL ที่จะใช้:</h4>";
            echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace;'>";
            echo htmlspecialchars($sql);
            echo "</div>";
            
            echo "<h4>ฟิลด์และค่าที่จะใส่:</h4>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr style='background: #f8f9fa;'><th style='padding: 8px;'>Field</th><th style='padding: 8px;'>Value</th></tr>";
            for ($i = 0; $i < count($fields); $i++) {
                echo "<tr><td style='padding: 8px;'><strong>{$fields[$i]}</strong></td><td style='padding: 8px;'>{$values[$i]}</td></tr>";
            }
            echo "</table>";
            
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute($values);
            
            if ($result) {
                $newId = $pdo->lastInsertId();
                echo "<p style='color: green;'>✅ เพิ่ม Asset สำเร็จ! ID: $newId</p>";
                
                // แสดงข้อมูลที่เพิ่ม
                $checkStmt = $pdo->prepare("SELECT * FROM assets WHERE id = ?");
                $checkStmt->execute([$newId]);
                $newAsset = $checkStmt->fetch();
                
                echo "<h4>ข้อมูล Asset ที่เพิ่มแล้ว:</h4>";
                echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
                echo "<tr style='background: #f8f9fa;'><th style='padding: 8px;'>Field</th><th style='padding: 8px;'>Value</th></tr>";
                foreach ($newAsset as $key => $value) {
                    if (!is_numeric($key)) {
                        echo "<tr><td style='padding: 8px;'><strong>$key</strong></td><td style='padding: 8px;'>$value</td></tr>";
                    }
                }
                echo "</table>";
                
                echo "<p style='color: blue;'>ℹ️ Asset ถูกเพิ่มในระบบแล้ว</p>";
                
            } else {
                echo "<p style='color: red;'>❌ ไม่สามารถเพิ่ม Asset ได้</p>";
            }
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ ข้อผิดพลาด: " . $e->getMessage() . "</p>";
        echo "<p style='color: red;'>Error Code: " . $e->getCode() . "</p>";
        echo "<p style='color: red;'>Stack Trace:</p>";
        echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>";
        echo htmlspecialchars($e->getTraceAsString());
        echo "</pre>";
    }
}

// ทดสอบการเรียก add_asset.php
echo "<h3>4. ทดสอบการเรียก add_asset.php:</h3>";
if ($_POST && isset($_POST['test_add_asset_page'])) {
    echo "<h4>กำลังทดสอบการเรียก add_asset.php...</h4>";
    
    // ใช้ file_get_contents เพื่อเรียก add_asset.php
    $url = 'http://localhost/asset/add_asset.php';
    
    // สร้าง context สำหรับ POST request
    $postData = http_build_query([
        'type' => 'Desktop',
        'brand' => 'Dell',
        'model' => 'Test Model',
        'status' => 'ใช้งาน',
        'description' => 'Test Description'
    ]);
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-type: application/x-www-form-urlencoded',
            'content' => $postData
        ]
    ]);
    
    try {
        $result = file_get_contents($url, false, $context);
        
        if ($result !== false) {
            echo "<p style='color: green;'>✅ เรียก add_asset.php สำเร็จ</p>";
            
            // ตรวจสอบผลลัพธ์
            if (strpos($result, 'เพิ่ม Asset สำเร็จ') !== false) {
                echo "<p style='color: green;'>✅ พบข้อความสำเร็จ</p>";
            } elseif (strpos($result, 'error') !== false || strpos($result, 'Error') !== false) {
                echo "<p style='color: red;'>❌ พบข้อผิดพลาด</p>";
            }
            
            // แสดงส่วนหนึ่งของผลลัพธ์
            echo "<h5>ส่วนหนึ่งของผลลัพธ์:</h5>";
            echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; max-height: 300px; overflow-y: auto;'>";
            echo htmlspecialchars(substr($result, 0, 2000));
            if (strlen($result) > 2000) {
                echo "...(ตัดทอน)";
            }
            echo "</div>";
            
        } else {
            echo "<p style='color: red;'>❌ ไม่สามารถเรียก add_asset.php ได้</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ ข้อผิดพลาด: " . $e->getMessage() . "</p>";
    }
}

?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบการเพิ่ม Asset แบบง่าย</title>
    <style>
        body {
            font-family: 'TH Sarabun New', Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h2 {
            background: #28a745;
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        h3 {
            background: #6c757d;
            color: white;
            padding: 10px;
            border-radius: 5px;
        }
        .form-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 16px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>

<div class="form-section">
    <h3>🧪 ทดสอบเพิ่ม Asset ในหน้านี้</h3>
    <form method="POST">
        <div class="form-group">
            <label for="type">Type * (จำเป็น)</label>
            <select id="type" name="type" required>
                <option value="">เลือก Type</option>
                <option value="Desktop">Desktop</option>
                <option value="Laptop">Laptop</option>
                <option value="Monitor">Monitor</option>
                <option value="All-in-one">All-in-one</option>
                <option value="Multifunction Laser Printer">Multifunction Laser Printer</option>
                <option value="Barcode Printer">Barcode Printer</option>
                <option value="Barcode Scanner">Barcode Scanner</option>
                <option value="Tablet">Tablet</option>
                <option value="UPS">UPS</option>
                <option value="Queue">Queue</option>
                <option value="IP Phone">IP Phone</option>
                <option value="Teleconference">Teleconference</option>
                <option value="Switch">Switch</option>
                <option value="Access Point">Access Point</option>
                <option value="Peripheral">Peripheral</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="brand">Brand</label>
            <select id="brand" name="brand">
                <option value="">เลือก Brand</option>
                <option value="-">-</option>
                <option value="Dell">Dell</option>
                <option value="Lenovo">Lenovo</option>
                <option value="Microsoft">Microsoft</option>
                <option value="Apple">Apple</option>
                <option value="Zebra">Zebra</option>
                <option value="HP">HP</option>
                <option value="Philips">Philips</option>
                <option value="Acer">Acer</option>
                <option value="LG">LG</option>
                <option value="Cisco">Cisco</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="model">Model</label>
            <input type="text" id="model" name="model" placeholder="ระบุรุ่น">
        </div>
        
        <div class="form-group">
            <label for="status">Status</label>
            <select id="status" name="status">
                <option value="ใช้งาน">ใช้งาน</option>
                <option value="ชำรุด">ชำรุด</option>
                <option value="สำรอง">สำรอง</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="description">Description</label>
            <textarea id="description" name="description" rows="3" placeholder="รายละเอียด"></textarea>
        </div>
        
        <div class="form-group">
            <input type="hidden" name="add_asset" value="1">
            <button type="submit">🧪 ทดสอบเพิ่ม Asset</button>
        </div>
    </form>
</div>

<div class="form-section">
    <h3>🔗 ทดสอบเรียก add_asset.php</h3>
    <form method="POST">
        <input type="hidden" name="test_add_asset_page" value="1">
        <button type="submit">🔗 ทดสอบเรียก add_asset.php</button>
    </form>
</div>

<div style="margin: 20px 0;">
    <a href="add_asset.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">➕ ไปหน้าเพิ่ม Asset</a>
    <a href="fix_add_asset.php" style="background: #e74c3c; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">🔧 แก้ไขปัญหา</a>
    <a href="index.php" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">🏠 หน้าหลัก</a>
</div>

</body>
</html>
