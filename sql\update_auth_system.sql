-- อัพเดทระบบ Authentication และ Authorization
-- รันไฟล์นี้ใน phpMyAdmin หรือ MySQL command line

USE asset_management;

-- ลบตาราง users เดิม (ถ้ามี)
DROP TABLE IF EXISTS users;

-- สร้างตาราง users ใหม่พร้อม Role และ Authentication
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    role ENUM('Admin', 'User') DEFAULT 'User',
    status ENUM('Active', 'Inactive') DEFAULT 'Active',
    last_login DATETIME NULL,
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_role (role),
    INDEX idx_status (status)
);

-- เพิ่มข้อมูลผู้ใช้ตัวอย่าง
-- Password: admin123 และ user123 (hashed ด้วย PHP password_hash)
INSERT INTO users (username, password, full_name, email, role) VALUES 
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Administrator', '<EMAIL>', 'Admin'),
('user1', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'John Doe', '<EMAIL>', 'User'),
('user2', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Jane Smith', '<EMAIL>', 'User');

-- แสดงข้อมูลผู้ใช้ที่สร้างขึ้น
SELECT id, username, full_name, email, role, status, created_date FROM users ORDER BY id;
