<?php
// ตรวจสอบโครงสร้างตาราง assets
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🔍 ตรวจสอบโครงสร้างตาราง Assets</h2>";

// เชื่อมต่อฐานข้อมูลโดยตรง
try {
    $host = 'localhost';
    $dbname = 'asset_management';
    $username = 'root';
    $password = '';
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p style='color: green;'>✅ Database connected successfully</p>";
    
} catch (Exception $e) {
    die("<p style='color: red;'>❌ Database connection failed: " . $e->getMessage() . "</p>");
}

// ตรวจสอบว่าตาราง assets มีอยู่หรือไม่
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'assets'");
    $tableExists = $stmt->rowCount() > 0;
    
    if ($tableExists) {
        echo "<p style='color: green;'>✅ Table 'assets' exists</p>";
    } else {
        echo "<p style='color: red;'>❌ Table 'assets' does not exist</p>";
        echo "<p>Creating table...</p>";
        
        // สร้างตาราง assets
        $createTableSQL = "
        CREATE TABLE assets (
            id INT AUTO_INCREMENT PRIMARY KEY,
            type VARCHAR(100),
            brand VARCHAR(100),
            model VARCHAR(100),
            tag VARCHAR(100),
            department VARCHAR(100),
            status VARCHAR(50) DEFAULT 'ใช้งาน',
            hostname VARCHAR(100),
            operating_system VARCHAR(100),
            serial_number VARCHAR(100),
            asset_id VARCHAR(100),
            warranty_expire DATE,
            description TEXT,
            set_name VARCHAR(100),
            person_added VARCHAR(100),
            person_modified VARCHAR(100),
            date_added DATETIME DEFAULT CURRENT_TIMESTAMP,
            date_modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $pdo->exec($createTableSQL);
        echo "<p style='color: green;'>✅ Table 'assets' created successfully</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error checking/creating table: " . $e->getMessage() . "</p>";
}

// แสดงโครงสร้างตาราง
try {
    echo "<h3>โครงสร้างตาราง assets:</h3>";
    $stmt = $pdo->query("DESCRIBE assets");
    $columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 8px;'>Field</th>";
    echo "<th style='padding: 8px;'>Type</th>";
    echo "<th style='padding: 8px;'>Null</th>";
    echo "<th style='padding: 8px;'>Key</th>";
    echo "<th style='padding: 8px;'>Default</th>";
    echo "<th style='padding: 8px;'>Extra</th>";
    echo "</tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td style='padding: 8px;'>{$column['Field']}</td>";
        echo "<td style='padding: 8px;'>{$column['Type']}</td>";
        echo "<td style='padding: 8px;'>{$column['Null']}</td>";
        echo "<td style='padding: 8px;'>{$column['Key']}</td>";
        echo "<td style='padding: 8px;'>{$column['Default']}</td>";
        echo "<td style='padding: 8px;'>{$column['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // นับจำนวน records
    $stmt = $pdo->query("SELECT COUNT(*) FROM assets");
    $count = $stmt->fetchColumn();
    echo "<p>จำนวน records ในตาราง: <strong>$count</strong></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error getting table structure: " . $e->getMessage() . "</p>";
}

// ทดสอบ INSERT แบบง่าย
if ($_POST && isset($_POST['test_insert'])) {
    echo "<h3>🧪 ทดสอบ INSERT:</h3>";
    
    try {
        // ทดสอบ INSERT เฉพาะ field ที่จำเป็น
        $sql = "INSERT INTO assets (type, status, date_added) VALUES (?, ?, NOW())";
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute(['Desktop', 'ใช้งาน']);
        
        if ($result) {
            $newId = $pdo->lastInsertId();
            echo "<p style='color: green;'>✅ INSERT successful! New ID: $newId</p>";
            
            // แสดงข้อมูลที่เพิ่ม
            $checkStmt = $pdo->prepare("SELECT * FROM assets WHERE id = ?");
            $checkStmt->execute([$newId]);
            $newRecord = $checkStmt->fetch();
            
            echo "<h4>ข้อมูลที่เพิ่ม:</h4>";
            echo "<pre>" . print_r($newRecord, true) . "</pre>";
            
            // ลบทันที
            $deleteStmt = $pdo->prepare("DELETE FROM assets WHERE id = ?");
            $deleteStmt->execute([$newId]);
            echo "<p style='color: blue;'>🗑️ Test record deleted</p>";
            
        } else {
            echo "<p style='color: red;'>❌ INSERT failed</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ INSERT error: " . $e->getMessage() . "</p>";
    }
}

// ทดสอบ INSERT ครบถ้วน
if ($_POST && isset($_POST['test_full_insert'])) {
    echo "<h3>🧪 ทดสอบ INSERT ครบถ้วน:</h3>";
    
    try {
        $sql = "INSERT INTO assets (type, brand, model, tag, department, status, hostname, operating_system, serial_number, asset_id, warranty_expire, description, set_name, person_added, person_modified, date_added, date_modified) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";
        
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([
            'Desktop',
            'Dell',
            'OptiPlex 7090',
            'TAG-001',
            'IT Department',
            'ใช้งาน',
            'PC-001',
            'Windows 10',
            'SN123456789',
            'ASSET-001',
            '2025-12-31',
            'Test Asset Description',
            'Set A',
            'admin',
            'admin'
        ]);
        
        if ($result) {
            $newId = $pdo->lastInsertId();
            echo "<p style='color: green;'>✅ Full INSERT successful! New ID: $newId</p>";
            
            // แสดงข้อมูลที่เพิ่ม
            $checkStmt = $pdo->prepare("SELECT * FROM assets WHERE id = ?");
            $checkStmt->execute([$newId]);
            $newRecord = $checkStmt->fetch();
            
            echo "<h4>ข้อมูลที่เพิ่มครบถ้วน:</h4>";
            echo "<pre>" . print_r($newRecord, true) . "</pre>";
            
            // ลบทันที
            $deleteStmt = $pdo->prepare("DELETE FROM assets WHERE id = ?");
            $deleteStmt->execute([$newId]);
            echo "<p style='color: blue;'>🗑️ Test record deleted</p>";
            
        } else {
            echo "<p style='color: red;'>❌ Full INSERT failed</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Full INSERT error: " . $e->getMessage() . "</p>";
        echo "<p style='color: red;'>Error Code: " . $e->getCode() . "</p>";
    }
}

?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ตรวจสอบโครงสร้างตาราง Assets</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h2 {
            background: #17a2b8;
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        button {
            background: #28a745;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background: #218838;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            border: 1px solid #e9ecef;
        }
    </style>
</head>
<body>

<div class="test-section">
    <h3>🧪 ทดสอบ INSERT แบบง่าย</h3>
    <p>ทดสอบ INSERT เฉพาะ field ที่จำเป็น</p>
    <form method="POST">
        <input type="hidden" name="test_insert" value="1">
        <button type="submit">ทดสอบ Simple INSERT</button>
    </form>
</div>

<div class="test-section">
    <h3>🧪 ทดสอบ INSERT ครบถ้วน</h3>
    <p>ทดสอบ INSERT ทุก field</p>
    <form method="POST">
        <input type="hidden" name="test_full_insert" value="1">
        <button type="submit">ทดสอบ Full INSERT</button>
    </form>
</div>

<div style="margin: 20px 0;">
    <a href="add_asset.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">➕ ไปหน้าเพิ่ม Asset</a>
    <a href="index.php" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">🏠 กลับหน้าหลัก</a>
</div>

</body>
</html>
