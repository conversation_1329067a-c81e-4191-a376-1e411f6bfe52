<?php
require_once 'includes/auth.php';

// ถ้าล็อกอินแล้วให้ไปหน้าหลัก
if ($auth->isLoggedIn()) {
    header('Location: index.php');
    exit;
}

$error = '';

// ประมวลผลการล็อกอิน
if ($_POST) {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    if (empty($username) || empty($password)) {
        $error = 'กรุณากรอก Username และ Password';
    } else {
        if ($auth->login($username, $password)) {
            header('Location: index.php');
            exit;
        } else {
            $error = 'Username หรือ Password ไม่ถูกต้อง';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>เข้าสู่ระบบ - Asset Management System</title>
    <link rel="stylesheet" href="assets/style.css">
    <style>
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .login-card {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }
        
        .login-header {
            margin-bottom: 30px;
        }
        
        .login-header h1 {
            color: #2d3748;
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .login-header p {
            color: #718096;
            font-size: 1rem;
        }
        
        .login-form .form-group {
            margin-bottom: 20px;
            text-align: left;
        }
        
        .login-form .form-control {
            padding: 15px;
            font-size: 1rem;
            border-radius: 8px;
        }
        
        .login-btn {
            width: 100%;
            padding: 15px;
            font-size: 1rem;
            font-weight: 600;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }
        
        .error-message {
            background: #fed7d7;
            color: #c53030;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 0.9rem;
        }
        

    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <h1>เข้าสู่ระบบ</h1>
                <p>Asset Management System</p>
            </div>
            
            <?php if ($error): ?>
                <div class="error-message">
                    <?= htmlspecialchars($error) ?>
                </div>
            <?php endif; ?>
            
            <form method="POST" action="" class="login-form">
                <div class="form-group">
                    <label for="username">Username</label>
                    <input type="text" id="username" name="username" class="form-control" 
                           placeholder="กรอก Username" required autofocus
                           value="<?= htmlspecialchars($_POST['username'] ?? '') ?>">
                </div>
                
                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" name="password" class="form-control" 
                           placeholder="กรอก Password" required>
                </div>
                
                <button type="submit" class="login-btn">เข้าสู่ระบบ</button>
            </form>

            <div class="register-link" style="text-align: center; margin: 20px 0;">
                ยังไม่มีบัญชี? <a href="register.php" style="color: #667eea; text-decoration: none; font-weight: 600;">ลงทะเบียน</a>
            </div>


        </div>
    </div>
    
    <script>
        // Auto focus on username field
        document.getElementById('username').focus();
        

    </script>
</body>
</html>
