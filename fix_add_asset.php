<?php
// แก้ไขปัญหาการเพิ่ม Asset
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🔧 แก้ไขปัญหาการเพิ่ม Asset</h2>";

// ตรวจสอบและแก้ไขการเชื่อมต่อฐานข้อมูล
echo "<h3>1. ตรวจสอบการเชื่อมต่อฐานข้อมูล:</h3>";
try {
    $pdo = new PDO("mysql:host=localhost;dbname=asset_management;charset=utf8", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✅ เชื่อมต่อฐานข้อมูลสำเร็จ</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ ไม่สามารถเชื่อมต่อฐานข้อมูล: " . $e->getMessage() . "</p>";
    
    // ลองสร้างฐานข้อมูล
    try {
        $pdo_create = new PDO("mysql:host=localhost;charset=utf8", 'root', '');
        $pdo_create->exec("CREATE DATABASE IF NOT EXISTS asset_management");
        echo "<p style='color: blue;'>ℹ️ สร้างฐานข้อมูล asset_management</p>";
        
        // เชื่อมต่อใหม่
        $pdo = new PDO("mysql:host=localhost;dbname=asset_management;charset=utf8", 'root', '');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        echo "<p style='color: green;'>✅ เชื่อมต่อฐานข้อมูลสำเร็จหลังสร้างใหม่</p>";
    } catch (Exception $e2) {
        echo "<p style='color: red;'>❌ ไม่สามารถสร้างฐานข้อมูล: " . $e2->getMessage() . "</p>";
        die();
    }
}

// ตรวจสอบและสร้างตาราง assets
echo "<h3>2. ตรวจสอบตาราง assets:</h3>";
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'assets'");
    $tableExists = $stmt->rowCount() > 0;
    
    if ($tableExists) {
        echo "<p style='color: green;'>✅ ตาราง assets มีอยู่</p>";
        
        // ตรวจสอบโครงสร้าง
        $stmt = $pdo->query("DESCRIBE assets");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "<p><strong>คอลัมน์ที่มีอยู่:</strong> " . implode(', ', $columns) . "</p>";
        
    } else {
        echo "<p style='color: red;'>❌ ตาราง assets ไม่มี - กำลังสร้าง...</p>";
        
        // สร้างตาราง assets
        $createSQL = "
        CREATE TABLE assets (
            id INT AUTO_INCREMENT PRIMARY KEY,
            type VARCHAR(100),
            brand VARCHAR(100),
            model VARCHAR(100),
            tag VARCHAR(100),
            department VARCHAR(100),
            status VARCHAR(50) DEFAULT 'ใช้งาน',
            hostname VARCHAR(100),
            operating_system VARCHAR(100),
            serial_number VARCHAR(100),
            asset_id VARCHAR(100),
            warranty_expire DATE,
            description TEXT,
            set_name VARCHAR(100),
            created_by VARCHAR(100),
            updated_by VARCHAR(100),
            created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $pdo->exec($createSQL);
        echo "<p style='color: green;'>✅ สร้างตาราง assets สำเร็จ</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ ข้อผิดพลาดตาราง: " . $e->getMessage() . "</p>";
}

// ตรวจสอบและสร้างตาราง users
echo "<h3>3. ตรวจสอบตาราง users:</h3>";
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    $tableExists = $stmt->rowCount() > 0;
    
    if ($tableExists) {
        echo "<p style='color: green;'>✅ ตาราง users มีอยู่</p>";
    } else {
        echo "<p style='color: red;'>❌ ตาราง users ไม่มี - กำลังสร้าง...</p>";
        
        // สร้างตาราง users
        $createUsersSQL = "
        CREATE TABLE users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            full_name VARCHAR(100),
            email VARCHAR(100),
            role ENUM('Admin', 'User') DEFAULT 'User',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $pdo->exec($createUsersSQL);
        echo "<p style='color: green;'>✅ สร้างตาราง users สำเร็จ</p>";
        
        // เพิ่มผู้ใช้ admin
        $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $insertAdmin = "INSERT INTO users (username, password, full_name, email, role) VALUES (?, ?, ?, ?, ?)";
        $stmt = $pdo->prepare($insertAdmin);
        $stmt->execute(['admin', $adminPassword, 'Administrator', '<EMAIL>', 'Admin']);
        echo "<p style='color: green;'>✅ เพิ่มผู้ใช้ admin สำเร็จ (รหัสผ่าน: admin123)</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ ข้อผิดพลาดตาราง users: " . $e->getMessage() . "</p>";
}

// ตั้งค่า session
echo "<h3>4. ตั้งค่า session:</h3>";
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION['username'])) {
    $_SESSION['username'] = 'admin';
    $_SESSION['role'] = 'Admin';
    $_SESSION['user_id'] = 1;
    echo "<p style='color: blue;'>ℹ️ ตั้งค่า session: admin/Admin</p>";
} else {
    echo "<p style='color: green;'>✅ Session มีอยู่แล้ว: " . $_SESSION['username'] . "/" . $_SESSION['role'] . "</p>";
}

// สร้าง function getCurrentUsername
if (!function_exists('getCurrentUsername')) {
    function getCurrentUsername() {
        return $_SESSION['username'] ?? 'admin';
    }
}

// ทดสอบการเพิ่ม Asset
echo "<h3>5. ทดสอบการเพิ่ม Asset:</h3>";
if ($_POST && isset($_POST['test_add'])) {
    try {
        $type = trim($_POST['type'] ?? '');
        $brand = trim($_POST['brand'] ?? '');
        $model = trim($_POST['model'] ?? '');
        $status = trim($_POST['status'] ?? 'ใช้งาน');
        
        if (empty($type)) {
            echo "<p style='color: red;'>❌ Type เป็นฟิลด์ที่จำเป็น</p>";
        } else {
            $sql = "INSERT INTO assets (type, brand, model, status, created_by, updated_by, created_date, updated_date) 
                    VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())";
            
            echo "<h4>SQL ที่ใช้:</h4>";
            echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace;'>";
            echo htmlspecialchars($sql);
            echo "</div>";
            
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([
                $type,
                $brand,
                $model,
                $status,
                getCurrentUsername(),
                getCurrentUsername()
            ]);
            
            if ($result) {
                $newId = $pdo->lastInsertId();
                echo "<p style='color: green;'>✅ เพิ่ม Asset สำเร็จ! ID: $newId</p>";
                
                // แสดงข้อมูลที่เพิ่ม
                $checkStmt = $pdo->prepare("SELECT * FROM assets WHERE id = ?");
                $checkStmt->execute([$newId]);
                $newRecord = $checkStmt->fetch();
                
                echo "<h4>ข้อมูลที่เพิ่ม:</h4>";
                echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
                echo "<tr style='background: #f8f9fa;'><th style='padding: 8px;'>Field</th><th style='padding: 8px;'>Value</th></tr>";
                foreach ($newRecord as $key => $value) {
                    if (!is_numeric($key)) {
                        echo "<tr><td style='padding: 8px;'><strong>$key</strong></td><td style='padding: 8px;'>$value</td></tr>";
                    }
                }
                echo "</table>";
                
                echo "<p style='color: blue;'>ℹ️ Asset ถูกเพิ่มในระบบแล้ว</p>";
                
            } else {
                echo "<p style='color: red;'>❌ ไม่สามารถเพิ่ม Asset ได้</p>";
            }
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ ข้อผิดพลาด: " . $e->getMessage() . "</p>";
        echo "<p style='color: red;'>Error Code: " . $e->getCode() . "</p>";
    }
}

?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>แก้ไขปัญหาการเพิ่ม Asset</title>
    <style>
        body {
            font-family: 'TH Sarabun New', Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h2 {
            background: #e74c3c;
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        h3 {
            background: #34495e;
            color: white;
            padding: 10px;
            border-radius: 5px;
        }
        .form-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 16px;
        }
        button {
            background: #27ae60;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #219a52;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>

<div class="form-section">
    <h3>🧪 ทดสอบเพิ่ม Asset</h3>
    <form method="POST">
        <div class="form-group">
            <label for="type">Type * (จำเป็น)</label>
            <select id="type" name="type" required>
                <option value="">เลือก Type</option>
                <option value="Desktop">Desktop</option>
                <option value="Laptop">Laptop</option>
                <option value="Monitor">Monitor</option>
                <option value="All-in-one">All-in-one</option>
                <option value="Multifunction Laser Printer">Multifunction Laser Printer</option>
                <option value="Barcode Printer">Barcode Printer</option>
                <option value="Barcode Scanner">Barcode Scanner</option>
                <option value="Tablet">Tablet</option>
                <option value="UPS">UPS</option>
                <option value="Queue">Queue</option>
                <option value="IP Phone">IP Phone</option>
                <option value="Teleconference">Teleconference</option>
                <option value="Switch">Switch</option>
                <option value="Access Point">Access Point</option>
                <option value="Peripheral">Peripheral</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="brand">Brand</label>
            <select id="brand" name="brand">
                <option value="">เลือก Brand</option>
                <option value="-">-</option>
                <option value="Dell">Dell</option>
                <option value="Lenovo">Lenovo</option>
                <option value="Microsoft">Microsoft</option>
                <option value="Apple">Apple</option>
                <option value="Zebra">Zebra</option>
                <option value="HP">HP</option>
                <option value="Philips">Philips</option>
                <option value="Acer">Acer</option>
                <option value="LG">LG</option>
                <option value="Cisco">Cisco</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="model">Model</label>
            <input type="text" id="model" name="model" placeholder="ระบุรุ่น">
        </div>
        
        <div class="form-group">
            <label for="status">Status</label>
            <select id="status" name="status">
                <option value="ใช้งาน">ใช้งาน</option>
                <option value="ชำรุด">ชำรุด</option>
                <option value="สำรอง">สำรอง</option>
            </select>
        </div>
        
        <div class="form-group">
            <input type="hidden" name="test_add" value="1">
            <button type="submit">🧪 ทดสอบเพิ่ม Asset</button>
        </div>
    </form>
</div>

<div style="margin: 20px 0;">
    <a href="add_asset.php" style="background: #3498db; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">➕ ไปหน้าเพิ่ม Asset</a>
    <a href="index.php" style="background: #95a5a6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">🏠 หน้าหลัก</a>
</div>

</body>
</html>
