<?php
// ทดสอบ register.php ที่อิงจากโครงสร้างฐานข้อมูล
require_once 'config/database.php';
require_once 'includes/auth.php';

echo "<h2>ทดสอบ register.php ที่อิงจากโครงสร้างฐานข้อมูล</h2>";

try {
    // ตรวจสอบโครงสร้างตาราง users
    echo "<h3>1. ตรวจสอบโครงสร้างตาราง users</h3>";
    
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: red;'>❌ ไม่พบตาราง users</p>";
        exit;
    }
    
    // ดึงรายการ columns
    $availableFields = [];
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll();
    foreach ($columns as $column) {
        $availableFields[] = $column['Field'];
    }
    
    // ตรวจสอบฟิลด์ที่จำเป็น
    $hasUsername = in_array('username', $availableFields);
    $hasPassword = in_array('password', $availableFields);
    $hasFullName = in_array('full_name', $availableFields);
    $hasEmail = in_array('email', $availableFields);
    $hasRole = in_array('role', $availableFields);
    $hasStatus = in_array('status', $availableFields);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 8px;'>Field</th>";
    echo "<th style='padding: 8px;'>Status</th>";
    echo "<th style='padding: 8px;'>Required for Register</th>";
    echo "</tr>";
    
    $fieldChecks = [
        'username' => [$hasUsername, 'Yes'],
        'password' => [$hasPassword, 'Yes'],
        'full_name' => [$hasFullName, 'No (optional)'],
        'email' => [$hasEmail, 'No (optional)'],
        'role' => [$hasRole, 'No (auto-set)'],
        'status' => [$hasStatus, 'No (auto-set)']
    ];
    
    foreach ($fieldChecks as $field => $check) {
        $icon = $check[0] ? '✅' : '❌';
        $status = $check[0] ? 'มี' : 'ไม่มี';
        echo "<tr>";
        echo "<td style='padding: 8px;'>$field</td>";
        echo "<td style='padding: 8px;'>$icon $status</td>";
        echo "<td style='padding: 8px;'>{$check[1]}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // ตรวจสอบความพร้อมใช้งาน
    if (!$hasUsername || !$hasPassword) {
        echo "<div style='background: #f8d7da; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #f5c6cb;'>";
        echo "<h4 style='color: #721c24;'>⚠️ ไม่สามารถใช้งาน register ได้</h4>";
        echo "<p style='color: #721c24;'>ตารางขาดฟิลด์ที่จำเป็น: ";
        $missing = [];
        if (!$hasUsername) $missing[] = 'username';
        if (!$hasPassword) $missing[] = 'password';
        echo implode(', ', $missing) . "</p>";
        echo "</div>";
        exit;
    }
    
    echo "<p style='color: green;'>✅ ตารางพร้อมใช้งาน register</p>";
    
    // ทดสอบการสร้างผู้ใช้ตามฟิลด์ที่มีอยู่
    echo "<h3>2. ทดสอบการสร้างผู้ใช้ตามฟิลด์ที่มีอยู่</h3>";
    
    $testUser = [
        'username' => 'testuser_' . time(),
        'password' => 'testpass123'
    ];
    
    // เพิ่มฟิลด์ที่มีอยู่
    if ($hasFullName) $testUser['full_name'] = 'Test User Full Name';
    if ($hasEmail) $testUser['email'] = '<EMAIL>';
    if ($hasRole) $testUser['role'] = 'User';
    if ($hasStatus) $testUser['status'] = 'Active';
    
    echo "<h4>ข้อมูลทดสอบ:</h4>";
    echo "<ul>";
    foreach ($testUser as $field => $value) {
        echo "<li><strong>$field:</strong> $value</li>";
    }
    echo "</ul>";
    
    $result = $auth->createUser($testUser);
    if ($result) {
        echo "<p style='color: green;'>✅ สร้างผู้ใช้สำเร็จ</p>";
        
        // ตรวจสอบข้อมูลในฐานข้อมูล
        $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ?");
        $stmt->execute([$testUser['username']]);
        $savedUser = $stmt->fetch();
        
        if ($savedUser) {
            echo "<h4>ข้อมูลที่บันทึกในฐานข้อมูล:</h4>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 8px;'>Field</th>";
            echo "<th style='padding: 8px;'>Value</th>";
            echo "</tr>";
            
            foreach ($availableFields as $field) {
                $value = $savedUser[$field] ?? 'NULL';
                if ($field === 'password') {
                    $value = '***hidden*** (length: ' . strlen($value) . ')';
                }
                echo "<tr>";
                echo "<td style='padding: 8px;'>$field</td>";
                echo "<td style='padding: 8px;'>$value</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // ทดสอบล็อกอิน
        echo "<h4>ทดสอบล็อกอิน:</h4>";
        session_start();
        $loginResult = $auth->login($testUser['username'], $testUser['password']);
        if ($loginResult) {
            echo "<p style='color: green;'>✅ ล็อกอินสำเร็จ</p>";
            echo "<ul>";
            echo "<li>User ID: " . ($_SESSION['user_id'] ?? 'N/A') . "</li>";
            echo "<li>Username: " . ($_SESSION['username'] ?? 'N/A') . "</li>";
            echo "<li>Full Name: " . ($_SESSION['full_name'] ?? 'N/A') . "</li>";
            echo "<li>Role: " . ($_SESSION['role'] ?? 'N/A') . "</li>";
            echo "</ul>";
        } else {
            echo "<p style='color: red;'>❌ ล็อกอินล้มเหลว</p>";
        }
        session_destroy();
        
        // ลบผู้ใช้ทดสอบ
        $stmt = $pdo->prepare("DELETE FROM users WHERE username = ?");
        $stmt->execute([$testUser['username']]);
        echo "<p style='color: blue;'>🗑️ ลบผู้ใช้ทดสอบแล้ว</p>";
        
    } else {
        echo "<p style='color: red;'>❌ สร้างผู้ใช้ล้มเหลว</p>";
    }
    
    // ทดสอบ Dynamic Form
    echo "<h3>3. ทดสอบ Dynamic Form</h3>";
    echo "<p>register.php จะแสดงฟิลด์ตามโครงสร้างฐานข้อมูล:</p>";
    echo "<ul>";
    echo "<li>username: แสดงเสมอ (required)</li>";
    echo "<li>password: แสดงเสมอ (required)</li>";
    if ($hasFullName) echo "<li>full_name: ✅ จะแสดง (required)</li>";
    else echo "<li>full_name: ❌ จะไม่แสดง</li>";
    if ($hasEmail) echo "<li>email: ✅ จะแสดง (optional)</li>";
    else echo "<li>email: ❌ จะไม่แสดง</li>";
    if ($hasRole) echo "<li>role: ✅ จะตั้งเป็น 'User' อัตโนมัติ</li>";
    else echo "<li>role: ❌ จะไม่ตั้งค่า</li>";
    if ($hasStatus) echo "<li>status: ✅ จะตั้งเป็น 'Active' อัตโนมัติ</li>";
    else echo "<li>status: ❌ จะไม่ตั้งค่า</li>";
    echo "</ul>";
    
    // ทดสอบ Error Handling
    echo "<h3>4. ทดสอบ Error Handling</h3>";
    
    // ทดสอบ username ซ้ำ
    $duplicateUser = [
        'username' => 'admin', // ใช้ username ที่มีอยู่แล้ว
        'password' => 'newpass123'
    ];
    if ($hasFullName) $duplicateUser['full_name'] = 'Duplicate User';
    
    $result = $auth->createUser($duplicateUser);
    if (!$result) {
        echo "<p style='color: green;'>✅ ระบบป้องกัน username ซ้ำได้ถูกต้อง</p>";
    } else {
        echo "<p style='color: red;'>❌ ระบบไม่ได้ป้องกัน username ซ้ำ</p>";
    }
    
    echo "<h3>5. สรุปผลการทดสอบ</h3>";
    echo "<p style='color: green;'>✅ register.php ที่อิงจากโครงสร้างฐานข้อมูลทำงานได้ถูกต้อง</p>";
    echo "<ul>";
    echo "<li>✅ ตรวจสอบฟิลด์ในฐานข้อมูลอัตโนมัติ</li>";
    echo "<li>✅ แสดงฟอร์มตามฟิลด์ที่มีอยู่</li>";
    echo "<li>✅ สร้างผู้ใช้ตามโครงสร้างฐานข้อมูล</li>";
    echo "<li>✅ ตั้งค่า default values อัตโนมัติ</li>";
    echo "<li>✅ แสดงข้อมูลฟิลด์ที่มีอยู่</li>";
    echo "<li>✅ Error handling ทำงานถูกต้อง</li>";
    echo "</ul>";
    
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='check_users_structure.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>ตรวจสอบโครงสร้างตาราง</a>";
    echo "<a href='register.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>ทดสอบหน้า Register</a>";
    echo "<a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>ไปหน้า Login</a>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ เกิดข้อผิดพลาด</h3>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<p style='color: red; margin-top: 20px;'><strong>หมายเหตุ:</strong> กรุณาลบไฟล์ test_new_register.php หลังจากทดสอบเสร็จแล้ว</p>";
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบ Register ใหม่</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <!-- เนื้อหาจะแสดงจาก PHP ด้านบน -->
</body>
</html>
