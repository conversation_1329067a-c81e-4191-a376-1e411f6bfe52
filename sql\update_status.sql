-- อัพเดท Status ในฐานข้อมูลให้ตรงกับตัวเลือกใหม่
-- รันไฟล์นี้ใน phpMyAdmin หรือ MySQL command line

USE asset_management;

-- อัพเดท Status ที่มีอยู่แล้วให้ตรงกับตัวเลือกใหม่
UPDATE assets SET status = 'ใช้งาน' WHERE status = 'Active';
UPDATE assets SET status = 'ชำรุด' WHERE status IN ('Maintenance', 'Inactive');
UPDATE assets SET status = 'สำรอง' WHERE status = 'Disposed';

-- แก้ไข ENUM ของ status column
ALTER TABLE assets MODIFY COLUMN status ENUM('ใช้งาน', 'ชำรุด', 'สำรอง') DEFAULT 'ใช้งาน';

-- ตรวจสอบโครงสร้างตารางหลังการแก้ไข
DESCRIBE assets;

-- แสดงสถิติ Status ทั้งหมดหลังการอัพเดท
SELECT status, COUNT(*) as count FROM assets GROUP BY status ORDER BY status;
