<?php
// ทดสอบระบบ Auth ที่แก้ไขให้ทำงานแบบ dynamic
require_once 'config/database.php';
require_once 'includes/auth.php';

echo "<h2>ทดสอบระบบ Auth แบบ Dynamic</h2>";

try {
    // ตรวจสอบโครงสร้างตาราง users
    echo "<h3>1. ตรวจสอบโครงสร้างตาราง users</h3>";
    
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: red;'>❌ ไม่พบตาราง users</p>";
        exit;
    }
    
    // ดึงรายการ columns
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll();
    
    $currentFields = [];
    foreach ($columns as $column) {
        $currentFields[] = $column['Field'];
    }
    
    echo "<p>ฟิลด์ที่มีอยู่: " . implode(', ', $currentFields) . "</p>";
    
    // ตรวจสอบฟิลด์ที่จำเป็น
    $hasUsername = in_array('username', $currentFields);
    $hasPassword = in_array('password', $currentFields);
    $hasFullName = in_array('full_name', $currentFields);
    $hasEmail = in_array('email', $currentFields);
    $hasRole = in_array('role', $currentFields);
    $hasStatus = in_array('status', $currentFields);
    $hasCreatedDate = in_array('created_date', $currentFields);
    $hasLastLogin = in_array('last_login', $currentFields);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 8px;'>Field</th>";
    echo "<th style='padding: 8px;'>Status</th>";
    echo "<th style='padding: 8px;'>Impact</th>";
    echo "</tr>";
    
    $fieldChecks = [
        'username' => [$hasUsername, 'จำเป็นสำหรับ login'],
        'password' => [$hasPassword, 'จำเป็นสำหรับ authentication'],
        'full_name' => [$hasFullName, 'แสดงชื่อผู้ใช้'],
        'email' => [$hasEmail, 'ติดต่อผู้ใช้'],
        'role' => [$hasRole, 'แยกสิทธิ์ Admin/User'],
        'status' => [$hasStatus, 'ควบคุมการใช้งาน'],
        'created_date' => [$hasCreatedDate, 'บันทึกวันที่สร้าง'],
        'last_login' => [$hasLastLogin, 'ติดตามการใช้งาน']
    ];
    
    foreach ($fieldChecks as $field => $check) {
        $icon = $check[0] ? '✅' : '❌';
        $status = $check[0] ? 'มี' : 'ไม่มี';
        echo "<tr>";
        echo "<td style='padding: 8px;'>$field</td>";
        echo "<td style='padding: 8px;'>$icon $status</td>";
        echo "<td style='padding: 8px;'>{$check[1]}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // ทดสอบการสร้าง Auth object
    echo "<h3>2. ทดสอบการสร้าง Auth object</h3>";
    
    $auth = new Auth($pdo);
    echo "<p style='color: green;'>✅ สร้าง Auth object สำเร็จ</p>";
    
    // ทดสอบการตรวจสอบฟิลด์
    echo "<h4>ทดสอบการตรวจสอบฟิลด์:</h4>";
    echo "<ul>";
    foreach ($fieldChecks as $field => $check) {
        // ใช้ reflection เพื่อเข้าถึง private method
        $reflection = new ReflectionClass($auth);
        $hasFieldMethod = $reflection->getMethod('hasField');
        $hasFieldMethod->setAccessible(true);
        
        $result = $hasFieldMethod->invoke($auth, $field);
        $icon = $result ? '✅' : '❌';
        echo "<li>$icon hasField('$field'): " . ($result ? 'true' : 'false') . "</li>";
    }
    echo "</ul>";
    
    // ทดสอบการสร้างผู้ใช้
    echo "<h3>3. ทดสอบการสร้างผู้ใช้แบบ Dynamic</h3>";
    
    $testUser = [
        'username' => 'testuser_' . time(),
        'password' => 'testpass123'
    ];
    
    // เพิ่มฟิลด์ตามที่มีอยู่
    if ($hasFullName) $testUser['full_name'] = 'Test User Dynamic';
    if ($hasEmail) $testUser['email'] = '<EMAIL>';
    if ($hasRole) $testUser['role'] = 'User';
    if ($hasStatus) $testUser['status'] = 'Active';
    
    echo "<h4>ข้อมูลทดสอบ:</h4>";
    echo "<ul>";
    foreach ($testUser as $field => $value) {
        echo "<li><strong>$field:</strong> $value</li>";
    }
    echo "</ul>";
    
    $result = $auth->createUser($testUser);
    if ($result) {
        echo "<p style='color: green;'>✅ สร้างผู้ใช้สำเร็จ</p>";
        
        // ตรวจสอบข้อมูลในฐานข้อมูล
        $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ?");
        $stmt->execute([$testUser['username']]);
        $savedUser = $stmt->fetch();
        
        if ($savedUser) {
            echo "<h4>ข้อมูลที่บันทึกในฐานข้อมูล:</h4>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 8px;'>Field</th>";
            echo "<th style='padding: 8px;'>Value</th>";
            echo "</tr>";
            
            foreach ($currentFields as $field) {
                $value = $savedUser[$field] ?? 'NULL';
                if ($field === 'password') {
                    $value = '***hidden*** (length: ' . strlen($value) . ')';
                }
                echo "<tr>";
                echo "<td style='padding: 8px;'>$field</td>";
                echo "<td style='padding: 8px;'>$value</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // ทดสอบล็อกอิน
        echo "<h4>ทดสอบล็อกอิน:</h4>";
        session_start();
        $loginResult = $auth->login($testUser['username'], $testUser['password']);
        if ($loginResult) {
            echo "<p style='color: green;'>✅ ล็อกอินสำเร็จ</p>";
            echo "<ul>";
            echo "<li>User ID: " . ($_SESSION['user_id'] ?? 'N/A') . "</li>";
            echo "<li>Username: " . ($_SESSION['username'] ?? 'N/A') . "</li>";
            echo "<li>Full Name: " . ($_SESSION['full_name'] ?? 'N/A') . "</li>";
            echo "<li>Role: " . ($_SESSION['role'] ?? 'N/A') . "</li>";
            echo "</ul>";
        } else {
            echo "<p style='color: red;'>❌ ล็อกอินล้มเหลว</p>";
        }
        session_destroy();
        
        // ทดสอบ getAllUsers
        echo "<h4>ทดสอบ getAllUsers:</h4>";
        $allUsers = $auth->getAllUsers();
        echo "<p>พบผู้ใช้ " . count($allUsers) . " คน</p>";
        
        if (!empty($allUsers)) {
            $firstUser = $allUsers[0];
            echo "<p>ฟิลด์ที่ดึงมา: " . implode(', ', array_keys($firstUser)) . "</p>";
        }
        
        // ลบผู้ใช้ทดสอบ
        $stmt = $pdo->prepare("DELETE FROM users WHERE username = ?");
        $stmt->execute([$testUser['username']]);
        echo "<p style='color: blue;'>🗑️ ลบผู้ใช้ทดสอบแล้ว</p>";
        
    } else {
        echo "<p style='color: red;'>❌ สร้างผู้ใช้ล้มเหลว</p>";
    }
    
    // ทดสอบ Error Handling
    echo "<h3>4. ทดสอบ Error Handling</h3>";
    
    // ทดสอบ username ซ้ำ
    $duplicateUser = [
        'username' => 'admin', // ใช้ username ที่มีอยู่แล้ว
        'password' => 'newpass123'
    ];
    
    $result = $auth->createUser($duplicateUser);
    if (!$result) {
        echo "<p style='color: green;'>✅ ระบบป้องกัน username ซ้ำได้ถูกต้อง</p>";
    } else {
        echo "<p style='color: red;'>❌ ระบบไม่ได้ป้องกัน username ซ้ำ</p>";
    }
    
    echo "<h3>5. สรุปผลการทดสอบ</h3>";
    echo "<p style='color: green;'>✅ ระบบ Auth แบบ Dynamic ทำงานได้ถูกต้อง</p>";
    echo "<ul>";
    echo "<li>✅ ตรวจสอบฟิลด์ในฐานข้อมูลอัตโนมัติ</li>";
    echo "<li>✅ สร้าง SQL queries ตามฟิลด์ที่มีอยู่</li>";
    echo "<li>✅ สร้างผู้ใช้ตามโครงสร้างฐานข้อมูล</li>";
    echo "<li>✅ ล็อกอินทำงานได้ถูกต้อง</li>";
    echo "<li>✅ ดึงรายการผู้ใช้ตามฟิลด์ที่มี</li>";
    echo "<li>✅ Error handling ทำงานถูกต้อง</li>";
    echo "</ul>";
    
    // แสดงคำแนะนำ
    if (!$hasPassword) {
        echo "<div style='background: #f8d7da; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #f5c6cb;'>";
        echo "<h4 style='color: #721c24;'>⚠️ คำเตือน:</h4>";
        echo "<p style='color: #721c24;'>ไม่มี password field - ระบบ login จะไม่ทำงาน</p>";
        echo "<p style='color: #721c24;'>กรุณาเพิ่ม password field ในตาราง users</p>";
        echo "</div>";
    }
    
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='check_current_structure.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>ตรวจสอบโครงสร้าง</a>";
    echo "<a href='fix_code_for_new_structure.php' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>แก้ไขโค้ด</a>";
    echo "<a href='register.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>ทดสอบ Register</a>";
    echo "<a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>ทดสอบ Login</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ เกิดข้อผิดพลาด</h3>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<p style='color: red; margin-top: 20px;'><strong>หมายเหตุ:</strong> กรุณาลบไฟล์ test_dynamic_auth.php หลังจากทดสอบเสร็จแล้ว</p>";
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบระบบ Auth แบบ Dynamic</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <!-- เนื้อหาจะแสดงจาก PHP ด้านบน -->
</body>
</html>
