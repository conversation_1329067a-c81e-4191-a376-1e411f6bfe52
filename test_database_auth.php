<?php
// ทดสอบระบบ Auth ที่อิงจากฐานข้อมูลเท่านั้น
require_once 'config/database.php';
require_once 'includes/auth.php';

echo "<h2>ทดสอบระบบ Auth ที่อิงจากฐานข้อมูลเท่านั้น</h2>";

try {
    // ตรวจสอบตาราง users และ password column
    echo "<h3>1. ตรวจสอบโครงสร้างฐานข้อมูล</h3>";
    
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: red;'>❌ ไม่พบตาราง users</p>";
        exit;
    }
    
    $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'password'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: red;'>❌ ไม่พบ password column ในตาราง users</p>";
        echo "<p>กรุณารันไฟล์ add_password_column.php ก่อน</p>";
        exit;
    }
    
    echo "<p style='color: green;'>✅ ตาราง users และ password column พร้อมใช้งาน</p>";
    
    // ตรวจสอบข้อมูลผู้ใช้
    echo "<h3>2. ตรวจสอบข้อมูลผู้ใช้</h3>";
    $stmt = $pdo->query("SELECT id, username, full_name, email, role, status, 
                         CASE WHEN password IS NULL OR password = '' THEN 'ไม่มี' ELSE 'มี' END as has_password 
                         FROM users");
    $users = $stmt->fetchAll();
    
    if (empty($users)) {
        echo "<p style='color: orange;'>⚠️ ไม่มีข้อมูลผู้ใช้</p>";
        
        // สร้างผู้ใช้ทดสอบ
        echo "<h4>สร้างผู้ใช้ทดสอบ:</h4>";
        $testUsers = [
            ['username' => 'admin', 'password' => 'admin123', 'full_name' => 'Administrator', 'email' => '<EMAIL>', 'role' => 'Admin'],
            ['username' => 'user1', 'password' => 'user123', 'full_name' => 'John Doe', 'email' => '<EMAIL>', 'role' => 'User']
        ];
        
        foreach ($testUsers as $userData) {
            $result = $auth->createUser($userData);
            if ($result) {
                echo "<p style='color: green;'>✅ สร้างผู้ใช้ {$userData['username']} สำเร็จ (password: {$userData['password']})</p>";
            } else {
                echo "<p style='color: red;'>❌ ไม่สามารถสร้างผู้ใช้ {$userData['username']} ได้</p>";
            }
        }
        
        // ดึงข้อมูลผู้ใช้ใหม่
        $stmt = $pdo->query("SELECT id, username, full_name, email, role, status, 
                             CASE WHEN password IS NULL OR password = '' THEN 'ไม่มี' ELSE 'มี' END as has_password 
                             FROM users");
        $users = $stmt->fetchAll();
    }
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 8px;'>ID</th>";
    echo "<th style='padding: 8px;'>Username</th>";
    echo "<th style='padding: 8px;'>Full Name</th>";
    echo "<th style='padding: 8px;'>Email</th>";
    echo "<th style='padding: 8px;'>Role</th>";
    echo "<th style='padding: 8px;'>Status</th>";
    echo "<th style='padding: 8px;'>Password</th>";
    echo "</tr>";
    
    foreach ($users as $user) {
        $passwordIcon = $user['has_password'] === 'มี' ? '🔒' : '❌';
        echo "<tr>";
        echo "<td style='padding: 8px;'>{$user['id']}</td>";
        echo "<td style='padding: 8px;'>{$user['username']}</td>";
        echo "<td style='padding: 8px;'>" . ($user['full_name'] ?? '-') . "</td>";
        echo "<td style='padding: 8px;'>" . ($user['email'] ?? '-') . "</td>";
        echo "<td style='padding: 8px;'>" . ($user['role'] ?? 'Admin') . "</td>";
        echo "<td style='padding: 8px;'>" . ($user['status'] ?? 'Active') . "</td>";
        echo "<td style='padding: 8px;'>$passwordIcon {$user['has_password']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // ทดสอบการล็อกอิน
    echo "<h3>3. ทดสอบการล็อกอิน (อิงจากฐานข้อมูลเท่านั้น)</h3>";
    
    $testLogins = [
        ['username' => 'admin', 'password' => 'admin123', 'expected' => true, 'note' => 'Admin account'],
        ['username' => 'user1', 'password' => 'user123', 'expected' => true, 'note' => 'User account'],
        ['username' => 'admin', 'password' => 'wrongpass', 'expected' => false, 'note' => 'Wrong password'],
        ['username' => 'nonexistent', 'password' => 'admin123', 'expected' => false, 'note' => 'Non-existent user']
    ];
    
    foreach ($testLogins as $test) {
        echo "<h4>ทดสอบ: {$test['username']} / {$test['password']} ({$test['note']})</h4>";
        
        // ล็อกเอาท์ก่อน
        if (isset($_SESSION)) {
            session_destroy();
        }
        session_start();
        
        $result = $auth->login($test['username'], $test['password']);
        
        if ($result === $test['expected']) {
            if ($result) {
                echo "<p style='color: green;'>✅ ล็อกอินสำเร็จ</p>";
                echo "<ul>";
                echo "<li>User ID: " . ($_SESSION['user_id'] ?? 'N/A') . "</li>";
                echo "<li>Username: " . ($_SESSION['username'] ?? 'N/A') . "</li>";
                echo "<li>Full Name: " . ($_SESSION['full_name'] ?? 'N/A') . "</li>";
                echo "<li>Role: " . ($_SESSION['role'] ?? 'N/A') . "</li>";
                echo "</ul>";
            } else {
                echo "<p style='color: green;'>✅ ล็อกอินล้มเหลว (ตามที่คาดหวัง)</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ ผลลัพธ์ไม่ตรงตามที่คาดหวัง</p>";
        }
        
        // ล็อกเอาท์
        if (isset($_SESSION)) {
            session_destroy();
        }
    }
    
    // ทดสอบฟังก์ชัน hasPassword
    echo "<h3>4. ทดสอบฟังก์ชัน hasPassword()</h3>";
    foreach ($users as $user) {
        $hasPassword = $auth->hasPassword($user['id']);
        $icon = $hasPassword ? '✅' : '❌';
        echo "<p>$icon User {$user['username']} (ID: {$user['id']}): " . ($hasPassword ? 'มี password' : 'ไม่มี password') . "</p>";
    }
    
    // ทดสอบการเปลี่ยนรหัสผ่าน
    echo "<h3>5. ทดสอบการเปลี่ยนรหัสผ่าน</h3>";
    if (!empty($users)) {
        $testUser = $users[0];
        echo "<h4>ทดสอบเปลี่ยนรหัสผ่านสำหรับ user: {$testUser['username']}</h4>";
        
        if ($auth->hasPassword($testUser['id'])) {
            // ทดสอบเปลี่ยนรหัสผ่านด้วยรหัสผ่านเดิมที่ถูกต้อง
            $oldPassword = $testUser['username'] === 'admin' ? 'admin123' : 'user123';
            $newPassword = 'newpass123';
            
            $changeResult = $auth->changePassword($testUser['id'], $oldPassword, $newPassword);
            if ($changeResult) {
                echo "<p style='color: green;'>✅ เปลี่ยนรหัสผ่านสำเร็จ</p>";
                
                // ทดสอบล็อกอินด้วยรหัสผ่านใหม่
                session_start();
                $loginResult = $auth->login($testUser['username'], $newPassword);
                if ($loginResult) {
                    echo "<p style='color: green;'>✅ ล็อกอินด้วยรหัสผ่านใหม่สำเร็จ</p>";
                } else {
                    echo "<p style='color: red;'>❌ ล็อกอินด้วยรหัสผ่านใหม่ล้มเหลว</p>";
                }
                session_destroy();
                
                // เปลี่ยนรหัสผ่านกลับ
                $auth->changePassword($testUser['id'], $newPassword, $oldPassword);
                echo "<p style='color: blue;'>🔄 เปลี่ยนรหัสผ่านกลับเป็นเดิม</p>";
            } else {
                echo "<p style='color: red;'>❌ เปลี่ยนรหัสผ่านล้มเหลว</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ User นี้ไม่มี password ในฐานข้อมูล</p>";
        }
    }
    
    echo "<h3>6. สรุปผลการทดสอบ</h3>";
    echo "<p style='color: green;'>✅ ระบบ Auth อิงจากฐานข้อมูลเท่านั้นทำงานได้ถูกต้อง</p>";
    echo "<ul>";
    echo "<li>✅ ล็อกอินใช้เฉพาะ password จากฐานข้อมูล</li>";
    echo "<li>✅ ไม่มี fallback password</li>";
    echo "<li>✅ ตรวจสอบรหัสผ่านด้วย password_verify()</li>";
    echo "<li>✅ เข้ารหัสรหัสผ่านด้วย password_hash()</li>";
    echo "<li>✅ ฟังก์ชัน hasPassword() ทำงานถูกต้อง</li>";
    echo "<li>✅ การเปลี่ยนรหัสผ่านทำงานถูกต้อง</li>";
    echo "</ul>";
    
    echo "<p><a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>ไปหน้าล็อกอิน</a></p>";
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ เกิดข้อผิดพลาด</h3>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<p style='color: red; margin-top: 20px;'><strong>หมายเหตุ:</strong> กรุณาลบไฟล์ test_database_auth.php หลังจากทดสอบเสร็จแล้ว</p>";
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบระบบ Auth อิงฐานข้อมูล</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <!-- เนื้อหาจะแสดงจาก PHP ด้านบน -->
</body>
</html>
