<?php
// Debug Error - แสดงข้อผิดพลาดทั้งหมด
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🔍 Debug Error Analysis</h2>";

try {
    require_once 'includes/auth.php';
    echo "<p style='color: green;'>✅ Auth loaded successfully</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Auth error: " . $e->getMessage() . "</p>";
    exit;
}

// ตรวจสอบการล็อกอิน
if (!$auth->isLoggedIn()) {
    echo "<p style='color: red;'>❌ Not logged in</p>";
    echo "<a href='login.php'>Login</a>";
    exit;
}

echo "<p style='color: green;'>✅ User logged in: " . ($_SESSION['username'] ?? 'N/A') . "</p>";

// ตรวจสอบฐานข้อมูล
try {
    $stmt = $pdo->query("SELECT 1");
    echo "<p style='color: green;'>✅ Database connection OK</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
    exit;
}

// ตรวจสอบตาราง assets
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM assets");
    $count = $stmt->fetchColumn();
    echo "<p style='color: green;'>✅ Assets table exists with $count records</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Assets table error: " . $e->getMessage() . "</p>";
}

// ตรวจสอบโครงสร้างตาราง
try {
    echo "<h3>Table Structure:</h3>";
    $stmt = $pdo->query("DESCRIBE assets");
    $columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $col) {
        echo "<tr>";
        echo "<td>{$col['Field']}</td>";
        echo "<td>{$col['Type']}</td>";
        echo "<td>{$col['Null']}</td>";
        echo "<td>{$col['Key']}</td>";
        echo "<td>{$col['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Table structure error: " . $e->getMessage() . "</p>";
}

// ทดสอบ INSERT แบบง่าย
if ($_POST && isset($_POST['test_simple'])) {
    echo "<h3>🧪 Simple INSERT Test:</h3>";
    
    try {
        $sql = "INSERT INTO assets (type) VALUES (?)";
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute(['Desktop']);
        
        if ($result) {
            $newId = $pdo->lastInsertId();
            echo "<p style='color: green;'>✅ Simple INSERT successful! ID: $newId</p>";
            
            // ลบทันที
            $deleteStmt = $pdo->prepare("DELETE FROM assets WHERE id = ?");
            $deleteStmt->execute([$newId]);
            echo "<p style='color: blue;'>🗑️ Test record deleted</p>";
        } else {
            echo "<p style='color: red;'>❌ Simple INSERT failed</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Simple INSERT error: " . $e->getMessage() . "</p>";
        echo "<p style='color: red;'>SQL State: " . $e->getCode() . "</p>";
    }
}

// ทดสอบ INSERT แบบครบถ้วน
if ($_POST && isset($_POST['test_full'])) {
    echo "<h3>🧪 Full INSERT Test:</h3>";
    
    try {
        $sql = "INSERT INTO assets (type, brand, model, tag, department, status, hostname, operating_system, serial_number, asset_id, warranty_expire, description, set_name, person_added, person_modified, date_added, date_modified) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";
        
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([
            'Desktop',
            'Dell',
            'Test Model',
            'TEST-001',
            'IT',
            'ใช้งาน',
            'TEST-PC',
            'Windows 10',
            'TEST123456',
            'ASSET-TEST-001',
            '2025-12-31',
            'Test Description',
            'Test Set',
            getCurrentUsername(),
            getCurrentUsername()
        ]);
        
        if ($result) {
            $newId = $pdo->lastInsertId();
            echo "<p style='color: green;'>✅ Full INSERT successful! ID: $newId</p>";
            
            // ลบทันที
            $deleteStmt = $pdo->prepare("DELETE FROM assets WHERE id = ?");
            $deleteStmt->execute([$newId]);
            echo "<p style='color: blue;'>🗑️ Test record deleted</p>";
        } else {
            echo "<p style='color: red;'>❌ Full INSERT failed</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Full INSERT error: " . $e->getMessage() . "</p>";
        echo "<p style='color: red;'>SQL State: " . $e->getCode() . "</p>";
    }
}

// ทดสอบ AssetManager
if ($_POST && isset($_POST['test_manager'])) {
    echo "<h3>🧪 AssetManager Test:</h3>";
    
    try {
        require_once 'includes/functions.php';
        echo "<p style='color: green;'>✅ Functions loaded</p>";
        
        $assetManager = new AssetManager($pdo);
        echo "<p style='color: green;'>✅ AssetManager created</p>";
        
        $testData = [
            'type' => 'Desktop',
            'brand' => 'Dell',
            'model' => 'Test Model',
            'tag' => 'TEST-001',
            'department' => 'IT',
            'status' => 'ใช้งาน',
            'hostname' => 'TEST-PC',
            'operating_system' => 'Windows 10',
            'serial_number' => 'TEST123456',
            'asset_id' => 'ASSET-TEST-001',
            'warranty_expire' => '2025-12-31',
            'description' => 'Test Description',
            'asset_set' => 'Test Set',
            'created_by' => getCurrentUsername(),
            'updated_by' => getCurrentUsername()
        ];
        
        echo "<p>Test data prepared:</p>";
        echo "<pre>" . print_r($testData, true) . "</pre>";
        
        $result = $assetManager->createAsset($testData);
        
        if ($result) {
            echo "<p style='color: green;'>✅ AssetManager successful! ID: $result</p>";
            
            // ลบทันที
            $assetManager->deleteAsset($result, getCurrentUsername());
            echo "<p style='color: blue;'>🗑️ Test record deleted</p>";
        } else {
            echo "<p style='color: red;'>❌ AssetManager failed (returned false)</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ AssetManager error: " . $e->getMessage() . "</p>";
        echo "<p style='color: red;'>File: " . $e->getFile() . "</p>";
        echo "<p style='color: red;'>Line: " . $e->getLine() . "</p>";
        echo "<p style='color: red;'>Trace:</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
}

// ตรวจสอบ PHP Configuration
echo "<h3>PHP Configuration:</h3>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>Error Reporting: " . error_reporting() . "</p>";
echo "<p>Display Errors: " . ini_get('display_errors') . "</p>";
echo "<p>Log Errors: " . ini_get('log_errors') . "</p>";
echo "<p>Error Log: " . ini_get('error_log') . "</p>";

?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Error Analysis</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h2 {
            background: #dc3545;
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background: #0056b3;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>

<div class="test-section">
    <h3>🧪 Simple INSERT Test</h3>
    <p>ทดสอบ INSERT เฉพาะ type field</p>
    <form method="POST">
        <input type="hidden" name="test_simple" value="1">
        <button type="submit">ทดสอบ Simple INSERT</button>
    </form>
</div>

<div class="test-section">
    <h3>🧪 Full INSERT Test</h3>
    <p>ทดสอบ INSERT ครบทุก field</p>
    <form method="POST">
        <input type="hidden" name="test_full" value="1">
        <button type="submit">ทดสอบ Full INSERT</button>
    </form>
</div>

<div class="test-section">
    <h3>🔧 AssetManager Test</h3>
    <p>ทดสอบการใช้ AssetManager class</p>
    <form method="POST">
        <input type="hidden" name="test_manager" value="1">
        <button type="submit">ทดสอบ AssetManager</button>
    </form>
</div>

<div style="margin: 20px 0;">
    <a href="add_asset.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">➕ ไปหน้าเพิ่ม Asset</a>
    <a href="index.php" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">🏠 กลับหน้าหลัก</a>
</div>

</body>
</html>
