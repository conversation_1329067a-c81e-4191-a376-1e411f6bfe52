<?php
session_start();
require_once 'includes/auth.php';

// ตรวจสอบการเข้าสู่ระบบ
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

// ตรวจสอบสิทธิ์ Admin
if (!isAdmin()) {
    header('Location: index.php');
    exit;
}

// สร้าง CSV template
$filename = 'asset_import_template.csv';

// Headers สำหรับ CSV ตามโครงสร้างฐานข้อมูล
$headers = [
    'Type',
    'Brand', 
    'Model',
    'Tag',
    'Department',
    'Status',
    'Hostname',
    'Operating System',
    'Serial Number',
    'Asset ID',
    'Warranty Expire',
    'Description',
    'Set'
];

// ตั้งค่า headers สำหรับดาวน์โหลด CSV แบบ UTF-8
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
header('Expires: 0');
header('Pragma: public');

// สร้างเนื้อหา CSV
$csvContent = '';

// เพิ่ม UTF-8 BOM เพื่อให้ Excel อ่านภาษาไทยได้ถูกต้อง
$csvContent .= "\xEF\xBB\xBF";

// สร้าง CSV headers เท่านั้น
$csvContent .= implode(',', $headers) . "\r\n";

// ส่งออกเนื้อหา CSV
echo $csvContent;
exit;
?>
