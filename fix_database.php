<?php
require_once 'config/database.php';

echo "<h2>ตรวจสอบและแก้ไขฐานข้อมูล</h2>";

try {
    // ตรวจสอบ structure ของตาราง assets
    echo "<h3>1. ตรวจสอบ structure ตาราง assets</h3>";
    $stmt = $pdo->query("DESCRIBE assets");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    $columnNames = [];
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
        $columnNames[] = $column['Field'];
    }
    echo "</table>";
    
    echo "<h3>2. ตรวจสอบ columns ที่จำเป็น</h3>";
    $requiredColumns = [
        'id', 'type', 'brand', 'model', 'tag', 'department', 'status',
        'hostname', 'operating_system', 'serial_number', 'asset_id',
        'warranty_expire', 'description', 'asset_set', 'created_date',
        'created_by', 'updated_date', 'updated_by'
    ];
    
    $missingColumns = [];
    foreach ($requiredColumns as $col) {
        if (in_array($col, $columnNames)) {
            echo "<span style='color: green;'>✅ $col - มีอยู่</span><br>";
        } else {
            echo "<span style='color: red;'>❌ $col - ไม่มี</span><br>";
            $missingColumns[] = $col;
        }
    }
    
    // เพิ่ม columns ที่ขาดหายไป
    if (!empty($missingColumns)) {
        echo "<h3>3. เพิ่ม columns ที่ขาดหายไป</h3>";
        
        foreach ($missingColumns as $col) {
            try {
                switch ($col) {
                    case 'asset_set':
                        $pdo->exec("ALTER TABLE assets ADD COLUMN asset_set VARCHAR(100) NULL");
                        echo "<span style='color: green;'>✅ เพิ่ม column asset_set สำเร็จ</span><br>";
                        break;
                    case 'created_date':
                        $pdo->exec("ALTER TABLE assets ADD COLUMN created_date DATETIME DEFAULT CURRENT_TIMESTAMP");
                        echo "<span style='color: green;'>✅ เพิ่ม column created_date สำเร็จ</span><br>";
                        break;
                    case 'updated_date':
                        $pdo->exec("ALTER TABLE assets ADD COLUMN updated_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");
                        echo "<span style='color: green;'>✅ เพิ่ม column updated_date สำเร็จ</span><br>";
                        break;
                    case 'created_by':
                        $pdo->exec("ALTER TABLE assets ADD COLUMN created_by VARCHAR(100) NULL");
                        echo "<span style='color: green;'>✅ เพิ่ม column created_by สำเร็จ</span><br>";
                        break;
                    case 'updated_by':
                        $pdo->exec("ALTER TABLE assets ADD COLUMN updated_by VARCHAR(100) NULL");
                        echo "<span style='color: green;'>✅ เพิ่ม column updated_by สำเร็จ</span><br>";
                        break;
                    default:
                        echo "<span style='color: orange;'>⚠️ ไม่ทราบวิธีเพิ่ม column $col</span><br>";
                }
            } catch (PDOException $e) {
                echo "<span style='color: red;'>❌ เกิดข้อผิดพลาดในการเพิ่ม column $col: " . $e->getMessage() . "</span><br>";
            }
        }
    } else {
        echo "<h3>3. ✅ ทุก columns มีครบแล้ว</h3>";
    }
    
    echo "<h3>4. ทดสอบการ Insert ข้อมูล</h3>";
    
    // ทดสอบ insert ข้อมูล
    $testData = [
        'type' => 'Test Device',
        'brand' => 'Test Brand',
        'model' => 'Test Model',
        'tag' => 'TEST-' . time(),
        'department' => 'Test Department',
        'status' => 'ใช้งาน',
        'hostname' => 'TEST-HOST',
        'operating_system' => 'Test OS',
        'serial_number' => 'TEST-SERIAL-' . time(),
        'asset_id' => 'TEST-ASSET-' . time(),
        'warranty_expire' => '2025-12-31',
        'description' => 'Test description',
        'asset_set' => 'Test Set',
        'created_by' => 'test',
        'updated_by' => 'test'
    ];
    
    $fields = array_keys($testData);
    $placeholders = ':' . implode(', :', $fields);
    $sql = "INSERT INTO assets (" . implode(', ', $fields) . ") VALUES (" . $placeholders . ")";
    
    echo "<pre>SQL: $sql</pre>";
    echo "<pre>Data: " . print_r($testData, true) . "</pre>";
    
    $stmt = $pdo->prepare($sql);
    if ($stmt->execute($testData)) {
        $insertId = $pdo->lastInsertId();
        echo "<span style='color: green;'>✅ ทดสอบ Insert สำเร็จ! ID: $insertId</span><br>";
        
        // ลบข้อมูลทดสอบ
        $pdo->prepare("DELETE FROM assets WHERE id = ?")->execute([$insertId]);
        echo "<span style='color: blue;'>🗑️ ลบข้อมูลทดสอบแล้ว</span><br>";
    } else {
        echo "<span style='color: red;'>❌ ทดสอบ Insert ไม่สำเร็จ</span><br>";
    }
    
} catch (PDOException $e) {
    echo "<span style='color: red;'>❌ เกิดข้อผิดพลาด: " . $e->getMessage() . "</span><br>";
}

echo "<hr>";
echo "<a href='test_import_simple.php'>ทดสอบ Import</a> | ";
echo "<a href='import_assets.php'>หน้า Import</a> | ";
echo "<a href='index.php'>หน้าหลัก</a>";
?>
