<?php
// ทดสอบการจัดตำแหน่ง badge ใน Profile Modal
require_once 'includes/auth.php';

// ตรวจสอบการล็อกอิน
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

echo "<h2>ทดสอบการจัดตำแหน่ง Badge ใน Profile Modal</h2>";

try {
    // ตรวจสอบ CSS ที่แก้ไข
    echo "<h3>1. ตรวจสอบ CSS ที่แก้ไข</h3>";
    
    if (file_exists('assets/style.css')) {
        $cssContent = file_get_contents('assets/style.css');
        
        $badgeAlignmentCss = [
            'justify-content: flex-end' => 'จัดตำแหน่งขวาใน detail-row span',
            'margin-left: auto' => 'ดัน badge ไปขวาสุด',
            'white-space: nowrap' => 'ป้องกัน badge แตกบรรทัด',
            '.badge-container' => 'Container สำหรับ badge',
            'display: flex' => 'ใช้ flexbox สำหรับจัดตำแหน่ง'
        ];
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>CSS Property/Class</th>";
        echo "<th style='padding: 8px;'>Purpose</th>";
        echo "<th style='padding: 8px;'>Status</th>";
        echo "</tr>";
        
        foreach ($badgeAlignmentCss as $css => $purpose) {
            $exists = strpos($cssContent, $css) !== false;
            $icon = $exists ? '✅' : '❌';
            $status = $exists ? 'มี' : 'ไม่มี';
            
            echo "<tr>";
            echo "<td style='padding: 8px; font-family: monospace; font-size: 0.9rem;'>$css</td>";
            echo "<td style='padding: 8px;'>$purpose</td>";
            echo "<td style='padding: 8px;'>$icon $status</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>❌ ไม่พบไฟล์ assets/style.css</p>";
    }
    
    // ตรวจสอบ HTML ที่แก้ไข
    echo "<h3>2. ตรวจสอบ HTML Structure ที่แก้ไข</h3>";
    
    $htmlFiles = ['index.php', 'users.php'];
    
    foreach ($htmlFiles as $file) {
        if (file_exists($file)) {
            echo "<h4>ไฟล์: $file</h4>";
            $content = file_get_contents($file);
            
            $badgeElements = [
                'badge-container' => 'Container wrapper สำหรับ badge',
                'profile_role.*role-badge' => 'Role badge element',
                'profile_status.*status-badge' => 'Status badge element'
            ];
            
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 8px;'>Element Pattern</th>";
            echo "<th style='padding: 8px;'>Description</th>";
            echo "<th style='padding: 8px;'>Status</th>";
            echo "</tr>";
            
            foreach ($badgeElements as $pattern => $description) {
                $exists = strpos($content, $pattern) !== false || preg_match("/$pattern/", $content);
                $icon = $exists ? '✅' : '❌';
                $status = $exists ? 'มี' : 'ไม่มี';
                
                echo "<tr>";
                echo "<td style='padding: 8px; font-family: monospace; font-size: 0.9rem;'>$pattern</td>";
                echo "<td style='padding: 8px;'>$description</td>";
                echo "<td style='padding: 8px;'>$icon $status</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
    // ตรวจสอบ JavaScript ที่เกี่ยวข้อง
    echo "<h3>3. ตรวจสอบ JavaScript สำหรับ Badge</h3>";
    
    foreach ($htmlFiles as $file) {
        if (file_exists($file)) {
            echo "<h4>JavaScript ใน $file:</h4>";
            $content = file_get_contents($file);
            
            $jsFeatures = [
                'profile_role.*className' => 'กำหนด CSS class สำหรับ role badge',
                'profile_status.*className' => 'กำหนด CSS class สำหรับ status badge',
                'role-badge admin' => 'เพิ่ม class admin สำหรับ Admin role',
                'status-badge inactive' => 'เพิ่ม class inactive สำหรับ Inactive status'
            ];
            
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 8px;'>JavaScript Feature</th>";
            echo "<th style='padding: 8px;'>Description</th>";
            echo "<th style='padding: 8px;'>Status</th>";
            echo "</tr>";
            
            foreach ($jsFeatures as $feature => $description) {
                $exists = strpos($content, $feature) !== false || preg_match("/$feature/", $content);
                $icon = $exists ? '✅' : '❌';
                $status = $exists ? 'มี' : 'ไม่มี';
                
                echo "<tr>";
                echo "<td style='padding: 8px; font-family: monospace; font-size: 0.9rem;'>$feature</td>";
                echo "<td style='padding: 8px;'>$description</td>";
                echo "<td style='padding: 8px;'>$icon $status</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
    // แสดงตัวอย่าง Badge
    echo "<h3>4. ตัวอย่าง Badge ที่จัดตำแหน่งแล้ว</h3>";
    
    echo "<div style='background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1); margin: 20px 0;'>";
    echo "<h4>ตัวอย่างการแสดงผล:</h4>";
    
    // จำลอง detail-row
    echo "<div style='display: flex; justify-content: space-between; align-items: center; padding: 15px 0; border-bottom: 1px solid #f1f5f9;'>";
    echo "<label style='font-weight: 600; color: #475569; margin: 0; min-width: 140px; display: flex; align-items: center; gap: 8px;'>";
    echo "<span style='width: 4px; height: 4px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%;'></span>";
    echo "🎭 บทบาท";
    echo "</label>";
    echo "<span style='color: #1e293b; text-align: right; flex: 1; font-weight: 500; font-size: 0.95rem; display: flex; justify-content: flex-end; align-items: center;'>";
    echo "<span style='display: flex; justify-content: flex-end; align-items: center; width: 100%;'>";
    echo "<span style='background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white; padding: 6px 14px; border-radius: 20px; font-size: 0.8rem; font-weight: 600; box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3); display: inline-flex; align-items: center; gap: 5px; margin-left: auto; white-space: nowrap;'>Admin</span>";
    echo "</span>";
    echo "</span>";
    echo "</div>";
    
    echo "<div style='display: flex; justify-content: space-between; align-items: center; padding: 15px 0; border-bottom: 1px solid #f1f5f9;'>";
    echo "<label style='font-weight: 600; color: #475569; margin: 0; min-width: 140px; display: flex; align-items: center; gap: 8px;'>";
    echo "<span style='width: 4px; height: 4px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%;'></span>";
    echo "🟢 สถานะ";
    echo "</label>";
    echo "<span style='color: #1e293b; text-align: right; flex: 1; font-weight: 500; font-size: 0.95rem; display: flex; justify-content: flex-end; align-items: center;'>";
    echo "<span style='display: flex; justify-content: flex-end; align-items: center; width: 100%;'>";
    echo "<span style='background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 6px 14px; border-radius: 20px; font-size: 0.8rem; font-weight: 600; box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3); display: inline-flex; align-items: center; gap: 5px; margin-left: auto; white-space: nowrap;'>Active</span>";
    echo "</span>";
    echo "</span>";
    echo "</div>";
    
    echo "<div style='display: flex; justify-content: space-between; align-items: center; padding: 15px 0;'>";
    echo "<label style='font-weight: 600; color: #475569; margin: 0; min-width: 140px; display: flex; align-items: center; gap: 8px;'>";
    echo "<span style='width: 4px; height: 4px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%;'></span>";
    echo "🎭 บทบาท (User)";
    echo "</label>";
    echo "<span style='color: #1e293b; text-align: right; flex: 1; font-weight: 500; font-size: 0.95rem; display: flex; justify-content: flex-end; align-items: center;'>";
    echo "<span style='display: flex; justify-content: flex-end; align-items: center; width: 100%;'>";
    echo "<span style='background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 6px 14px; border-radius: 20px; font-size: 0.8rem; font-weight: 600; box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3); display: inline-flex; align-items: center; gap: 5px; margin-left: auto; white-space: nowrap;'>User</span>";
    echo "</span>";
    echo "</span>";
    echo "</div>";
    
    echo "</div>";
    
    // ทดสอบ API
    echo "<h3>5. ทดสอบ API และการแสดงผล</h3>";
    
    if (file_exists('get_profile_data.php')) {
        ob_start();
        include 'get_profile_data.php';
        $response = ob_get_clean();
        
        $data = json_decode($response, true);
        
        if ($data && isset($data['success']) && $data['success']) {
            echo "<p style='color: green;'>✅ API ทำงานได้ถูกต้อง</p>";
            echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
            echo "<strong>ข้อมูลผู้ใช้ปัจจุบัน:</strong><br>";
            echo "Role: " . ($data['user']['role'] ?? 'N/A') . "<br>";
            echo "Status: " . ($data['user']['status'] ?? 'N/A') . "<br>";
            echo "</div>";
        }
    }
    
    echo "<h3>6. สรุปการแก้ไข</h3>";
    
    echo "<div style='background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 15px; margin: 20px 0;'>";
    echo "<h4 style='color: white; margin-top: 0;'>🎯 การแก้ไขที่ทำ:</h4>";
    echo "<ul style='color: white;'>";
    echo "<li>✨ <strong>CSS Flexbox:</strong> ใช้ justify-content: flex-end สำหรับจัดตำแหน่งขวา</li>";
    echo "<li>🎯 <strong>Margin Auto:</strong> ใช้ margin-left: auto ดัน badge ไปขวาสุด</li>";
    echo "<li>📦 <strong>Badge Container:</strong> เพิ่ม wrapper สำหรับ badge</li>";
    echo "<li>🔒 <strong>No Wrap:</strong> ป้องกัน badge แตกบรรทัด</li>";
    echo "<li>🎨 <strong>Consistent Alignment:</strong> badge ทุกตัวอยู่ชิดขวาเหมือนกัน</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #c3e6cb;'>";
    echo "<h4 style='color: #155724;'>✅ ผลลัพธ์:</h4>";
    echo "<ul style='color: #155724;'>";
    echo "<li>✅ Badge บทบาทและสถานะอยู่ชิดขวาแล้ว</li>";
    echo "<li>✅ การจัดตำแหน่งสม่ำเสมอในทุกแถว</li>";
    echo "<li>✅ ใช้งานได้ทั้ง index.php และ users.php</li>";
    echo "<li>✅ Responsive design ยังคงทำงานได้</li>";
    echo "<li>✅ Badge สีต่างกันสำหรับ Admin/User และ Active/Inactive</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='index.php' style='background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 12px 24px; text-decoration: none; border-radius: 10px; margin-right: 10px; display: inline-flex; align-items: center; gap: 8px;'>🏠 ทดสอบใน index.php</a>";
    echo "<a href='users.php' style='background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 12px 24px; text-decoration: none; border-radius: 10px; display: inline-flex; align-items: center; gap: 8px;'>👥 ทดสอบใน users.php</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ เกิดข้อผิดพลาด</h3>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<p style='color: red; margin-top: 20px;'><strong>หมายเหตุ:</strong> กรุณาลบไฟล์ test_badge_alignment.php หลังจากทดสอบเสร็จแล้ว</p>";
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบการจัดตำแหน่ง Badge</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        h2 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
        table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            width: 100%;
        }
    </style>
</head>
<body>
    <!-- เนื้อหาจะแสดงจาก PHP ด้านบน -->
</body>
</html>
