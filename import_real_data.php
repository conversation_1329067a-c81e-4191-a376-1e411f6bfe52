<?php
session_start();

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config/database.php';

// Check if functions.php exists
if (file_exists('includes/functions.php')) {
    require_once 'includes/functions.php';
}

// Set up session
$_SESSION['username'] = 'admin';
$_SESSION['role'] = 'Admin';

echo "=== Import ไฟล์ import.csv เข้าฐานข้อมูล ===\n\n";

// Test database connection first
try {
    $stmt = $pdo->query("SELECT 1");
    echo "✅ การเชื่อมต่อฐานข้อมูล: สำเร็จ\n";
} catch (Exception $e) {
    echo "❌ การเชื่อมต่อฐานข้อมูล: ล้มเหลว - " . $e->getMessage() . "\n";
    exit;
}

// Check if import.csv exists
if (!file_exists('import.csv')) {
    echo "❌ ไม่พบไฟล์ import.csv\n";
    exit;
}

echo "✅ พบไฟล์ import.csv\n";

// Include functions from import_csv.php
function createColumnMapping($headers) {
    $mapping = [
        'Type' => 'type',
        'Brand' => 'brand',
        'Model' => 'model',
        'Tag' => 'tag',
        'Department' => 'department',
        'Status' => 'status',
        'Hostname' => 'hostname',
        'Operating System' => 'operating_system',
        'Serial Number' => 'serial_number',
        'Asset ID' => 'asset_id',
        'Warranty Expire' => 'warranty_expire',
        'Description' => 'description',
        'Set' => 'set_name'
    ];
    
    $columnMap = [];
    foreach ($headers as $index => $header) {
        $header = trim($header);
        if (isset($mapping[$header])) {
            $columnMap[$index] = $mapping[$header];
        }
    }
    
    return $columnMap;
}

function parseDate($dateString) {
    if (empty($dateString) || $dateString === '-') {
        return null;
    }
    
    $formats = ['d/m/Y', 'Y-m-d', 'm/d/Y', 'd-m-Y'];
    
    foreach ($formats as $format) {
        $date = DateTime::createFromFormat($format, $dateString);
        if ($date !== false) {
            return $date->format('Y-m-d');
        }
    }
    
    return null;
}

function processRow($data, $headers, $columnMap, $pdo, $rowNumber) {
    $result = ['success' => false, 'error' => ''];
    
    try {
        $insertData = [];
        
        foreach ($columnMap as $index => $dbColumn) {
            $value = isset($data[$index]) ? trim($data[$index]) : '';
            
            // Skip empty values and dashes
            if (!empty($value) && $value !== '-') {
                if ($dbColumn === 'warranty_expire') {
                    $value = parseDate($value);
                    if ($value === null) {
                        continue; // Skip invalid dates
                    }
                }
                $insertData[$dbColumn] = $value;
            }
        }
        
        if (empty($insertData['type'])) {
            $result['error'] = 'ไม่มีข้อมูล Type';
            return $result;
        }
        
        // Check for duplicate tag (skip if tag already exists)
        if (!empty($insertData['tag'])) {
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM assets WHERE tag = ?");
            $stmt->execute([$insertData['tag']]);
            if ($stmt->fetchColumn() > 0) {
                $result['error'] = "Tag '{$insertData['tag']}' มีอยู่แล้ว (ข้าม)";
                return $result;
            }
        }
        
        $insertData['created_by'] = $_SESSION['username'] ?? 'system';
        $insertData['updated_by'] = $_SESSION['username'] ?? 'system';
        $insertData['created_date'] = date('Y-m-d H:i:s');
        $insertData['updated_date'] = date('Y-m-d H:i:s');
        
        $columns = array_keys($insertData);
        $placeholders = ':' . implode(', :', $columns);
        $sql = "INSERT INTO assets (`" . implode("`, `", $columns) . "`) VALUES ($placeholders)";
        
        $stmt = $pdo->prepare($sql);
        if ($stmt->execute($insertData)) {
            $assetId = $pdo->lastInsertId();
            
            // Create log entry
            try {
                $logStmt = $pdo->prepare("INSERT INTO asset_logs (asset_id, action_type, changed_by, description, changed_date) VALUES (?, 'CREATE', ?, 'Imported from CSV', ?)");
                $logStmt->execute([$assetId, $_SESSION['username'] ?? 'system', date('Y-m-d H:i:s')]);
            } catch (Exception $logError) {
                // Log creation failed, but asset creation succeeded
                echo "    Warning: Could not create log entry for Asset ID $assetId\n";
            }
            
            $result['success'] = true;
            $result['asset_id'] = $assetId;
        } else {
            $result['error'] = 'ไม่สามารถบันทึกข้อมูลได้: ' . implode(', ', $stmt->errorInfo());
        }
        
    } catch (Exception $e) {
        $result['error'] = $e->getMessage();
    }
    
    return $result;
}

// Start processing
echo "\n=== เริ่มประมวลผลไฟล์ ===\n";

try {
    $content = file_get_contents('import.csv');
    if ($content === false) {
        echo "❌ ไม่สามารถอ่านไฟล์ import.csv ได้\n";
        exit;
    }

    if (strlen($content) === 0) {
        echo "❌ ไฟล์ import.csv ว่างเปล่า\n";
        exit;
    }

    // Remove BOM if present
    $content = str_replace("\xEF\xBB\xBF", '', $content);

    // Split lines
    $lines = preg_split('/\r\n|\r|\n/', $content);
    $lines = array_filter($lines, function($line) {
        return trim($line) !== '';
    });

    if (empty($lines)) {
        echo "❌ ไฟล์ CSV ไม่มีข้อมูล\n";
        exit;
    }

    if (count($lines) < 2) {
        echo "❌ ไฟล์ CSV ต้องมีอย่างน้อย 2 บรรทัด (header + data)\n";
        exit;
    }

    // Read header
    $headerLine = array_shift($lines);
    $headers = str_getcsv($headerLine);

    if (!$headers || empty($headers)) {
        echo "❌ ไฟล์ CSV ไม่มี header หรือ header ไม่ถูกต้อง\n";
        exit;
    }

    // Clean headers
    $headers = array_map(function($header) {
        return trim($header, " \t\n\r\0\x0B\"'");
    }, $headers);

    echo "✅ Headers: " . implode(', ', $headers) . "\n";
    echo "✅ จำนวนบรรทัดข้อมูล: " . count($lines) . " บรรทัด\n\n";

    // Check required headers
    $requiredHeaders = ['Type'];
    foreach ($requiredHeaders as $required) {
        if (!in_array($required, $headers)) {
            echo "❌ ไม่พบ header ที่จำเป็น: $required\n";
            exit;
        }
    }

    $columnMap = createColumnMapping($headers);
    $headerCount = count($headers);

    $rowNumber = 1;
    $imported = 0;
    $errors = 0;
    $skipped = 0;
    $errorDetails = [];

    echo "=== เริ่ม Import ข้อมูล ===\n";

    $pdo->beginTransaction();

    foreach ($lines as $line) {
        $rowNumber++;

        if (trim($line) === '') {
            continue;
        }

        $data = str_getcsv($line);

        // Fix column count
        if (count($data) !== $headerCount) {
            if (count($data) < $headerCount) {
                $data = array_pad($data, $headerCount, '');
            } else {
                $data = array_slice($data, 0, $headerCount);
            }
        }

        // Skip empty rows
        if (empty(array_filter($data, function($value) { return trim($value) !== ''; }))) {
            $skipped++;
            continue;
        }

        $rowResult = processRow($data, $headers, $columnMap, $pdo, $rowNumber);

        if ($rowResult['success']) {
            $imported++;
            if ($imported % 100 == 0) {
                echo "  ✅ ประมวลผลแล้ว $imported รายการ...\n";
            }
        } else {
            $errors++;
            $errorDetails[] = "แถว $rowNumber: " . $rowResult['error'];
            
            // Show first few errors
            if ($errors <= 5) {
                echo "  ❌ แถว $rowNumber: " . $rowResult['error'] . "\n";
            }
        }
    }

    $pdo->commit();

    echo "\n=== ผลลัพธ์การ Import ===\n";
    echo "✅ นำเข้าสำเร็จ: $imported รายการ\n";
    echo "❌ ข้อผิดพลาด: $errors รายการ\n";
    echo "⏭️ ข้าม: $skipped รายการ\n";
    echo "📊 รวมประมวลผล: " . ($imported + $errors + $skipped) . " รายการ\n";

    if ($errors > 0 && $errors <= 20) {
        echo "\nรายละเอียดข้อผิดพลาด:\n";
        foreach (array_slice($errorDetails, 0, 20) as $error) {
            echo "  - $error\n";
        }
        if (count($errorDetails) > 20) {
            echo "  ... และอีก " . (count($errorDetails) - 20) . " ข้อผิดพลาด\n";
        }
    }

    // Check final database state
    echo "\n=== สถานะฐานข้อมูลหลัง Import ===\n";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM assets");
    $result = $stmt->fetch();
    echo "จำนวน Assets ทั้งหมด: " . $result['count'] . "\n";

    $stmt = $pdo->query("SELECT COUNT(*) as count FROM asset_logs WHERE description LIKE '%Imported from CSV%'");
    $result = $stmt->fetch();
    echo "จำนวน Import Logs: " . $result['count'] . "\n";

} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollback();
    }
    echo "❌ เกิดข้อผิดพลาดร้ายแรง: " . $e->getMessage() . "\n";
}

echo "\n🎉 การ Import เสร็จสิ้น!\n";
?>
