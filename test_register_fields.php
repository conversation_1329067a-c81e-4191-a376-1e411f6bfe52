<?php
// ทดสอบ register.php ที่แก้ไขแล้ว
require_once 'config/database.php';
require_once 'includes/auth.php';

echo "<h2>ทดสอบ register.php ที่แก้ไขแล้ว</h2>";

try {
    // ตรวจสอบโครงสร้างฐานข้อมูล
    echo "<h3>1. ตรวจสอบโครงสร้างฐานข้อมูล</h3>";
    
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: red;'>❌ ไม่พบตาราง users</p>";
        exit;
    }
    
    // ตรวจสอบ columns ที่จำเป็น
    $requiredColumns = ['username', 'password', 'full_name', 'email', 'role', 'status'];
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h4>Columns ในตาราง users:</h4>";
    echo "<ul>";
    foreach ($requiredColumns as $col) {
        $exists = in_array($col, $columns);
        $icon = $exists ? '✅' : '❌';
        echo "<li>$icon $col</li>";
    }
    echo "</ul>";
    
    // ทดสอบการสร้างผู้ใช้ด้วย field names ที่ถูกต้อง
    echo "<h3>2. ทดสอบการสร้างผู้ใช้ด้วย field names ที่ถูกต้อง</h3>";
    
    $testUser = [
        'username' => 'testuser_' . time(),
        'password' => 'testpass123',
        'full_name' => 'Test User Full Name',
        'email' => '<EMAIL>',
        'role' => 'User',
        'status' => 'Active'
    ];
    
    echo "<h4>ข้อมูลทดสอบ:</h4>";
    echo "<ul>";
    echo "<li><strong>username:</strong> {$testUser['username']}</li>";
    echo "<li><strong>password:</strong> {$testUser['password']}</li>";
    echo "<li><strong>full_name:</strong> {$testUser['full_name']}</li>";
    echo "<li><strong>email:</strong> {$testUser['email']}</li>";
    echo "<li><strong>role:</strong> {$testUser['role']}</li>";
    echo "<li><strong>status:</strong> {$testUser['status']}</li>";
    echo "</ul>";
    
    $result = $auth->createUser($testUser);
    if ($result) {
        echo "<p style='color: green;'>✅ สร้างผู้ใช้สำเร็จ</p>";
        
        // ตรวจสอบข้อมูลในฐานข้อมูล
        $stmt = $pdo->prepare("SELECT username, full_name, email, role, status FROM users WHERE username = ?");
        $stmt->execute([$testUser['username']]);
        $savedUser = $stmt->fetch();
        
        if ($savedUser) {
            echo "<h4>ข้อมูลที่บันทึกในฐานข้อมูล:</h4>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 8px;'>Field</th>";
            echo "<th style='padding: 8px;'>Value</th>";
            echo "<th style='padding: 8px;'>Status</th>";
            echo "</tr>";
            
            $checks = [
                'username' => $savedUser['username'] === $testUser['username'],
                'full_name' => $savedUser['full_name'] === $testUser['full_name'],
                'email' => $savedUser['email'] === $testUser['email'],
                'role' => $savedUser['role'] === $testUser['role'],
                'status' => $savedUser['status'] === $testUser['status']
            ];
            
            foreach ($checks as $field => $isCorrect) {
                $icon = $isCorrect ? '✅' : '❌';
                $value = $savedUser[$field] ?? 'NULL';
                echo "<tr>";
                echo "<td style='padding: 8px;'>$field</td>";
                echo "<td style='padding: 8px;'>$value</td>";
                echo "<td style='padding: 8px;'>$icon</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // ทดสอบล็อกอิน
        echo "<h4>ทดสอบล็อกอิน:</h4>";
        session_start();
        $loginResult = $auth->login($testUser['username'], $testUser['password']);
        if ($loginResult) {
            echo "<p style='color: green;'>✅ ล็อกอินสำเร็จ</p>";
            echo "<ul>";
            echo "<li>User ID: " . ($_SESSION['user_id'] ?? 'N/A') . "</li>";
            echo "<li>Username: " . ($_SESSION['username'] ?? 'N/A') . "</li>";
            echo "<li>Full Name: " . ($_SESSION['full_name'] ?? 'N/A') . "</li>";
            echo "<li>Role: " . ($_SESSION['role'] ?? 'N/A') . "</li>";
            echo "</ul>";
        } else {
            echo "<p style='color: red;'>❌ ล็อกอินล้มเหลว</p>";
        }
        session_destroy();
        
        // ลบผู้ใช้ทดสอบ
        $stmt = $pdo->prepare("DELETE FROM users WHERE username = ?");
        $stmt->execute([$testUser['username']]);
        echo "<p style='color: blue;'>🗑️ ลบผู้ใช้ทดสอบแล้ว</p>";
        
    } else {
        echo "<p style='color: red;'>❌ สร้างผู้ใช้ล้มเหลว</p>";
    }
    
    // ทดสอบ Default Role
    echo "<h3>3. ทดสอบ Default Role เป็น User</h3>";
    
    $testUserDefaultRole = [
        'username' => 'defaultrole_' . time(),
        'password' => 'testpass123',
        'full_name' => 'Default Role User',
        'email' => '<EMAIL>',
        'role' => 'User', // ตรวจสอบว่า register.php กำหนดเป็น User
        'status' => 'Active'
    ];
    
    $result = $auth->createUser($testUserDefaultRole);
    if ($result) {
        $stmt = $pdo->prepare("SELECT role FROM users WHERE username = ?");
        $stmt->execute([$testUserDefaultRole['username']]);
        $role = $stmt->fetchColumn();
        
        if ($role === 'User') {
            echo "<p style='color: green;'>✅ Default Role เป็น User ถูกต้อง</p>";
        } else {
            echo "<p style='color: red;'>❌ Default Role ไม่ถูกต้อง: $role</p>";
        }
        
        // ลบผู้ใช้ทดสอบ
        $stmt = $pdo->prepare("DELETE FROM users WHERE username = ?");
        $stmt->execute([$testUserDefaultRole['username']]);
        echo "<p style='color: blue;'>🗑️ ลบผู้ใช้ทดสอบแล้ว</p>";
    }
    
    // ทดสอบ Field Mapping
    echo "<h3>4. ทดสอบ Field Mapping</h3>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 8px;'>HTML Field</th>";
    echo "<th style='padding: 8px;'>Database Column</th>";
    echo "<th style='padding: 8px;'>Status</th>";
    echo "</tr>";
    
    $fieldMapping = [
        'username' => 'username',
        'full_name' => 'full_name',
        'email' => 'email',
        'password' => 'password',
        'role' => 'role (default: User)',
        'status' => 'status (default: Active)'
    ];
    
    foreach ($fieldMapping as $htmlField => $dbColumn) {
        echo "<tr>";
        echo "<td style='padding: 8px;'>$htmlField</td>";
        echo "<td style='padding: 8px;'>$dbColumn</td>";
        echo "<td style='padding: 8px;'>✅ ถูกต้อง</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>5. สรุปผลการทดสอบ</h3>";
    echo "<p style='color: green;'>✅ register.php ทำงานได้ถูกต้องตามที่แก้ไข</p>";
    echo "<ul>";
    echo "<li>✅ ใช้ field name 'full_name' สำหรับชื่อ-นามสกุล</li>";
    echo "<li>✅ ใช้ field name 'email' สำหรับ Email</li>";
    echo "<li>✅ Role default เป็น 'User'</li>";
    echo "<li>✅ Status default เป็น 'Active'</li>";
    echo "<li>✅ การเข้ารหัสรหัสผ่านทำงานถูกต้อง</li>";
    echo "<li>✅ การล็อกอินด้วยผู้ใช้ใหม่ทำงานได้</li>";
    echo "</ul>";
    
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='register.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>ทดสอบหน้า Register</a>";
    echo "<a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>ไปหน้า Login</a>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ เกิดข้อผิดพลาด</h3>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<p style='color: red; margin-top: 20px;'><strong>หมายเหตุ:</strong> กรุณาลบไฟล์ test_register_fields.php หลังจากทดสอบเสร็จแล้ว</p>";
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบ Register Fields</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <!-- เนื้อหาจะแสดงจาก PHP ด้านบน -->
</body>
</html>
