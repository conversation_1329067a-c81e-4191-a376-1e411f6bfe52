-- อัพเดทฐานข้อมูลให้ Asset ID ไม่จำเป็นต้องใส่ข้อมูล (NULL ได้)
-- รันไฟล์นี้ใน phpMyAdmin หรือ MySQL command line

USE asset_management;

-- แก้ไข column asset_id ให้สามารถเป็น NULL ได้
ALTER TABLE assets MODIFY COLUMN asset_id VARCHAR(50) NULL;

-- ตรวจสอบโครงสร้างตารางหลังการแก้ไข
DESCRIBE assets;

-- แสดงข้อมูล Asset ที่มี asset_id เป็น NULL หรือว่าง
SELECT id, type, brand, model, asset_id, created_date 
FROM assets 
WHERE asset_id IS NULL OR asset_id = '' 
ORDER BY created_date DESC;
