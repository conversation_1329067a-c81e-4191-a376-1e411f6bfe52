<?php
// ทดสอบ Profile Modal
require_once 'includes/auth.php';

// ตรวจสอบการล็อกอิน
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

echo "<h2>ทดสอบ Profile Modal</h2>";

try {
    // ตรวจสอบไฟล์ที่จำเป็น
    echo "<h3>1. ตรวจสอบไฟล์ที่จำเป็น</h3>";
    
    $requiredFiles = [
        'get_profile_data.php' => 'API ดึงข้อมูลโปรไฟล์',
        'update_profile.php' => 'API อัพเดทโปรไฟล์',
        'assets/style.css' => 'CSS สำหรับ Modal'
    ];
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 8px;'>File</th>";
    echo "<th style='padding: 8px;'>Description</th>";
    echo "<th style='padding: 8px;'>Status</th>";
    echo "</tr>";
    
    foreach ($requiredFiles as $file => $description) {
        $exists = file_exists($file);
        $icon = $exists ? '✅' : '❌';
        $status = $exists ? 'มี' : 'ไม่มี';
        
        echo "<tr>";
        echo "<td style='padding: 8px;'>$file</td>";
        echo "<td style='padding: 8px;'>$description</td>";
        echo "<td style='padding: 8px;'>$icon $status</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // ทดสอบ API get_profile_data.php
    echo "<h3>2. ทดสอบ API get_profile_data.php</h3>";
    
    if (file_exists('get_profile_data.php')) {
        // จำลองการเรียก API
        ob_start();
        include 'get_profile_data.php';
        $response = ob_get_clean();
        
        $data = json_decode($response, true);
        
        if ($data && isset($data['success'])) {
            if ($data['success']) {
                echo "<p style='color: green;'>✅ API ทำงานได้ถูกต้อง</p>";
                echo "<h4>ข้อมูลที่ได้รับ:</h4>";
                echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>";
                echo json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                echo "</pre>";
            } else {
                echo "<p style='color: red;'>❌ API ส่งคืน error: " . ($data['message'] ?? 'Unknown error') . "</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ API ไม่ส่งคืน JSON ที่ถูกต้อง</p>";
            echo "<p>Response: " . htmlspecialchars($response) . "</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ ไม่พบไฟล์ get_profile_data.php</p>";
    }
    
    // ตรวจสอบโครงสร้างฐานข้อมูล
    echo "<h3>3. ตรวจสอบโครงสร้างฐานข้อมูล</h3>";
    
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll();
    
    $availableFields = [];
    foreach ($columns as $column) {
        $availableFields[] = $column['Field'];
    }
    
    $profileFields = [
        'username' => 'จำเป็น',
        'full_name' => 'แสดงชื่อ-นามสกุล',
        'email' => 'แสดง email',
        'role' => 'แสดงบทบาท',
        'status' => 'แสดงสถานะ',
        'last_login' => 'แสดงเวลาล็อกอินล่าสุด',
        'created_date' => 'แสดงวันที่สร้างบัญชี'
    ];
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 8px;'>Field</th>";
    echo "<th style='padding: 8px;'>Purpose</th>";
    echo "<th style='padding: 8px;'>Status</th>";
    echo "</tr>";
    
    foreach ($profileFields as $field => $purpose) {
        $exists = in_array($field, $availableFields);
        $icon = $exists ? '✅' : '❌';
        $status = $exists ? 'มี' : 'ไม่มี';
        
        echo "<tr>";
        echo "<td style='padding: 8px;'>$field</td>";
        echo "<td style='padding: 8px;'>$purpose</td>";
        echo "<td style='padding: 8px;'>$icon $status</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // ตรวจสอบ CSS
    echo "<h3>4. ตรวจสอบ CSS สำหรับ Profile Modal</h3>";
    
    if (file_exists('assets/style.css')) {
        $cssContent = file_get_contents('assets/style.css');
        
        $cssClasses = [
            '.profile-info' => 'Container สำหรับข้อมูลโปรไฟล์',
            '.profile-avatar' => 'Avatar circle',
            '.avatar-circle' => 'รูปโปรไฟล์',
            '.profile-details' => 'รายละเอียดโปรไฟล์',
            '.detail-row' => 'แถวข้อมูล',
            '.role-badge' => 'Badge สำหรับ role',
            '.status-badge' => 'Badge สำหรับ status'
        ];
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>CSS Class</th>";
        echo "<th style='padding: 8px;'>Purpose</th>";
        echo "<th style='padding: 8px;'>Status</th>";
        echo "</tr>";
        
        foreach ($cssClasses as $class => $purpose) {
            $exists = strpos($cssContent, $class) !== false;
            $icon = $exists ? '✅' : '❌';
            $status = $exists ? 'มี' : 'ไม่มี';
            
            echo "<tr>";
            echo "<td style='padding: 8px;'>$class</td>";
            echo "<td style='padding: 8px;'>$purpose</td>";
            echo "<td style='padding: 8px;'>$icon $status</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>❌ ไม่พบไฟล์ assets/style.css</p>";
    }
    
    // ตรวจสอบ JavaScript functions
    echo "<h3>5. ตรวจสอบ JavaScript Functions</h3>";
    
    $jsFiles = ['index.php', 'users.php'];
    $jsFunctions = [
        'openProfileModal' => 'เปิด Profile Modal',
        'closeProfileModal' => 'ปิด Profile Modal',
        'loadProfileData' => 'โหลดข้อมูลโปรไฟล์',
        'toggleEditMode' => 'สลับโหมดแก้ไข',
        'saveProfile' => 'บันทึกโปรไฟล์'
    ];
    
    foreach ($jsFiles as $file) {
        if (file_exists($file)) {
            echo "<h4>ไฟล์: $file</h4>";
            $content = file_get_contents($file);
            
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 8px;'>Function</th>";
            echo "<th style='padding: 8px;'>Purpose</th>";
            echo "<th style='padding: 8px;'>Status</th>";
            echo "</tr>";
            
            foreach ($jsFunctions as $func => $purpose) {
                $exists = strpos($content, "function $func") !== false;
                $icon = $exists ? '✅' : '❌';
                $status = $exists ? 'มี' : 'ไม่มี';
                
                echo "<tr>";
                echo "<td style='padding: 8px;'>$func()</td>";
                echo "<td style='padding: 8px;'>$purpose</td>";
                echo "<td style='padding: 8px;'>$icon $status</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
    echo "<h3>6. สรุปผลการทดสอบ</h3>";
    
    $allFilesExist = true;
    foreach ($requiredFiles as $file => $desc) {
        if (!file_exists($file)) {
            $allFilesExist = false;
            break;
        }
    }
    
    if ($allFilesExist) {
        echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #c3e6cb;'>";
        echo "<h4 style='color: #155724;'>✅ Profile Modal พร้อมใช้งาน</h4>";
        echo "<ul style='color: #155724;'>";
        echo "<li>✅ ไฟล์ API ครบถ้วน</li>";
        echo "<li>✅ CSS สำหรับ Modal มีอยู่</li>";
        echo "<li>✅ JavaScript functions ครบถ้วน</li>";
        echo "<li>✅ โครงสร้างฐานข้อมูลพร้อม</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<h4>วิธีใช้งาน:</h4>";
        echo "<ol>";
        echo "<li>คลิกที่ลิงก์ 'โปรไฟล์' ในเมนู</li>";
        echo "<li>Modal จะเปิดขึ้นแสดงข้อมูลโปรไฟล์</li>";
        echo "<li>คลิก 'แก้ไขโปรไฟล์' เพื่อแก้ไขข้อมูล</li>";
        echo "<li>กรอกข้อมูลและคลิก 'บันทึก'</li>";
        echo "<li>คลิกนอก Modal หรือปุ่ม 'ปิด' เพื่อปิด</li>";
        echo "</ol>";
    } else {
        echo "<div style='background: #f8d7da; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #f5c6cb;'>";
        echo "<h4 style='color: #721c24;'>⚠️ Profile Modal ยังไม่พร้อมใช้งาน</h4>";
        echo "<p style='color: #721c24;'>กรุณาตรวจสอบไฟล์ที่ขาดหายไป</p>";
        echo "</div>";
    }
    
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>ทดสอบใน index.php</a>";
    echo "<a href='users.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>ทดสอบใน users.php</a>";
    echo "<a href='get_profile_data.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>ดู API Response</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ เกิดข้อผิดพลาด</h3>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<p style='color: red; margin-top: 20px;'><strong>หมายเหตุ:</strong> กรุณาลบไฟล์ test_profile_modal.php หลังจากทดสอบเสร็จแล้ว</p>";
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบ Profile Modal</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        pre {
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <!-- เนื้อหาจะแสดงจาก PHP ด้านบน -->
</body>
</html>
