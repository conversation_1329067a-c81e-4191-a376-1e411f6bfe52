<?php
// ไฟล์สำหรับอัพเดท Type ให้ตรงกับตัวเลือกใหม่
// รันไฟล์นี้เพียงครั้งเดียวแล้วลบทิ้ง

require_once 'config/database.php';

try {
    echo "<h2>กำลังอัพเดท Asset Types ให้ตรงกับตัวเลือกใหม่...</h2>";
    
    // อัพเดท Type ที่มีอยู่แล้ว
    $updates = [
        "UPDATE assets SET type = 'Desktop' WHERE type IN ('Computer', 'PC', 'Desktop Computer')",
        "UPDATE assets SET operating_system = 'Windows 11' WHERE operating_system = 'Windows 11 Pro'",
        "UPDATE assets SET operating_system = 'Windows 10' WHERE operating_system = 'Windows 10 Pro'",
        "UPDATE assets SET operating_system = 'Windows 7' WHERE operating_system = 'Windows 7 Pro'"
    ];
    
    foreach ($updates as $sql) {
        $result = $pdo->exec($sql);
        echo "<p style='color: green;'>✅ อัพเดทข้อมูล: {$result} รายการ</p>";
    }
    
    // เพิ่มข้อมูลตัวอย่างใหม่
    echo "<h3>เพิ่มข้อมูลตัวอย่างใหม่:</h3>";
    
    $newAssets = [
        ['All-in-one', 'Apple', 'iMac 24"', 'AIO001', 'Design', 'Active', 'DESIGN-AIO-001', 'MacOS', 'AP123456789', 'AST006', '2025-08-15', 'All-in-one computer for design team', 'Design Set 1'],
        ['Tablet', 'Apple', 'iPad Pro 12.9"', 'TAB001', 'Sales', 'Active', NULL, NULL, 'AP987654321', 'AST007', '2024-11-30', 'Tablet for sales presentation', 'Mobile Set 2'],
        ['UPS', 'APC', 'Smart-UPS 1500VA', 'UPS001', 'IT Department', 'Active', NULL, NULL, 'APC11223344', 'AST008', '2026-02-28', 'Uninterruptible Power Supply for server room', 'Power Set 1'],
        ['Barcode Scanner', 'Zebra', 'DS2208', 'BS001', 'Warehouse', 'Active', NULL, NULL, 'ZB556677889', 'AST009', '2025-05-10', 'Handheld barcode scanner for inventory', 'Warehouse Set 1'],
        ['IP Phone', 'Cisco', 'IP Phone 8841', 'PH001', 'Reception', 'Active', 'RECEPTION-PH-001', NULL, 'CS334455667', 'AST010', '2025-09-20', 'IP phone for reception desk', 'Communication Set 1'],
        ['Access Point', 'Ubiquiti', 'UniFi AP AC Pro', 'AP001', 'IT Department', 'Active', 'IT-AP-001', NULL, 'UB778899001', 'AST011', '2025-12-05', 'Wireless access point for office', 'Network Set 2'],
        ['Teleconference', 'Logitech', 'MeetUp Conference Cam', 'TC001', 'Meeting Room A', 'Active', 'MEETING-TC-001', NULL, 'LG445566778', 'AST012', '2025-07-18', 'Conference camera for video meetings', 'Meeting Set 1'],
        ['Queue', 'Custom', 'Digital Queue Display', 'Q001', 'Customer Service', 'Active', 'CS-QUEUE-001', NULL, 'CQ123456789', 'AST013', '2025-04-25', 'Digital queue management system', 'Service Set 1'],
        ['Barcode Printer', 'Zebra', 'ZD420', 'BP001', 'Warehouse', 'Active', NULL, NULL, 'ZB998877665', 'AST014', '2025-06-12', 'Thermal barcode label printer', 'Warehouse Set 2'],
        ['Peripheral', 'Logitech', 'MX Master 3 Mouse', 'PER001', 'IT Department', 'Active', NULL, NULL, 'LG112233445', 'AST015', '2024-10-30', 'Wireless mouse for workstation', 'Peripheral Set 1']
    ];
    
    $sql = "INSERT INTO assets (type, brand, model, tag, department, status, hostname, operating_system, serial_number, asset_id, warranty_expire, description, asset_set, created_by, updated_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'admin', 'admin')";
    $stmt = $pdo->prepare($sql);
    
    $addedCount = 0;
    foreach ($newAssets as $asset) {
        try {
            $stmt->execute($asset);
            echo "<p style='color: green;'>✅ เพิ่ม {$asset[0]}: {$asset[1]} {$asset[2]}</p>";
            $addedCount++;
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                echo "<p style='color: orange;'>⚠️ ข้าม {$asset[0]}: {$asset[1]} {$asset[2]} (มีอยู่แล้ว)</p>";
            } else {
                echo "<p style='color: red;'>❌ ผิดพลาด {$asset[0]}: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    echo "<p style='color: blue;'>📊 เพิ่มข้อมูลใหม่: {$addedCount} รายการ</p>";
    
    // แสดงสถิติ Type ทั้งหมด
    echo "<h3>สถิติ Asset Types หลังการอัพเดท:</h3>";
    $stmt = $pdo->query("SELECT type, COUNT(*) as count FROM assets GROUP BY type ORDER BY type");
    $types = $stmt->fetchAll();
    
    echo "<table style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f8f9fa;'><th style='border: 1px solid #ddd; padding: 10px;'>Type</th><th style='border: 1px solid #ddd; padding: 10px;'>จำนวน</th></tr>";
    
    $total = 0;
    foreach ($types as $type) {
        echo "<tr>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$type['type']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px; text-align: center;'>{$type['count']}</td>";
        echo "</tr>";
        $total += $type['count'];
    }
    
    echo "<tr style='background: #e9ecef; font-weight: bold;'>";
    echo "<td style='border: 1px solid #ddd; padding: 8px;'>รวมทั้งหมด</td>";
    echo "<td style='border: 1px solid #ddd; padding: 8px; text-align: center;'>{$total}</td>";
    echo "</tr>";
    echo "</table>";
    
    echo "<h2 style='color: green;'>🎉 การอัพเดทเสร็จสมบูรณ์!</h2>";
    echo "<p><strong>ตอนนี้ระบบมี Asset Types ครบถ้วนตามที่กำหนดแล้ว</strong></p>";
    echo "<p><a href='index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>กลับไปหน้าหลัก</a></p>";
    echo "<p style='color: red; margin-top: 20px;'><strong>หมายเหตุ:</strong> กรุณาลบไฟล์ update_types.php หลังจากรันเสร็จแล้ว</p>";
    
} catch (PDOException $e) {
    echo "<h2 style='color: red;'>❌ เกิดข้อผิดพลาด</h2>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<p>กรุณาตรวจสอบการเชื่อมต่อฐานข้อมูลและลองใหม่</p>";
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>อัพเดท Asset Types - Asset Management System</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- เนื้อหาจะแสดงจาก PHP ด้านบน -->
    </div>
</body>
</html>
