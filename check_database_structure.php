<?php
// ตรวจสอบโครงสร้างฐานข้อมูลจริง
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🔍 ตรวจสอบโครงสร้างฐานข้อมูล Assets</h2>";

// เชื่อมต่อฐานข้อมูล
try {
    $pdo = new PDO("mysql:host=localhost;dbname=asset_management;charset=utf8", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✅ เชื่อมต่อฐานข้อมูลสำเร็จ</p>";
} catch (Exception $e) {
    die("<p style='color: red;'>❌ ไม่สามารถเชื่อมต่อฐานข้อมูล: " . $e->getMessage() . "</p>");
}

// ตรวจสอบตารางที่มีอยู่
echo "<h3>ตารางที่มีในฐานข้อมูล:</h3>";
try {
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li><strong>$table</strong></li>";
    }
    echo "</ul>";
    
    if (!in_array('assets', $tables)) {
        echo "<p style='color: red;'>❌ ไม่พบตาราง 'assets'</p>";
        echo "<p>กำลังสร้างตาราง assets...</p>";
        
        // สร้างตาราง assets ตามโครงสร้างมาตรฐาน
        $createSQL = "
        CREATE TABLE assets (
            id INT AUTO_INCREMENT PRIMARY KEY,
            type VARCHAR(100),
            brand VARCHAR(100),
            model VARCHAR(100),
            tag VARCHAR(100),
            department VARCHAR(100),
            status VARCHAR(50) DEFAULT 'ใช้งาน',
            hostname VARCHAR(100),
            operating_system VARCHAR(100),
            serial_number VARCHAR(100),
            asset_id VARCHAR(100),
            warranty_expire DATE,
            description TEXT,
            set_name VARCHAR(100),
            person_added VARCHAR(100),
            person_modified VARCHAR(100),
            date_added DATETIME DEFAULT CURRENT_TIMESTAMP,
            date_modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ";
        
        $pdo->exec($createSQL);
        echo "<p style='color: green;'>✅ สร้างตาราง assets สำเร็จ</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ ข้อผิดพลาด: " . $e->getMessage() . "</p>";
}

// แสดงโครงสร้างตาราง assets
echo "<h3>โครงสร้างตาราง assets:</h3>";
try {
    $stmt = $pdo->query("DESCRIBE assets");
    $columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; background: white;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 10px;'>Field</th>";
    echo "<th style='padding: 10px;'>Type</th>";
    echo "<th style='padding: 10px;'>Null</th>";
    echo "<th style='padding: 10px;'>Key</th>";
    echo "<th style='padding: 10px;'>Default</th>";
    echo "<th style='padding: 10px;'>Extra</th>";
    echo "</tr>";
    
    $columnNames = [];
    foreach ($columns as $column) {
        $columnNames[] = $column['Field'];
        echo "<tr>";
        echo "<td style='padding: 10px;'><strong>{$column['Field']}</strong></td>";
        echo "<td style='padding: 10px;'>{$column['Type']}</td>";
        echo "<td style='padding: 10px;'>{$column['Null']}</td>";
        echo "<td style='padding: 10px;'>{$column['Key']}</td>";
        echo "<td style='padding: 10px;'>{$column['Default']}</td>";
        echo "<td style='padding: 10px;'>{$column['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h4>รายชื่อคอลัมน์ทั้งหมด:</h4>";
    echo "<p style='background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace;'>";
    echo implode(', ', $columnNames);
    echo "</p>";
    
    // นับจำนวน records
    $stmt = $pdo->query("SELECT COUNT(*) FROM assets");
    $count = $stmt->fetchColumn();
    echo "<p><strong>จำนวน records ในตาราง:</strong> $count</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ ไม่สามารถดูโครงสร้างตาราง: " . $e->getMessage() . "</p>";
}

// สร้าง PHP code สำหรับ add_asset.php
echo "<h3>📋 PHP Code สำหรับ add_asset.php:</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 0.9rem; overflow-x: auto;'>";
echo htmlspecialchars('
// ข้อมูลที่จะใส่ในตาราง
$data = [
    \'type\' => trim($_POST[\'type\'] ?? \'\'),
    \'brand\' => trim($_POST[\'brand\'] ?? \'\'),
    \'model\' => trim($_POST[\'model\'] ?? \'\'),
    \'tag\' => trim($_POST[\'tag\'] ?? \'\'),
    \'department\' => trim($_POST[\'department\'] ?? \'\'),
    \'status\' => trim($_POST[\'status\'] ?? \'ใช้งาน\'),
    \'hostname\' => trim($_POST[\'hostname\'] ?? \'\'),
    \'operating_system\' => trim($_POST[\'operating_system\'] ?? \'\'),
    \'serial_number\' => trim($_POST[\'serial_number\'] ?? \'\'),
    \'asset_id\' => trim($_POST[\'asset_id\'] ?? \'\'),
    \'warranty_expire\' => !empty($_POST[\'warranty_expire\']) ? $_POST[\'warranty_expire\'] : null,
    \'description\' => trim($_POST[\'description\'] ?? \'\'),
    \'set_name\' => trim($_POST[\'set_name\'] ?? \'\'),
    \'person_added\' => getCurrentUsername(),
    \'person_modified\' => getCurrentUsername()
];

// SQL INSERT
$sql = "INSERT INTO assets (type, brand, model, tag, department, status, hostname, operating_system, serial_number, asset_id, warranty_expire, description, set_name, person_added, person_modified, date_added, date_modified) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";

$stmt = $pdo->prepare($sql);
$result = $stmt->execute([
    $data[\'type\'], $data[\'brand\'], $data[\'model\'], $data[\'tag\'], $data[\'department\'],
    $data[\'status\'], $data[\'hostname\'], $data[\'operating_system\'], $data[\'serial_number\'],
    $data[\'asset_id\'], $data[\'warranty_expire\'], $data[\'description\'], $data[\'set_name\'],
    $data[\'person_added\'], $data[\'person_modified\']
]);
');
echo "</div>";

// ทดสอบ INSERT
if ($_POST && isset($_POST['test_insert'])) {
    echo "<h3>🧪 ทดสอบ INSERT:</h3>";
    
    try {
        $sql = "INSERT INTO assets (type, brand, model, tag, department, status, hostname, operating_system, serial_number, asset_id, warranty_expire, description, set_name, person_added, person_modified, date_added, date_modified) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";
        
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([
            'Desktop',
            'Dell',
            'OptiPlex 7090',
            'TAG-TEST',
            'IT Department',
            'ใช้งาน',
            'PC-TEST',
            'Windows 10',
            'SN123456789',
            'ASSET-TEST',
            '2025-12-31',
            'Test Asset Description',
            'Test Set',
            'admin',
            'admin'
        ]);
        
        if ($result) {
            $newId = $pdo->lastInsertId();
            echo "<p style='color: green;'>✅ INSERT สำเร็จ! ID: $newId</p>";
            
            // แสดงข้อมูลที่เพิ่ม
            $checkStmt = $pdo->prepare("SELECT * FROM assets WHERE id = ?");
            $checkStmt->execute([$newId]);
            $newRecord = $checkStmt->fetch();
            
            echo "<h4>ข้อมูลที่เพิ่ม:</h4>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%; background: white;'>";
            echo "<tr style='background: #f8f9fa;'><th style='padding: 8px;'>Field</th><th style='padding: 8px;'>Value</th></tr>";
            foreach ($newRecord as $key => $value) {
                if (!is_numeric($key)) {
                    echo "<tr><td style='padding: 8px;'><strong>$key</strong></td><td style='padding: 8px;'>$value</td></tr>";
                }
            }
            echo "</table>";
            
            // ลบข้อมูลทดสอบ
            $deleteStmt = $pdo->prepare("DELETE FROM assets WHERE id = ?");
            $deleteStmt->execute([$newId]);
            echo "<p style='color: blue;'>🗑️ ลบข้อมูลทดสอบแล้ว</p>";
            
        } else {
            echo "<p style='color: red;'>❌ INSERT ไม่สำเร็จ</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ INSERT error: " . $e->getMessage() . "</p>";
        echo "<p style='color: red;'>Error Code: " . $e->getCode() . "</p>";
    }
}

?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ตรวจสอบโครงสร้างฐานข้อมูล Assets</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h2 {
            background: #6f42c1;
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        button {
            background: #28a745;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #218838;
        }
    </style>
</head>
<body>

<div class="test-section">
    <h3>🧪 ทดสอบ INSERT ตามโครงสร้างจริง</h3>
    <p>ทดสอบการเพิ่มข้อมูลตามโครงสร้างตารางที่มีอยู่</p>
    <form method="POST">
        <input type="hidden" name="test_insert" value="1">
        <button type="submit">🧪 ทดสอบ INSERT</button>
    </form>
</div>

<div style="margin: 20px 0;">
    <a href="add_asset.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">➕ ไปหน้าเพิ่ม Asset</a>
    <a href="index.php" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">🏠 กลับหน้าหลัก</a>
</div>

</body>
</html>
