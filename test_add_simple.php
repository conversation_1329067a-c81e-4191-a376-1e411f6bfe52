<?php
// ทดสอบการเพิ่ม Asset แบบง่าย
require_once 'includes/auth.php';

// ตรวจสอบการล็อกอิน
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

echo "<h2>🧪 ทดสอบการเพิ่ม Asset แบบง่าย</h2>";

// แสดงข้อมูลผู้ใช้
echo "<h3>ข้อมูลผู้ใช้:</h3>";
echo "<p>Username: " . ($_SESSION['username'] ?? 'N/A') . "</p>";
echo "<p>Role: " . ($_SESSION['role'] ?? 'N/A') . "</p>";
echo "<p>Is Admin: " . ($auth->isAdmin() ? 'Yes' : 'No') . "</p>";

if (!$auth->isAdmin()) {
    echo "<p style='color: red;'>❌ ต้องเป็น Admin เท่านั้น</p>";
    exit;
}

// ตรวจสอบโครงสร้างตาราง
echo "<h3>โครงสร้างตาราง assets:</h3>";
try {
    $stmt = $pdo->query("DESCRIBE assets");
    $columns = $stmt->fetchAll();
    
    echo "<ul>";
    foreach ($columns as $column) {
        echo "<li>{$column['Field']} ({$column['Type']})</li>";
    }
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

// ทดสอบการเพิ่มข้อมูล
if ($_POST && isset($_POST['test_insert'])) {
    echo "<h3>ผลการทดสอบ:</h3>";
    
    try {
        // ทดสอบ SQL แบบง่าย
        $sql = "INSERT INTO assets (type, brand, status) VALUES (?, ?, ?)";
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute(['Desktop', 'Dell', 'ใช้งาน']);
        
        if ($result) {
            $newId = $pdo->lastInsertId();
            echo "<p style='color: green;'>✅ เพิ่มข้อมูลสำเร็จ! ID: $newId</p>";
            
            // ลบทันที
            $deleteStmt = $pdo->prepare("DELETE FROM assets WHERE id = ?");
            $deleteResult = $deleteStmt->execute([$newId]);
            
            if ($deleteResult) {
                echo "<p style='color: blue;'>🗑️ ลบข้อมูลทดสอบแล้ว</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ เพิ่มข้อมูลไม่สำเร็จ</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    }
}

// ทดสอบ AssetManager
if ($_POST && isset($_POST['test_manager'])) {
    echo "<h3>ทดสอบ AssetManager:</h3>";
    
    try {
        require_once 'includes/functions.php';
        $assetManager = new AssetManager($pdo);
        
        $testData = [
            'type' => 'Desktop',
            'brand' => 'Dell',
            'model' => 'Test',
            'tag' => 'TEST',
            'department' => 'IT',
            'status' => 'ใช้งาน',
            'hostname' => 'TEST-PC',
            'operating_system' => 'Windows 10',
            'serial_number' => 'TEST123',
            'asset_id' => 'ASSET-TEST',
            'warranty_expire' => null,
            'description' => 'Test',
            'asset_set' => 'Test Set',
            'created_by' => getCurrentUsername(),
            'updated_by' => getCurrentUsername()
        ];
        
        $result = $assetManager->createAsset($testData);
        
        if ($result) {
            echo "<p style='color: green;'>✅ AssetManager สำเร็จ! ID: $result</p>";
            
            // ลบทันที
            $assetManager->deleteAsset($result, getCurrentUsername());
            echo "<p style='color: blue;'>🗑️ ลบข้อมูลทดสอบแล้ว</p>";
        } else {
            echo "<p style='color: red;'>❌ AssetManager ไม่สำเร็จ</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ AssetManager Error: " . $e->getMessage() . "</p>";
    }
}

?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบการเพิ่ม Asset</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h2 {
            background: #007bff;
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        button {
            background: #28a745;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background: #218838;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>

<div class="test-section">
    <h3>🧪 ทดสอบการเพิ่มข้อมูลโดยตรง</h3>
    <p>ทดสอบ SQL INSERT แบบง่าย</p>
    <form method="POST">
        <input type="hidden" name="test_insert" value="1">
        <button type="submit">ทดสอบ SQL INSERT</button>
    </form>
</div>

<div class="test-section">
    <h3>🔧 ทดสอบ AssetManager</h3>
    <p>ทดสอบการใช้ AssetManager class</p>
    <form method="POST">
        <input type="hidden" name="test_manager" value="1">
        <button type="submit">ทดสอบ AssetManager</button>
    </form>
</div>

<div class="warning">
    <strong>⚠️ หมายเหตุ:</strong> ข้อมูลทดสอบจะถูกลบทันทีหลังจากเพิ่มสำเร็จ
</div>

<div style="margin: 20px 0;">
    <a href="add_asset.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">➕ ไปหน้าเพิ่ม Asset</a>
    <a href="index.php" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">🏠 กลับหน้าหลัก</a>
</div>

</body>
</html>
