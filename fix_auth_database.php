<?php
// ไฟล์สำหรับแก้ไขปัญหาฐานข้อมูล Authentication
// รันไฟล์นี้เพียงครั้งเดียวแล้วลบทิ้ง

require_once 'config/database.php';

try {
    echo "<h2>กำลังแก้ไขฐานข้อมูลสำหรับระบบ Authentication...</h2>";
    
    // ตรวจสอบว่าตาราง users มีอยู่หรือไม่
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    $tableExists = $stmt->rowCount() > 0;
    
    if ($tableExists) {
        echo "<p style='color: blue;'>📋 ตาราง users มีอยู่แล้ว - กำลังตรวจสอบโครงสร้าง...</p>";
        
        // ตรวจสอบ columns ที่มีอยู่
        $stmt = $pdo->query("DESCRIBE users");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $requiredColumns = ['password', 'role', 'status', 'last_login', 'updated_date'];
        $missingColumns = array_diff($requiredColumns, $columns);
        
        if (!empty($missingColumns)) {
            echo "<p style='color: orange;'>⚠️ พบ columns ที่ขาดหายไป: " . implode(', ', $missingColumns) . "</p>";
            
            // เพิ่ม columns ที่ขาดหายไป
            foreach ($missingColumns as $column) {
                switch ($column) {
                    case 'password':
                        $pdo->exec("ALTER TABLE users ADD COLUMN password VARCHAR(255) NOT NULL DEFAULT ''");
                        echo "<p style='color: green;'>✅ เพิ่ม column: password</p>";
                        break;
                    case 'role':
                        $pdo->exec("ALTER TABLE users ADD COLUMN role ENUM('Admin', 'User') DEFAULT 'User'");
                        echo "<p style='color: green;'>✅ เพิ่ม column: role</p>";
                        break;
                    case 'status':
                        $pdo->exec("ALTER TABLE users ADD COLUMN status ENUM('Active', 'Inactive') DEFAULT 'Active'");
                        echo "<p style='color: green;'>✅ เพิ่ม column: status</p>";
                        break;
                    case 'last_login':
                        $pdo->exec("ALTER TABLE users ADD COLUMN last_login DATETIME NULL");
                        echo "<p style='color: green;'>✅ เพิ่ม column: last_login</p>";
                        break;
                    case 'updated_date':
                        $pdo->exec("ALTER TABLE users ADD COLUMN updated_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");
                        echo "<p style='color: green;'>✅ เพิ่ม column: updated_date</p>";
                        break;
                }
            }
            
            // เพิ่ม indexes
            try {
                $pdo->exec("ALTER TABLE users ADD INDEX idx_username (username)");
                echo "<p style='color: green;'>✅ เพิ่ม index: idx_username</p>";
            } catch (PDOException $e) {
                if (strpos($e->getMessage(), 'Duplicate key name') === false) {
                    echo "<p style='color: orange;'>⚠️ ไม่สามารถเพิ่ม index username: " . $e->getMessage() . "</p>";
                }
            }
            
            try {
                $pdo->exec("ALTER TABLE users ADD INDEX idx_role (role)");
                echo "<p style='color: green;'>✅ เพิ่ม index: idx_role</p>";
            } catch (PDOException $e) {
                if (strpos($e->getMessage(), 'Duplicate key name') === false) {
                    echo "<p style='color: orange;'>⚠️ ไม่สามารถเพิ่ม index role: " . $e->getMessage() . "</p>";
                }
            }
            
            try {
                $pdo->exec("ALTER TABLE users ADD INDEX idx_status (status)");
                echo "<p style='color: green;'>✅ เพิ่ม index: idx_status</p>";
            } catch (PDOException $e) {
                if (strpos($e->getMessage(), 'Duplicate key name') === false) {
                    echo "<p style='color: orange;'>⚠️ ไม่สามารถเพิ่ม index status: " . $e->getMessage() . "</p>";
                }
            }
        } else {
            echo "<p style='color: green;'>✅ โครงสร้างตารางครบถ้วนแล้ว</p>";
        }
        
        // อัพเดทข้อมูลผู้ใช้ที่มีอยู่
        $stmt = $pdo->query("SELECT id, username, password FROM users");
        $existingUsers = $stmt->fetchAll();
        
        foreach ($existingUsers as $user) {
            // ถ้ายังไม่มีรหัสผ่านหรือรหัสผ่านว่าง
            if (empty($user['password'])) {
                $defaultPassword = password_hash('admin123', PASSWORD_DEFAULT);
                $pdo->prepare("UPDATE users SET password = ?, role = 'Admin', status = 'Active' WHERE id = ?")
                    ->execute([$defaultPassword, $user['id']]);
                echo "<p style='color: blue;'>🔑 อัพเดทรหัสผ่านสำหรับ user: {$user['username']} (password: admin123)</p>";
            }
        }
        
    } else {
        echo "<p style='color: blue;'>📋 ไม่พบตาราง users - กำลังสร้างใหม่...</p>";
        
        // สร้างตาราง users ใหม่
        $sql = "CREATE TABLE users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            full_name VARCHAR(100) NOT NULL,
            email VARCHAR(100),
            role ENUM('Admin', 'User') DEFAULT 'User',
            status ENUM('Active', 'Inactive') DEFAULT 'Active',
            last_login DATETIME NULL,
            created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_username (username),
            INDEX idx_role (role),
            INDEX idx_status (status)
        )";
        
        $pdo->exec($sql);
        echo "<p style='color: green;'>✅ สร้างตาราง users สำเร็จ</p>";
    }
    
    // เพิ่มผู้ใช้ตัวอย่าง (ถ้ายังไม่มี)
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = ?");
    
    // ตรวจสอบ admin
    $stmt->execute(['admin']);
    if ($stmt->fetchColumn() == 0) {
        $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $pdo->prepare("INSERT INTO users (username, password, full_name, email, role) VALUES (?, ?, ?, ?, ?)")
            ->execute(['admin', $hashedPassword, 'Administrator', '<EMAIL>', 'Admin']);
        echo "<p style='color: green;'>✅ เพิ่มผู้ใช้: admin (password: admin123)</p>";
    }
    
    // ตรวจสอบ user1
    $stmt->execute(['user1']);
    if ($stmt->fetchColumn() == 0) {
        $hashedPassword = password_hash('user123', PASSWORD_DEFAULT);
        $pdo->prepare("INSERT INTO users (username, password, full_name, email, role) VALUES (?, ?, ?, ?, ?)")
            ->execute(['user1', $hashedPassword, 'John Doe', '<EMAIL>', 'User']);
        echo "<p style='color: green;'>✅ เพิ่มผู้ใช้: user1 (password: user123)</p>";
    }
    
    // แสดงโครงสร้างตารางหลังการแก้ไข
    echo "<h3>โครงสร้างตาราง users หลังการแก้ไข:</h3>";
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll();
    
    echo "<table style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='border: 1px solid #ddd; padding: 10px;'>Field</th>";
    echo "<th style='border: 1px solid #ddd; padding: 10px;'>Type</th>";
    echo "<th style='border: 1px solid #ddd; padding: 10px;'>Null</th>";
    echo "<th style='border: 1px solid #ddd; padding: 10px;'>Key</th>";
    echo "<th style='border: 1px solid #ddd; padding: 10px;'>Default</th>";
    echo "</tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$column['Field']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$column['Type']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$column['Null']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$column['Key']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$column['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // แสดงรายการผู้ใช้
    echo "<h3>รายการผู้ใช้ในระบบ:</h3>";
    $stmt = $pdo->query("SELECT id, username, full_name, email, role, status, created_date FROM users ORDER BY id");
    $users = $stmt->fetchAll();
    
    echo "<table style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='border: 1px solid #ddd; padding: 10px;'>ID</th>";
    echo "<th style='border: 1px solid #ddd; padding: 10px;'>Username</th>";
    echo "<th style='border: 1px solid #ddd; padding: 10px;'>Full Name</th>";
    echo "<th style='border: 1px solid #ddd; padding: 10px;'>Email</th>";
    echo "<th style='border: 1px solid #ddd; padding: 10px;'>Role</th>";
    echo "<th style='border: 1px solid #ddd; padding: 10px;'>Status</th>";
    echo "</tr>";
    
    foreach ($users as $user) {
        echo "<tr>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$user['id']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$user['username']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$user['full_name']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$user['email']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$user['role']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$user['status']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2 style='color: green;'>🎉 การแก้ไขเสร็จสมบูรณ์!</h2>";
    echo "<p><strong>ตอนนี้ระบบ Authentication พร้อมใช้งานแล้ว</strong></p>";
    echo "<ul>";
    echo "<li>✅ ตาราง users มีโครงสร้างครบถ้วน</li>";
    echo "<li>✅ มีผู้ใช้ตัวอย่างพร้อมใช้งาน</li>";
    echo "<li>✅ รหัสผ่านถูกเข้ารหัสอย่างปลอดภัย</li>";
    echo "</ul>";
    echo "<p><a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>ไปหน้าล็อกอิน</a></p>";
    echo "<p style='color: red; margin-top: 20px;'><strong>หมายเหตุ:</strong> กรุณาลบไฟล์ fix_auth_database.php หลังจากรันเสร็จแล้ว</p>";
    
} catch (PDOException $e) {
    echo "<h2 style='color: red;'>❌ เกิดข้อผิดพลาด</h2>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<p>กรุณาตรวจสอบการเชื่อมต่อฐานข้อมูลและลองใหม่</p>";
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>แก้ไขฐานข้อมูล Authentication - Asset Management System</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- เนื้อหาจะแสดงจาก PHP ด้านบน -->
    </div>
</body>
</html>
