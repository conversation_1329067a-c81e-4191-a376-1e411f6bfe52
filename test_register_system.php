<?php
// ทดสอบระบบ Register
require_once 'config/database.php';
require_once 'includes/auth.php';

echo "<h2>ทดสอบระบบ Register</h2>";

try {
    // ตรวจสอบโครงสร้างฐานข้อมูล
    echo "<h3>1. ตรวจสอบโครงสร้างฐานข้อมูล</h3>";
    
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: red;'>❌ ไม่พบตาราง users</p>";
        exit;
    }
    
    $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'password'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: red;'>❌ ไม่พบ password column</p>";
        exit;
    }
    
    echo "<p style='color: green;'>✅ โครงสร้างฐานข้อมูลพร้อมใช้งาน</p>";
    
    // ทดสอบการสร้างผู้ใช้
    echo "<h3>2. ทดสอบการสร้างผู้ใช้</h3>";
    
    $testUsers = [
        [
            'username' => 'testuser_' . time(),
            'password' => 'testpass123',
            'full_name' => 'Test User',
            'email' => '<EMAIL>',
            'role' => 'User',
            'status' => 'Active'
        ],
        [
            'username' => 'testadmin_' . time(),
            'password' => 'adminpass123',
            'full_name' => 'Test Admin',
            'email' => '<EMAIL>',
            'role' => 'Admin',
            'status' => 'Active'
        ]
    ];
    
    $createdUsers = [];
    
    foreach ($testUsers as $userData) {
        echo "<h4>ทดสอบสร้างผู้ใช้: {$userData['username']}</h4>";
        
        $result = $auth->createUser($userData);
        if ($result) {
            echo "<p style='color: green;'>✅ สร้างผู้ใช้สำเร็จ</p>";
            $createdUsers[] = $userData['username'];
            
            // ทดสอบล็อกอิน
            session_start();
            $loginResult = $auth->login($userData['username'], $userData['password']);
            if ($loginResult) {
                echo "<p style='color: green;'>✅ ล็อกอินด้วยผู้ใช้ใหม่สำเร็จ</p>";
                echo "<ul>";
                echo "<li>Username: " . ($_SESSION['username'] ?? 'N/A') . "</li>";
                echo "<li>Full Name: " . ($_SESSION['full_name'] ?? 'N/A') . "</li>";
                echo "<li>Role: " . ($_SESSION['role'] ?? 'N/A') . "</li>";
                echo "</ul>";
            } else {
                echo "<p style='color: red;'>❌ ล็อกอินด้วยผู้ใช้ใหม่ล้มเหลว</p>";
            }
            session_destroy();
        } else {
            echo "<p style='color: red;'>❌ สร้างผู้ใช้ล้มเหลว</p>";
        }
    }
    
    // ทดสอบการตรวจสอบ username ซ้ำ
    echo "<h3>3. ทดสอบการตรวจสอบ username ซ้ำ</h3>";
    if (!empty($createdUsers)) {
        $duplicateUser = [
            'username' => $createdUsers[0], // ใช้ username ที่มีอยู่แล้ว
            'password' => 'newpass123',
            'full_name' => 'Duplicate User',
            'email' => '<EMAIL>',
            'role' => 'User',
            'status' => 'Active'
        ];
        
        $result = $auth->createUser($duplicateUser);
        if (!$result) {
            echo "<p style='color: green;'>✅ ระบบป้องกัน username ซ้ำได้ถูกต้อง</p>";
        } else {
            echo "<p style='color: red;'>❌ ระบบไม่ได้ป้องกัน username ซ้ำ</p>";
        }
    }
    
    // ทดสอบการตรวจสอบข้อมูล
    echo "<h3>4. ทดสอบการตรวจสอบข้อมูล</h3>";
    
    $invalidUsers = [
        [
            'data' => ['username' => 'ab', 'password' => 'test123', 'full_name' => 'Test', 'role' => 'User'],
            'test' => 'Username สั้นเกินไป'
        ],
        [
            'data' => ['username' => 'validuser', 'password' => '123', 'full_name' => 'Test', 'role' => 'User'],
            'test' => 'Password สั้นเกินไป'
        ],
        [
            'data' => ['username' => 'validuser2', 'password' => 'test123', 'full_name' => '', 'role' => 'User'],
            'test' => 'Full name ว่าง'
        ]
    ];
    
    foreach ($invalidUsers as $test) {
        echo "<h4>ทดสอบ: {$test['test']}</h4>";
        
        // จำลองการตรวจสอบข้อมูล
        $username = $test['data']['username'];
        $password = $test['data']['password'];
        $fullName = $test['data']['full_name'];
        
        $isValid = true;
        $errorMessage = '';
        
        if (strlen($username) < 3) {
            $isValid = false;
            $errorMessage = 'Username ต้องมีอย่างน้อย 3 ตัวอักษร';
        } elseif (strlen($password) < 6) {
            $isValid = false;
            $errorMessage = 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร';
        } elseif (empty($fullName)) {
            $isValid = false;
            $errorMessage = 'กรุณากรอกชื่อ-นามสกุล';
        }
        
        if (!$isValid) {
            echo "<p style='color: green;'>✅ ตรวจพบข้อผิดพลาด: $errorMessage</p>";
        } else {
            echo "<p style='color: red;'>❌ ไม่ได้ตรวจพบข้อผิดพลาด</p>";
        }
    }
    
    // แสดงผู้ใช้ที่สร้างขึ้น
    echo "<h3>5. ผู้ใช้ที่สร้างขึ้นในการทดสอบ</h3>";
    if (!empty($createdUsers)) {
        $placeholders = str_repeat('?,', count($createdUsers) - 1) . '?';
        $stmt = $pdo->prepare("SELECT id, username, full_name, email, role, status, created_date FROM users WHERE username IN ($placeholders)");
        $stmt->execute($createdUsers);
        $users = $stmt->fetchAll();
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>Username</th>";
        echo "<th style='padding: 8px;'>Full Name</th>";
        echo "<th style='padding: 8px;'>Email</th>";
        echo "<th style='padding: 8px;'>Role</th>";
        echo "<th style='padding: 8px;'>Status</th>";
        echo "</tr>";
        
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>{$user['id']}</td>";
            echo "<td style='padding: 8px;'>{$user['username']}</td>";
            echo "<td style='padding: 8px;'>{$user['full_name']}</td>";
            echo "<td style='padding: 8px;'>{$user['email']}</td>";
            echo "<td style='padding: 8px;'>{$user['role']}</td>";
            echo "<td style='padding: 8px;'>{$user['status']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // ทำความสะอาด - ลบผู้ใช้ทดสอบ
    echo "<h3>6. ทำความสะอาด</h3>";
    if (!empty($createdUsers)) {
        $placeholders = str_repeat('?,', count($createdUsers) - 1) . '?';
        $stmt = $pdo->prepare("DELETE FROM users WHERE username IN ($placeholders)");
        $stmt->execute($createdUsers);
        echo "<p style='color: blue;'>🗑️ ลบผู้ใช้ทดสอบ " . count($createdUsers) . " คนแล้ว</p>";
    }
    
    echo "<h3>7. สรุปผลการทดสอบ</h3>";
    echo "<p style='color: green;'>✅ ระบบ Register ทำงานได้ถูกต้อง</p>";
    echo "<ul>";
    echo "<li>✅ สร้างผู้ใช้ใหม่ได้</li>";
    echo "<li>✅ เข้ารหัสรหัสผ่านอย่างปลอดภัย</li>";
    echo "<li>✅ ตรวจสอบ username ซ้ำ</li>";
    echo "<li>✅ ตรวจสอบความถูกต้องของข้อมูล</li>";
    echo "<li>✅ ล็อกอินด้วยผู้ใช้ใหม่ได้</li>";
    echo "<li>✅ กำหนด role และ status ได้</li>";
    echo "</ul>";
    
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='register.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>ทดสอบหน้า Register</a>";
    echo "<a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>ไปหน้า Login</a>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ เกิดข้อผิดพลาด</h3>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<p style='color: red; margin-top: 20px;'><strong>หมายเหตุ:</strong> กรุณาลบไฟล์ test_register_system.php หลังจากทดสอบเสร็จแล้ว</p>";
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบระบบ Register</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <!-- เนื้อหาจะแสดงจาก PHP ด้านบน -->
</body>
</html>
