<?php
// ทดสอบการเพิ่ม Asset ขั้นสุดท้าย
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'includes/auth.php';

// ตรวจสอบการล็อกอิน
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

echo "<h2>🎯 ทดสอบการเพิ่ม Asset ขั้นสุดท้าย</h2>";

// ตรวจสอบการเชื่อมต่อฐานข้อมูล
try {
    $stmt = $pdo->query("SELECT 1");
    echo "<p style='color: green;'>✅ เชื่อมต่อฐานข้อมูลสำเร็จ</p>";
} catch (Exception $e) {
    die("<p style='color: red;'>❌ ไม่สามารถเชื่อมต่อฐานข้อมูล: " . $e->getMessage() . "</p>");
}

// แสดงข้อมูลผู้ใช้
echo "<h3>ข้อมูลผู้ใช้:</h3>";
echo "<p><strong>Username:</strong> " . ($_SESSION['username'] ?? 'N/A') . "</p>";
echo "<p><strong>Role:</strong> " . ($_SESSION['role'] ?? 'N/A') . "</p>";
echo "<p><strong>Is Admin:</strong> " . ($auth->isAdmin() ? 'Yes' : 'No') . "</p>";

if (!$auth->isAdmin()) {
    echo "<p style='color: red;'>❌ ต้องเป็น Admin เท่านั้น</p>";
    exit;
}

// ตรวจสอบโครงสร้างตาราง
echo "<h3>โครงสร้างตาราง assets:</h3>";
try {
    $stmt = $pdo->query("DESCRIBE assets");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<p><strong>คอลัมน์ที่มีอยู่:</strong> " . implode(', ', $columns) . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ ไม่สามารถดูโครงสร้างตาราง: " . $e->getMessage() . "</p>";
}

// ทดสอบการเพิ่ม Asset
if ($_POST && isset($_POST['add_asset'])) {
    echo "<h3>🚀 กำลังเพิ่ม Asset...</h3>";
    
    // รับข้อมูลจากฟอร์ม
    $data = [
        'type' => trim($_POST['type'] ?? ''),
        'brand' => trim($_POST['brand'] ?? ''),
        'model' => trim($_POST['model'] ?? ''),
        'tag' => trim($_POST['tag'] ?? ''),
        'department' => trim($_POST['department'] ?? ''),
        'status' => trim($_POST['status'] ?? 'ใช้งาน'),
        'hostname' => trim($_POST['hostname'] ?? ''),
        'operating_system' => trim($_POST['operating_system'] ?? ''),
        'serial_number' => trim($_POST['serial_number'] ?? ''),
        'asset_id' => trim($_POST['asset_id'] ?? ''),
        'warranty_expire' => !empty($_POST['warranty_expire']) ? $_POST['warranty_expire'] : null,
        'description' => trim($_POST['description'] ?? ''),
        'set_name' => trim($_POST['set_name'] ?? ''),
        'created_by' => getCurrentUsername(),
        'updated_by' => getCurrentUsername()
    ];
    
    echo "<h4>ข้อมูลที่จะเพิ่ม:</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'><th style='padding: 8px;'>Field</th><th style='padding: 8px;'>Value</th></tr>";
    foreach ($data as $key => $value) {
        echo "<tr><td style='padding: 8px;'><strong>$key</strong></td><td style='padding: 8px;'>$value</td></tr>";
    }
    echo "</table>";
    
    if (empty($data['type'])) {
        echo "<p style='color: red;'>❌ Type เป็นฟิลด์ที่จำเป็น</p>";
    } else {
        try {
            // SQL INSERT ตรงตามโครงสร้างตารางจริง
            $sql = "INSERT INTO assets (type, brand, model, tag, department, status, hostname, operating_system, serial_number, asset_id, warranty_expire, description, set_name, created_by, updated_by, created_date, updated_date)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";

            echo "<h4>SQL ที่ใช้:</h4>";
            echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 0.9rem;'>";
            echo htmlspecialchars($sql);
            echo "</div>";

            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([
                $data['type'],
                $data['brand'],
                $data['model'],
                $data['tag'],
                $data['department'],
                $data['status'],
                $data['hostname'],
                $data['operating_system'],
                $data['serial_number'],
                $data['asset_id'],
                $data['warranty_expire'],
                $data['description'],
                $data['set_name'],
                $data['created_by'],
                $data['updated_by']
            ]);

            if ($result) {
                $newId = $pdo->lastInsertId();
                echo "<p style='color: green;'>✅ เพิ่ม Asset สำเร็จ! ID: $newId</p>";
                
                // แสดงข้อมูลที่เพิ่ม
                $checkStmt = $pdo->prepare("SELECT * FROM assets WHERE id = ?");
                $checkStmt->execute([$newId]);
                $newAsset = $checkStmt->fetch();
                
                echo "<h4>ข้อมูล Asset ที่เพิ่มแล้ว:</h4>";
                echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
                echo "<tr style='background: #f8f9fa;'><th style='padding: 8px;'>Field</th><th style='padding: 8px;'>Value</th></tr>";
                foreach ($newAsset as $key => $value) {
                    if (!is_numeric($key)) {
                        echo "<tr><td style='padding: 8px;'><strong>$key</strong></td><td style='padding: 8px;'>$value</td></tr>";
                    }
                }
                echo "</table>";
                
                echo "<p style='color: blue;'>ℹ️ Asset ถูกเพิ่มในระบบแล้ว</p>";
                
            } else {
                echo "<p style='color: red;'>❌ ไม่สามารถเพิ่ม Asset ได้</p>";
            }
            
        } catch (PDOException $e) {
            echo "<p style='color: red;'>❌ ข้อผิดพลาดฐานข้อมูล: " . $e->getMessage() . "</p>";
            echo "<p style='color: red;'>Error Code: " . $e->getCode() . "</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ ข้อผิดพลาด: " . $e->getMessage() . "</p>";
        }
    }
}

?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบการเพิ่ม Asset ขั้นสุดท้าย</title>
    <style>
        body {
            font-family: 'TH Sarabun New', Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h2 {
            background: #007bff;
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        .form-section {
            background: white;
            padding: 25px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
        }
        .form-group {
            flex: 1;
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 16px;
        }
        button {
            background: #28a745;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
        }
        button:hover {
            background: #218838;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
        }
        th, td {
            padding: 10px;
            text-align: left;
            border: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
    </style>
</head>
<body>

<div class="form-section">
    <h3>🎯 เพิ่ม Asset ใหม่</h3>
    <form method="POST">
        <div class="form-row">
            <div class="form-group">
                <label for="type">Type * (จำเป็น)</label>
                <select id="type" name="type" required>
                    <option value="">เลือก Type</option>
                    <option value="Desktop">Desktop</option>
                    <option value="Laptop">Laptop</option>
                    <option value="Monitor">Monitor</option>
                    <option value="All-in-one">All-in-one</option>
                    <option value="Multifunction Laser Printer">Multifunction Laser Printer</option>
                    <option value="Barcode Printer">Barcode Printer</option>
                    <option value="Barcode Scanner">Barcode Scanner</option>
                    <option value="Tablet">Tablet</option>
                    <option value="UPS">UPS</option>
                    <option value="Queue">Queue</option>
                    <option value="IP Phone">IP Phone</option>
                    <option value="Teleconference">Teleconference</option>
                    <option value="Switch">Switch</option>
                    <option value="Access Point">Access Point</option>
                    <option value="Peripheral">Peripheral</option>
                </select>
            </div>
            <div class="form-group">
                <label for="brand">Brand</label>
                <select id="brand" name="brand">
                    <option value="">เลือก Brand</option>
                    <option value="Dell">Dell</option>
                    <option value="Lenovo">Lenovo</option>
                    <option value="Microsoft">Microsoft</option>
                    <option value="Apple">Apple</option>
                    <option value="Zebra">Zebra</option>
                    <option value="HP">HP</option>
                    <option value="Philips">Philips</option>
                    <option value="Acer">Acer</option>
                    <option value="LG">LG</option>
                    <option value="Cisco">Cisco</option>
                </select>
            </div>
        </div>

        <div class="form-row">
            <div class="form-group">
                <label for="model">Model</label>
                <input type="text" id="model" name="model">
            </div>
            <div class="form-group">
                <label for="tag">Tag</label>
                <input type="text" id="tag" name="tag">
            </div>
        </div>

        <div class="form-row">
            <div class="form-group">
                <label for="department">Department</label>
                <input type="text" id="department" name="department">
            </div>
            <div class="form-group">
                <label for="status">Status</label>
                <select id="status" name="status">
                    <option value="ใช้งาน">ใช้งาน</option>
                    <option value="ชำรุด">ชำรุด</option>
                    <option value="สำรอง">สำรอง</option>
                </select>
            </div>
        </div>

        <div class="form-row">
            <div class="form-group">
                <label for="hostname">Hostname</label>
                <input type="text" id="hostname" name="hostname">
            </div>
            <div class="form-group">
                <label for="operating_system">Operating System</label>
                <select id="operating_system" name="operating_system">
                    <option value="">เลือก Operating System</option>
                    <option value="Windows 7">Windows 7</option>
                    <option value="Windows 10">Windows 10</option>
                    <option value="Windows 11">Windows 11</option>
                    <option value="MacOS">MacOS</option>
                </select>
            </div>
        </div>

        <div class="form-row">
            <div class="form-group">
                <label for="serial_number">Serial Number</label>
                <input type="text" id="serial_number" name="serial_number">
            </div>
            <div class="form-group">
                <label for="asset_id">Asset ID</label>
                <input type="text" id="asset_id" name="asset_id">
            </div>
        </div>

        <div class="form-row">
            <div class="form-group">
                <label for="warranty_expire">Warranty Expire</label>
                <input type="date" id="warranty_expire" name="warranty_expire">
            </div>
            <div class="form-group">
                <label for="set_name">Set</label>
                <input type="text" id="set_name" name="set_name">
            </div>
        </div>

        <div class="form-group">
            <label for="description">Description</label>
            <textarea id="description" name="description" rows="3"></textarea>
        </div>

        <div class="form-group">
            <input type="hidden" name="add_asset" value="1">
            <button type="submit">🎯 เพิ่ม Asset</button>
        </div>
    </form>
</div>

<div style="margin: 20px 0;">
    <a href="check_database_structure.php" style="background: #6f42c1; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">🔍 ตรวจสอบฐานข้อมูล</a>
    <a href="add_asset.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">➕ หน้าเพิ่ม Asset</a>
    <a href="index.php" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">🏠 หน้าหลัก</a>
</div>

</body>
</html>
