<?php
// ทดสอบ Badge ขนาดกะทัดรัด
require_once 'includes/auth.php';

// ตรวจสอบการล็อกอิน
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

echo "<h2>ทดสอบ Badge ขนาดกะทัดรัด</h2>";

try {
    // ตรวจสอบ CSS ที่แก้ไข
    echo "<h3>1. ตรวจสอบ CSS Badge ใหม่</h3>";
    
    if (file_exists('assets/style.css')) {
        $cssContent = file_get_contents('assets/style.css');
        
        $compactBadgeCss = [
            'padding: 4px 10px' => 'Padding ที่กะทัดรัดขึ้น',
            'border-radius: 12px' => 'มุมโค้งที่เหมาะสม',
            'font-size: 0.75rem' => 'ขนาดตัวอักษรที่เล็กลง',
            'min-width: fit-content' => 'ความกว้างพอดีกับเนื้อหา',
            'line-height: 1.2' => 'ความสูงบรรทัดที่กะทัดรัด',
            'justify-content: center' => 'จัดกึ่งกลางเนื้อหา',
            'gap: 3px' => 'ระยะห่างระหว่างไอคอนและข้อความ'
        ];
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>CSS Property</th>";
        echo "<th style='padding: 8px;'>Purpose</th>";
        echo "<th style='padding: 8px;'>Status</th>";
        echo "</tr>";
        
        foreach ($compactBadgeCss as $css => $purpose) {
            $exists = strpos($cssContent, $css) !== false;
            $icon = $exists ? '✅' : '❌';
            $status = $exists ? 'มี' : 'ไม่มี';
            
            echo "<tr>";
            echo "<td style='padding: 8px; font-family: monospace; font-size: 0.9rem;'>$css</td>";
            echo "<td style='padding: 8px;'>$purpose</td>";
            echo "<td style='padding: 8px;'>$icon $status</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // แสดงตัวอย่าง Badge ขนาดต่างๆ
    echo "<h3>2. เปรียบเทียบขนาด Badge</h3>";
    
    echo "<div style='background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1); margin: 20px 0;'>";
    
    echo "<h4>Badge ขนาดเก่า (ใหญ่เกินไป):</h4>";
    echo "<div style='margin: 15px 0;'>";
    echo "<span style='background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white; padding: 6px 14px; border-radius: 20px; font-size: 0.8rem; font-weight: 600; margin-right: 10px;'>Admin</span>";
    echo "<span style='background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 6px 14px; border-radius: 20px; font-size: 0.8rem; font-weight: 600;'>Active</span>";
    echo "</div>";
    
    echo "<h4>Badge ขนาดใหม่ (กะทัดรัด):</h4>";
    echo "<div style='margin: 15px 0;'>";
    echo "<span style='background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white; padding: 4px 10px; border-radius: 12px; font-size: 0.75rem; font-weight: 600; margin-right: 10px; min-width: fit-content; line-height: 1.2;'>Admin</span>";
    echo "<span style='background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 4px 10px; border-radius: 12px; font-size: 0.75rem; font-weight: 600; min-width: fit-content; line-height: 1.2;'>Active</span>";
    echo "</div>";
    
    echo "<h4>Badge สำหรับ User และ Inactive:</h4>";
    echo "<div style='margin: 15px 0;'>";
    echo "<span style='background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 4px 10px; border-radius: 12px; font-size: 0.75rem; font-weight: 600; margin-right: 10px; min-width: fit-content; line-height: 1.2;'>User</span>";
    echo "<span style='background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); color: white; padding: 4px 10px; border-radius: 12px; font-size: 0.75rem; font-weight: 600; min-width: fit-content; line-height: 1.2;'>Inactive</span>";
    echo "</div>";
    
    echo "</div>";
    
    // จำลอง Profile Modal
    echo "<h3>3. จำลอง Profile Modal ด้วย Badge ใหม่</h3>";
    
    echo "<div style='background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1); margin: 20px 0; max-width: 450px;'>";
    
    // Header section
    echo "<div style='text-align: center; margin-bottom: 25px;'>";
    echo "<div style='font-size: 1.5rem; font-weight: 700; color: #2d3748; margin-bottom: 8px;'>admin</div>";
    echo "<div style='display: inline-flex; align-items: center; gap: 5px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 6px 12px; border-radius: 18px; font-size: 0.8rem; font-weight: 600; box-shadow: 0 3px 12px rgba(102, 126, 234, 0.3);'>";
    echo "<span>👑</span><span>Admin</span>";
    echo "</div>";
    echo "</div>";
    
    // Detail rows
    $details = [
        ['🏷️ Username', 'admin'],
        ['👤 ชื่อ-นามสกุล', 'ผู้ดูแลระบบ'],
        ['📧 Email', '<EMAIL>'],
        ['🎭 บทบาท', '<span style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white; padding: 4px 10px; border-radius: 12px; font-size: 0.75rem; font-weight: 600; min-width: fit-content; line-height: 1.2;">Admin</span>'],
        ['🟢 สถานะ', '<span style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 4px 10px; border-radius: 12px; font-size: 0.75rem; font-weight: 600; min-width: fit-content; line-height: 1.2;">Active</span>'],
        ['🕐 เข้าสู่ระบบล่าสุด', '25/12/2024 14:30:15'],
        ['📅 วันที่สร้างบัญชี', '01/01/2024 09:00:00']
    ];
    
    foreach ($details as $detail) {
        echo "<div style='display: flex; justify-content: space-between; align-items: center; padding: 12px 0; border-bottom: 1px solid #f1f5f9;'>";
        echo "<label style='font-weight: 600; color: #475569; margin: 0; min-width: 140px; display: flex; align-items: center; gap: 8px; font-size: 0.95rem;'>";
        echo "<span style='width: 4px; height: 4px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%;'></span>";
        echo $detail[0];
        echo "</label>";
        echo "<span style='color: #1e293b; text-align: right; flex: 1; font-weight: 500; font-size: 0.95rem; display: flex; justify-content: flex-end; align-items: center;'>";
        echo $detail[1];
        echo "</span>";
        echo "</div>";
    }
    
    echo "</div>";
    
    // ทดสอบ API
    echo "<h3>4. ทดสอบข้อมูลจริงจาก API</h3>";
    
    if (file_exists('get_profile_data.php')) {
        ob_start();
        include 'get_profile_data.php';
        $response = ob_get_clean();
        
        $data = json_decode($response, true);
        
        if ($data && isset($data['success']) && $data['success']) {
            echo "<p style='color: green;'>✅ API ทำงานได้ถูกต้อง</p>";
            
            $user = $data['user'];
            echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0;'>";
            echo "<h4>ข้อมูลผู้ใช้ปัจจุบัน:</h4>";
            echo "<div style='display: flex; gap: 15px; align-items: center; flex-wrap: wrap;'>";
            echo "<strong>Username:</strong> " . ($user['username'] ?? 'N/A');
            
            if (isset($user['role'])) {
                $roleClass = $user['role'] === 'Admin' ? 'admin' : '';
                $roleColor = $user['role'] === 'Admin' ? 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)' : 'linear-gradient(135deg, #10b981 0%, #059669 100%)';
                echo "<span style='background: $roleColor; color: white; padding: 4px 10px; border-radius: 12px; font-size: 0.75rem; font-weight: 600; min-width: fit-content; line-height: 1.2;'>" . $user['role'] . "</span>";
            }
            
            if (isset($user['status'])) {
                $statusColor = $user['status'] === 'Inactive' ? 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)' : 'linear-gradient(135deg, #10b981 0%, #059669 100%)';
                echo "<span style='background: $statusColor; color: white; padding: 4px 10px; border-radius: 12px; font-size: 0.75rem; font-weight: 600; min-width: fit-content; line-height: 1.2;'>" . $user['status'] . "</span>";
            }
            echo "</div>";
            echo "</div>";
        }
    }
    
    echo "<h3>5. สรุปการปรับปรุง Badge</h3>";
    
    echo "<div style='background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 15px; margin: 20px 0;'>";
    echo "<h4 style='color: white; margin-top: 0;'>🎯 การปรับปรุงที่ทำ:</h4>";
    echo "<ul style='color: white;'>";
    echo "<li>📏 <strong>Compact Size:</strong> ลด padding จาก 6px 14px เป็น 4px 10px</li>";
    echo "<li>🔤 <strong>Smaller Font:</strong> ลดขนาดตัวอักษรจาก 0.8rem เป็น 0.75rem</li>";
    echo "<li>🎨 <strong>Rounded Corners:</strong> ลด border-radius จาก 20px เป็น 12px</li>";
    echo "<li>📐 <strong>Fit Content:</strong> ใช้ min-width: fit-content</li>";
    echo "<li>📝 <strong>Line Height:</strong> ตั้ง line-height: 1.2 สำหรับความกะทัดรัด</li>";
    echo "<li>🎯 <strong>Center Alignment:</strong> ใช้ justify-content: center</li>";
    echo "<li>✨ <strong>Subtle Shadow:</strong> ลดความเข้มของ shadow</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #c3e6cb;'>";
    echo "<h4 style='color: #155724;'>✅ ผลลัพธ์:</h4>";
    echo "<ul style='color: #155724;'>";
    echo "<li>✅ Badge มีขนาดกะทัดรัดและพอดีกับเนื้อหา</li>";
    echo "<li>✅ ไม่เปลืองพื้นที่ในหน้าจอ</li>";
    echo "<li>✅ ยังคงสวยงามและอ่านง่าย</li>";
    echo "<li>✅ สีสันและ gradient ยังคงสวยงาม</li>";
    echo "<li>✅ จัดตำแหน่งชิดขวาได้ถูกต้อง</li>";
    echo "<li>✅ ใช้งานได้ดีทั้งบนเดสก์ท็อปและมือถือ</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='index.php' style='background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 12px 24px; text-decoration: none; border-radius: 10px; margin-right: 10px; display: inline-flex; align-items: center; gap: 8px;'>🏠 ทดสอบใน index.php</a>";
    echo "<a href='users.php' style='background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 12px 24px; text-decoration: none; border-radius: 10px; display: inline-flex; align-items: center; gap: 8px;'>👥 ทดสอบใน users.php</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ เกิดข้อผิดพลาด</h3>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<p style='color: red; margin-top: 20px;'><strong>หมายเหตุ:</strong> กรุณาลบไฟล์ test_compact_badges.php หลังจากทดสอบเสร็จแล้ว</p>";
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบ Badge ขนาดกะทัดรัด</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        h2 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
        table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            width: 100%;
        }
    </style>
</head>
<body>
    <!-- เนื้อหาจะแสดงจาก PHP ด้านบน -->
</body>
</html>
