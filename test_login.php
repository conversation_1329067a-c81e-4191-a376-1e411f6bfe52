<?php
// ไฟล์ทดสอบระบบ Login
require_once 'config/database.php';

echo "<h2>ทดสอบระบบ Login</h2>";

try {
    // ตรวจสอบตาราง users
    echo "<h3>1. ตรวจสอบตาราง users</h3>";
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✅ ตาราง users มีอยู่</p>";
        
        // ตรวจสอบ columns
        echo "<h4>Columns ในตาราง users:</h4>";
        $stmt = $pdo->query("DESCRIBE users");
        $columns = $stmt->fetchAll();
        
        echo "<ul>";
        foreach ($columns as $column) {
            echo "<li>{$column['Field']} - {$column['Type']}</li>";
        }
        echo "</ul>";
        
        // ตรวจสอบข้อมูลผู้ใช้
        echo "<h4>ข้อมูลผู้ใช้ที่มีอยู่:</h4>";
        $stmt = $pdo->query("SELECT * FROM users");
        $users = $stmt->fetchAll();
        
        if (empty($users)) {
            echo "<p style='color: orange;'>⚠️ ไม่มีข้อมูลผู้ใช้</p>";
            
            // เพิ่มผู้ใช้ตัวอย่าง
            echo "<h4>กำลังเพิ่มผู้ใช้ตัวอย่าง...</h4>";
            
            // ตรวจสอบว่ามี column password หรือไม่
            $hasPassword = false;
            foreach ($columns as $column) {
                if ($column['Field'] === 'password') {
                    $hasPassword = true;
                    break;
                }
            }
            
            if ($hasPassword) {
                $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
                $stmt = $pdo->prepare("INSERT INTO users (username, password, full_name, email) VALUES (?, ?, ?, ?)");
                $stmt->execute(['admin', $hashedPassword, 'Administrator', '<EMAIL>']);
                echo "<p style='color: green;'>✅ เพิ่มผู้ใช้ admin สำเร็จ</p>";
            } else {
                $stmt = $pdo->prepare("INSERT INTO users (username, full_name, email) VALUES (?, ?, ?)");
                $stmt->execute(['admin', 'Administrator', '<EMAIL>']);
                echo "<p style='color: green;'>✅ เพิ่มผู้ใช้ admin สำเร็จ (ไม่มี password column)</p>";
            }
        } else {
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr>";
            foreach ($columns as $column) {
                echo "<th style='padding: 8px;'>{$column['Field']}</th>";
            }
            echo "</tr>";
            
            foreach ($users as $user) {
                echo "<tr>";
                foreach ($columns as $column) {
                    $value = $user[$column['Field']] ?? 'NULL';
                    if ($column['Field'] === 'password' && !empty($value)) {
                        $value = '***hidden***';
                    }
                    echo "<td style='padding: 8px;'>{$value}</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ ไม่พบตาราง users</p>";
        
        // สร้างตาราง users
        echo "<h4>กำลังสร้างตาราง users...</h4>";
        $sql = "CREATE TABLE users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL DEFAULT '',
            full_name VARCHAR(100) NOT NULL,
            email VARCHAR(100),
            role ENUM('Admin', 'User') DEFAULT 'Admin',
            status ENUM('Active', 'Inactive') DEFAULT 'Active',
            created_date DATETIME DEFAULT CURRENT_TIMESTAMP
        )";
        
        $pdo->exec($sql);
        echo "<p style='color: green;'>✅ สร้างตาราง users สำเร็จ</p>";
        
        // เพิ่มผู้ใช้ตัวอย่าง
        $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO users (username, password, full_name, email, role) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute(['admin', $hashedPassword, 'Administrator', '<EMAIL>', 'Admin']);
        echo "<p style='color: green;'>✅ เพิ่มผู้ใช้ admin สำเร็จ</p>";
    }
    
    // ทดสอบการล็อกอิน
    echo "<h3>2. ทดสอบการล็อกอิน</h3>";
    
    require_once 'includes/auth.php';
    
    // ทดสอบล็อกอินด้วย admin/admin123
    if ($auth->login('admin', 'admin123')) {
        echo "<p style='color: green;'>✅ ล็อกอินสำเร็จด้วย admin/admin123</p>";
        echo "<p>Username: " . getCurrentUsername() . "</p>";
        echo "<p>Full Name: " . getCurrentUserFullName() . "</p>";
        echo "<p>Role: " . getCurrentUserRole() . "</p>";
        
        // ล็อกเอาท์
        session_destroy();
        echo "<p style='color: blue;'>🔓 ล็อกเอาท์แล้ว</p>";
    } else {
        echo "<p style='color: red;'>❌ ล็อกอินไม่สำเร็จ</p>";
    }
    
    echo "<h3>3. สรุป</h3>";
    echo "<p style='color: green;'>✅ ระบบพร้อมใช้งาน</p>";
    echo "<p><a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>ไปหน้าล็อกอิน</a></p>";
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ เกิดข้อผิดพลาด</h3>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<p style='color: red; margin-top: 20px;'><strong>หมายเหตุ:</strong> กรุณาลบไฟล์ test_login.php หลังจากทดสอบเสร็จแล้ว</p>";
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบระบบ Login</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <!-- เนื้อหาจะแสดงจาก PHP ด้านบน -->
</body>
</html>
