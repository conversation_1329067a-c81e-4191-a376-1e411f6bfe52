<?php
// Debug Asset Modal
require_once 'includes/auth.php';

// ตรวจสอบการล็อกอิน
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

echo "<h2>🔍 Debug Asset Modal</h2>";

try {
    // ดึง Asset ตัวแรกสำหรับทดสอบ
    $stmt = $pdo->query("SELECT * FROM assets LIMIT 1");
    $testAsset = $stmt->fetch();
    
    if (!$testAsset) {
        echo "<p style='color: red;'>❌ ไม่มี Assets ในระบบสำหรับทดสอบ</p>";
        echo "<p>กรุณาเพิ่ม Asset ก่อนทดสอบ</p>";
        exit;
    }
    
    $testId = $testAsset['id'];
    
    echo "<h3>1. ข้อมูล Asset สำหรับทดสอบ</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<strong>Asset ID:</strong> $testId<br>";
    echo "<strong>Type:</strong> " . ($testAsset['type'] ?? 'N/A') . "<br>";
    echo "<strong>Brand:</strong> " . ($testAsset['brand'] ?? 'N/A') . "<br>";
    echo "<strong>Model:</strong> " . ($testAsset['model'] ?? 'N/A') . "<br>";
    echo "</div>";
    
    echo "<h3>2. ทดสอบ API get_asset_data.php</h3>";
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='get_asset_data.php?id=$testId' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔍 ทดสอบ API</a>";
    echo "</div>";
    
    echo "<h3>3. ทดสอบ Asset Modal แบบ Manual</h3>";
    
    // สร้างปุ่มทดสอบ
    echo "<div style='margin: 20px 0;'>";
    echo "<button onclick='testViewModal($testId)' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; margin-right: 10px; cursor: pointer;'>👁️ ทดสอบ View Modal</button>";
    echo "<button onclick='testEditModal($testId)' style='background: #ffc107; color: black; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>✏️ ทดสอบ Edit Modal</button>";
    echo "</div>";
    
    echo "<h3>4. Debug Console</h3>";
    echo "<div id='debugConsole' style='background: #000; color: #0f0; padding: 15px; border-radius: 8px; font-family: monospace; height: 300px; overflow-y: auto; margin: 20px 0;'>";
    echo "Debug console ready...\n";
    echo "</div>";
    
    echo "<h3>5. Manual API Test</h3>";
    echo "<div style='margin: 20px 0;'>";
    echo "<button onclick='manualApiTest($testId)' style='background: #6f42c1; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>🧪 Manual API Test</button>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ เกิดข้อผิดพลาด</h3>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Asset Modal</title>
    <link rel="stylesheet" href="assets/style.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        h2 {
            background: linear-gradient(135deg, #6f42c1 0%, #5a2d91 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 10px 25px rgba(111, 66, 193, 0.3);
        }
    </style>
</head>
<body>

<!-- Simple View Asset Modal for Testing -->
<div id="viewAssetModal" class="modal" style="display: none;">
    <div class="modal-content asset-modal">
        <div class="modal-header">
            <h2>👁️ ดูรายละเอียด Asset (Debug)</h2>
            <span class="close" onclick="closeViewAssetModal()">&times;</span>
        </div>
        <div class="modal-body">
            <div class="asset-details">
                <div class="detail-row"><label>🆔 ID:</label><span id="view_asset_id">-</span></div>
                <div class="detail-row"><label>📱 ประเภท:</label><span id="view_type">-</span></div>
                <div class="detail-row"><label>🏷️ ยี่ห้อ:</label><span id="view_brand">-</span></div>
                <div class="detail-row"><label>📦 รุ่น:</label><span id="view_model">-</span></div>
                <div class="detail-row"><label>🏷️ Tag:</label><span id="view_tag">-</span></div>
                <div class="detail-row"><label>🏢 แผนก:</label><span id="view_department">-</span></div>
                <div class="detail-row"><label>📊 สถานะ:</label><span id="view_status">-</span></div>
                <div class="detail-row"><label>💻 Hostname:</label><span id="view_hostname">-</span></div>
                <div class="detail-row"><label>💿 OS:</label><span id="view_operating_system">-</span></div>
                <div class="detail-row"><label>🔢 Serial Number:</label><span id="view_serial_number">-</span></div>
                <div class="detail-row"><label>🆔 Asset ID:</label><span id="view_asset_id_display">-</span></div>
                <div class="detail-row"><label>📅 Warranty Expire:</label><span id="view_warranty_expire">-</span></div>
                <div class="detail-row"><label>📝 คำอธิบาย:</label><span id="view_description">-</span></div>
                <div class="detail-row"><label>📦 ชุด:</label><span id="view_set_name">-</span></div>
                <div class="detail-row"><label>📅 วันที่เพิ่ม:</label><span id="view_date_added">-</span></div>
                <div class="detail-row"><label>👤 ผู้เพิ่ม:</label><span id="view_person_added">-</span></div>
                <div class="detail-row"><label>📅 วันที่แก้ไข:</label><span id="view_date_modified">-</span></div>
                <div class="detail-row"><label>👤 ผู้แก้ไข:</label><span id="view_person_modified">-</span></div>
            </div>
        </div>
        <div class="modal-footer">
            <button onclick="closeViewAssetModal()" class="btn btn-secondary">ปิด</button>
        </div>
    </div>
</div>

<!-- Simple Edit Asset Modal for Testing -->
<div id="editAssetModal" class="modal" style="display: none;">
    <div class="modal-content asset-modal">
        <div class="modal-header">
            <h2>✏️ แก้ไข Asset (Debug)</h2>
            <span class="close" onclick="closeEditAssetModal()">&times;</span>
        </div>
        <div class="modal-body">
            <form id="editAssetForm">
                <input type="hidden" id="edit_asset_id_hidden" name="id">
                <div class="form-group">
                    <label for="edit_type">📱 ประเภท</label>
                    <select id="edit_type" name="type" class="form-control">
                        <option value="">เลือก Type</option>
                        <option value="Desktop">Desktop</option>
                        <option value="Laptop">Laptop</option>
                        <option value="Monitor">Monitor</option>
                        <option value="All-in-one">All-in-one</option>
                        <option value="Multifunction Laser Printer">Multifunction Laser Printer</option>
                        <option value="Barcode Printer">Barcode Printer</option>
                        <option value="Barcode Scanner">Barcode Scanner</option>
                        <option value="Tablet">Tablet</option>
                        <option value="UPS">UPS</option>
                        <option value="Queue">Queue</option>
                        <option value="IP Phone">IP Phone</option>
                        <option value="Teleconference">Teleconference</option>
                        <option value="Switch">Switch</option>
                        <option value="Access Point">Access Point</option>
                        <option value="Peripheral">Peripheral</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="edit_brand">🏷️ ยี่ห้อ</label>
                    <select id="edit_brand" name="brand" class="form-control">
                        <option value="">เลือกยี่ห้อ</option>
                        <option value="Dell">Dell</option>
                        <option value="Lenovo">Lenovo</option>
                    </select>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button onclick="closeEditAssetModal()" class="btn btn-secondary">ปิด</button>
        </div>
    </div>
</div>

<script>
    function log(message) {
        const console = document.getElementById('debugConsole');
        const timestamp = new Date().toLocaleTimeString();
        console.innerHTML += `[${timestamp}] ${message}\n`;
        console.scrollTop = console.scrollHeight;
    }

    function testViewModal(assetId) {
        log(`🔍 Testing View Modal for Asset ID: ${assetId}`);
        openViewAssetModal(assetId);
    }

    function testEditModal(assetId) {
        log(`✏️ Testing Edit Modal for Asset ID: ${assetId}`);
        openEditAssetModal(assetId);
    }

    function openViewAssetModal(assetId) {
        log(`📂 Opening View Modal...`);
        document.getElementById('viewAssetModal').style.display = 'block';
        document.body.style.overflow = 'hidden';
        loadAssetData(assetId, 'view');
    }

    function openEditAssetModal(assetId) {
        log(`📂 Opening Edit Modal...`);
        document.getElementById('editAssetModal').style.display = 'block';
        document.body.style.overflow = 'hidden';
        loadAssetData(assetId, 'edit');
    }

    function closeViewAssetModal() {
        log(`❌ Closing View Modal`);
        document.getElementById('viewAssetModal').style.display = 'none';
        document.body.style.overflow = 'auto';
    }

    function closeEditAssetModal() {
        log(`❌ Closing Edit Modal`);
        document.getElementById('editAssetModal').style.display = 'none';
        document.body.style.overflow = 'auto';
    }

    function loadAssetData(assetId, mode) {
        log(`🔄 Loading asset data for ID: ${assetId}, mode: ${mode}`);
        
        fetch(`get_asset_data.php?id=${assetId}`)
            .then(response => {
                log(`📡 Response status: ${response.status}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                log(`📊 Data received: ${JSON.stringify(data, null, 2)}`);
                
                if (data.success) {
                    if (mode === 'view') {
                        populateViewModal(data.asset);
                    } else if (mode === 'edit') {
                        populateEditModal(data.asset);
                    }
                } else {
                    log(`❌ API Error: ${data.message}`);
                    alert('เกิดข้อผิดพลาดในการโหลดข้อมูล Asset: ' + data.message);
                }
            })
            .catch(error => {
                log(`💥 Fetch Error: ${error.message}`);
                alert('เกิดข้อผิดพลาดในการเชื่อมต่อกับเซิร์ฟเวอร์: ' + error.message);
            });
    }

    function populateViewModal(asset) {
        log(`📝 Populating view modal with asset data`);
        
        try {
            document.getElementById('view_asset_id').textContent = asset.id || '-';
            document.getElementById('view_type').textContent = asset.type || '-';
            document.getElementById('view_brand').textContent = asset.brand || '-';
            document.getElementById('view_model').textContent = asset.model || '-';
            document.getElementById('view_tag').textContent = asset.tag || '-';
            document.getElementById('view_department').textContent = asset.department || '-';
            document.getElementById('view_status').textContent = asset.status || '-';
            document.getElementById('view_hostname').textContent = asset.hostname || '-';
            document.getElementById('view_operating_system').textContent = asset.operating_system || '-';
            document.getElementById('view_serial_number').textContent = asset.serial_number || '-';
            document.getElementById('view_asset_id_display').textContent = asset.asset_id || '-';
            document.getElementById('view_warranty_expire').textContent = asset.warranty_expire || '-';
            document.getElementById('view_description').textContent = asset.description || '-';
            document.getElementById('view_set_name').textContent = asset.set_name || '-';
            document.getElementById('view_date_added').textContent = asset.date_added || '-';
            document.getElementById('view_person_added').textContent = asset.person_added || '-';
            document.getElementById('view_date_modified').textContent = asset.date_modified || '-';
            document.getElementById('view_person_modified').textContent = asset.person_modified || '-';
            
            log(`✅ View modal populated successfully`);
        } catch (error) {
            log(`💥 Error populating view modal: ${error.message}`);
        }
    }

    function populateEditModal(asset) {
        log(`📝 Populating edit modal with asset data`);
        
        try {
            document.getElementById('edit_asset_id_hidden').value = asset.id || '';
            document.getElementById('edit_type').value = asset.type || '';
            document.getElementById('edit_brand').value = asset.brand || '';
            
            log(`✅ Edit modal populated successfully`);
        } catch (error) {
            log(`💥 Error populating edit modal: ${error.message}`);
        }
    }

    function manualApiTest(assetId) {
        log(`🧪 Manual API Test for Asset ID: ${assetId}`);
        
        fetch(`get_asset_data.php?id=${assetId}`)
            .then(response => response.text())
            .then(text => {
                log(`📄 Raw response: ${text}`);
                try {
                    const data = JSON.parse(text);
                    log(`📊 Parsed JSON: ${JSON.stringify(data, null, 2)}`);
                } catch (e) {
                    log(`💥 JSON Parse Error: ${e.message}`);
                }
            })
            .catch(error => {
                log(`💥 Manual API Test Error: ${error.message}`);
            });
    }

    // เริ่มต้น debug console
    log(`🚀 Debug Asset Modal initialized`);
</script>

</body>
</html>
