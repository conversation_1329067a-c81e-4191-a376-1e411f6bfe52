<?php
// ทดสอบ add_asset.php ที่แก้ไขแล้ว
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🧪 ทดสอบ add_asset.php ที่แก้ไขแล้ว</h2>";

// เริ่ม session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// ตั้งค่า session
if (!isset($_SESSION['username'])) {
    $_SESSION['username'] = 'admin';
    $_SESSION['role'] = 'Admin';
    $_SESSION['user_id'] = 1;
    echo "<p style='color: blue;'>ℹ️ ตั้งค่า session: admin/Admin</p>";
} else {
    echo "<p style='color: green;'>✅ Session มีอยู่แล้ว: " . $_SESSION['username'] . "/" . $_SESSION['role'] . "</p>";
}

// ตรวจสอบการเชื่อมต่อฐานข้อมูล
echo "<h3>ตรวจสอบการเชื่อมต่อฐานข้อมูล:</h3>";
try {
    $pdo = new PDO("mysql:host=localhost;dbname=asset_management;charset=utf8", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✅ เชื่อมต่อฐานข้อมูลสำเร็จ</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ ไม่สามารถเชื่อมต่อฐานข้อมูล: " . $e->getMessage() . "</p>";
    die();
}

// ตรวจสอบตาราง assets
echo "<h3>ตรวจสอบตาราง assets:</h3>";
try {
    $stmt = $pdo->query("DESCRIBE assets");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "<p style='color: green;'>✅ ตาราง assets มีอยู่</p>";
    echo "<p><strong>คอลัมน์:</strong> " . implode(', ', $columns) . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ ตาราง assets ไม่มี: " . $e->getMessage() . "</p>";
}

// ตรวจสอบไฟล์ add_asset.php
echo "<h3>ตรวจสอบไฟล์ add_asset.php:</h3>";
if (file_exists('add_asset.php')) {
    echo "<p style='color: green;'>✅ ไฟล์ add_asset.php มีอยู่</p>";
    
    // ตรวจสอบ syntax
    $output = [];
    $return_var = 0;
    exec('php -l add_asset.php 2>&1', $output, $return_var);
    
    if ($return_var === 0) {
        echo "<p style='color: green;'>✅ ไฟล์ add_asset.php ไม่มี syntax error</p>";
    } else {
        echo "<p style='color: red;'>❌ ไฟล์ add_asset.php มี syntax error:</p>";
        echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>";
        foreach ($output as $line) {
            echo htmlspecialchars($line) . "<br>";
        }
        echo "</div>";
    }
} else {
    echo "<p style='color: red;'>❌ ไฟล์ add_asset.php ไม่มี</p>";
}

// ทดสอบการส่งข้อมูลไปยัง add_asset.php
echo "<h3>ทดสอบการส่งข้อมูลไปยัง add_asset.php:</h3>";
if ($_POST && isset($_POST['test_submit'])) {
    echo "<h4>ข้อมูลที่ส่ง:</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'><th style='padding: 8px;'>Field</th><th style='padding: 8px;'>Value</th></tr>";
    foreach ($_POST as $key => $value) {
        if ($key !== 'test_submit') {
            echo "<tr><td style='padding: 8px;'><strong>$key</strong></td><td style='padding: 8px;'>$value</td></tr>";
        }
    }
    echo "</table>";
    
    // ลองเรียก add_asset.php ด้วย curl
    echo "<h4>ทดสอบเรียก add_asset.php:</h4>";
    
    $postData = http_build_query($_POST);
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-type: application/x-www-form-urlencoded',
            'content' => $postData
        ]
    ]);
    
    try {
        $result = file_get_contents('http://localhost/asset/add_asset.php', false, $context);
        
        if ($result !== false) {
            echo "<p style='color: green;'>✅ เรียก add_asset.php สำเร็จ</p>";
            
            // ตรวจสอบว่ามีข้อความสำเร็จหรือไม่
            if (strpos($result, 'เพิ่ม Asset สำเร็จ') !== false) {
                echo "<p style='color: green;'>✅ พบข้อความสำเร็จในผลลัพธ์</p>";
            } elseif (strpos($result, 'error') !== false || strpos($result, 'Error') !== false) {
                echo "<p style='color: red;'>❌ พบข้อผิดพลาดในผลลัพธ์</p>";
            } else {
                echo "<p style='color: blue;'>ℹ️ ไม่พบข้อความสำเร็จหรือข้อผิดพลาดที่ชัดเจน</p>";
            }
            
            // แสดงส่วนหนึ่งของผลลัพธ์
            echo "<h5>ส่วนหนึ่งของผลลัพธ์:</h5>";
            echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; max-height: 200px; overflow-y: auto;'>";
            echo htmlspecialchars(substr($result, 0, 1000));
            if (strlen($result) > 1000) {
                echo "...(ตัดทอน)";
            }
            echo "</div>";
            
        } else {
            echo "<p style='color: red;'>❌ ไม่สามารถเรียก add_asset.php ได้</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ ข้อผิดพลาดในการเรียก add_asset.php: " . $e->getMessage() . "</p>";
    }
}

?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบ add_asset.php ที่แก้ไขแล้ว</title>
    <style>
        body {
            font-family: 'TH Sarabun New', Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h2 {
            background: #17a2b8;
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        h3 {
            background: #6c757d;
            color: white;
            padding: 10px;
            border-radius: 5px;
        }
        .form-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 16px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>

<div class="form-section">
    <h3>🧪 ทดสอบส่งข้อมูลไปยัง add_asset.php</h3>
    <form method="POST">
        <div class="form-group">
            <label for="type">Type * (จำเป็น)</label>
            <select id="type" name="type" required>
                <option value="">เลือก Type</option>
                <option value="Desktop">Desktop</option>
                <option value="Laptop">Laptop</option>
                <option value="Monitor">Monitor</option>
                <option value="All-in-one">All-in-one</option>
                <option value="Multifunction Laser Printer">Multifunction Laser Printer</option>
                <option value="Barcode Printer">Barcode Printer</option>
                <option value="Barcode Scanner">Barcode Scanner</option>
                <option value="Tablet">Tablet</option>
                <option value="UPS">UPS</option>
                <option value="Queue">Queue</option>
                <option value="IP Phone">IP Phone</option>
                <option value="Teleconference">Teleconference</option>
                <option value="Switch">Switch</option>
                <option value="Access Point">Access Point</option>
                <option value="Peripheral">Peripheral</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="brand">Brand</label>
            <select id="brand" name="brand">
                <option value="">เลือก Brand</option>
                <option value="-">-</option>
                <option value="Dell">Dell</option>
                <option value="Lenovo">Lenovo</option>
                <option value="Microsoft">Microsoft</option>
                <option value="Apple">Apple</option>
                <option value="Zebra">Zebra</option>
                <option value="HP">HP</option>
                <option value="Philips">Philips</option>
                <option value="Acer">Acer</option>
                <option value="LG">LG</option>
                <option value="Cisco">Cisco</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="model">Model</label>
            <input type="text" id="model" name="model" placeholder="ระบุรุ่น" value="Test Model">
        </div>
        
        <div class="form-group">
            <label for="status">Status</label>
            <select id="status" name="status">
                <option value="ใช้งาน">ใช้งาน</option>
                <option value="ชำรุด">ชำรุด</option>
                <option value="สำรอง">สำรอง</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="description">Description</label>
            <textarea id="description" name="description" rows="3" placeholder="รายละเอียด">Test Asset Description</textarea>
        </div>
        
        <div class="form-group">
            <input type="hidden" name="test_submit" value="1">
            <button type="submit">🧪 ทดสอบส่งข้อมูล</button>
        </div>
    </form>
</div>

<div style="margin: 20px 0;">
    <a href="add_asset.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">➕ ไปหน้าเพิ่ม Asset</a>
    <a href="fix_add_asset.php" style="background: #e74c3c; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">🔧 แก้ไขปัญหา</a>
    <a href="index.php" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">🏠 หน้าหลัก</a>
</div>

</body>
</html>
