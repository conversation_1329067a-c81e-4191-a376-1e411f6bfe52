<?php
session_start();
require_once 'includes/auth.php';
require_once 'config/database.php';
require_once 'includes/functions.php';

// ตรวจสอบการเข้าสู่ระบบ
if (!isLoggedIn()) {
    header('Location: login.php');
    exit;
}

// ตรวจสอบสิทธิ์ Admin
if (!isAdmin()) {
    header('Location: index.php');
    exit;
}

$message = '';
$error = '';
$importResults = [];

// ประมวลผลการอัพโหลด
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['csv_file'])) {
    $file = $_FILES['csv_file'];

    // ตรวจสอบข้อผิดพลาดการอัพโหลด
    if ($file['error'] !== UPLOAD_ERR_OK) {
        $uploadErrors = [
            UPLOAD_ERR_INI_SIZE => 'ไฟล์มีขนาดใหญ่เกินที่กำหนด',
            UPLOAD_ERR_FORM_SIZE => 'ไฟล์มีขนาดใหญ่เกินที่กำหนดในฟอร์ม',
            UPLOAD_ERR_PARTIAL => 'ไฟล์อัพโหลดไม่สมบูรณ์',
            UPLOAD_ERR_NO_FILE => 'ไม่มีไฟล์ที่อัพโหลด',
            UPLOAD_ERR_NO_TMP_DIR => 'ไม่พบโฟลเดอร์ชั่วคราว',
            UPLOAD_ERR_CANT_WRITE => 'ไม่สามารถเขียนไฟล์ได้',
            UPLOAD_ERR_EXTENSION => 'การอัพโหลดถูกหยุดโดย extension'
        ];
        $error = $uploadErrors[$file['error']] ?? 'เกิดข้อผิดพลาดในการอัพโหลดไฟล์';
    } else {
        // ตรวจสอบขนาดไฟล์
        if ($file['size'] > 5 * 1024 * 1024) { // 5MB
            $error = 'ไฟล์มีขนาดใหญ่เกิน 5MB';
        } else {
            // ตรวจสอบประเภทไฟล์
            $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            if ($fileExtension !== 'csv') {
                $error = 'กรุณาอัพโหลดไฟล์ CSV เท่านั้น';
            } else {
                // ตรวจสอบว่าไฟล์มีอยู่จริง
                if (!file_exists($file['tmp_name'])) {
                    $error = 'ไม่พบไฟล์ที่อัพโหลด';
                } else {
                    // ประมวลผลไฟล์ CSV
                    $importResults = processCSVFile($file['tmp_name'], $pdo);

                    if ($importResults['success']) {
                        $message = "นำเข้าข้อมูลสำเร็จ {$importResults['imported']} รายการ";
                        if ($importResults['errors'] > 0) {
                            $message .= " (มีข้อผิดพลาด {$importResults['errors']} รายการ)";
                        }
                    } else {
                        $error = $importResults['message'];
                    }
                }
            }
        }
    }
}

// ฟังก์ชันประมวลผลไฟล์ CSV
function processCSVFile($filePath, $pdo) {
    $results = [
        'success' => false,
        'imported' => 0,
        'errors' => 0,
        'message' => '',
        'details' => []
    ];

    try {
        // อ่านไฟล์ทั้งหมดก่อน
        $content = file_get_contents($filePath);
        if ($content === false) {
            $results['message'] = 'ไม่สามารถอ่านไฟล์ CSV ได้';
            return $results;
        }

        // ตรวจสอบขนาดไฟล์
        if (strlen($content) === 0) {
            $results['message'] = 'ไฟล์ CSV ว่างเปล่า';
            return $results;
        }

        // ลบ BOM ถ้ามี
        $content = str_replace("\xEF\xBB\xBF", '', $content);

        // แยกบรรทัด
        $lines = preg_split('/\r\n|\r|\n/', $content);
        $lines = array_filter($lines, function($line) {
            return trim($line) !== '';
        });

        if (empty($lines)) {
            $results['message'] = 'ไฟล์ CSV ไม่มีข้อมูล';
            return $results;
        }

        if (count($lines) < 2) {
            $results['message'] = 'ไฟล์ CSV ต้องมีอย่างน้อย 2 บรรทัด (header + data)';
            return $results;
        }

        // อ่าน header จากบรรทัดแรก
        $headerLine = array_shift($lines);
        $headers = str_getcsv($headerLine);

        if (!$headers || empty($headers)) {
            $results['message'] = 'ไฟล์ CSV ไม่มี header หรือ header ไม่ถูกต้อง';
            return $results;
        }

        // ทำความสะอาด headers
        $headers = array_map(function($header) {
            return trim($header, " \t\n\r\0\x0B\"'");
        }, $headers);

        // ตรวจสอบ header ที่จำเป็น
        $requiredHeaders = ['Type'];
        foreach ($requiredHeaders as $required) {
            if (!in_array($required, $headers)) {
                $results['message'] = "ไม่พบ header ที่จำเป็น: $required";
                return $results;
            }
        }

        // สร้าง mapping ของ columns
        $columnMap = createColumnMapping($headers);
        $headerCount = count($headers);

        $rowNumber = 1;
        $pdo->beginTransaction();

        foreach ($lines as $line) {
            $rowNumber++;

            // ข้ามบรรทัดว่าง
            if (trim($line) === '') {
                continue;
            }

            // แปลงบรรทัดเป็น array
            $data = str_getcsv($line);

            // ตรวจสอบจำนวน column
            if (count($data) !== $headerCount) {
                // ปรับจำนวน column ให้เท่ากับ header
                if (count($data) < $headerCount) {
                    // เพิ่ม column ว่างถ้าน้อยกว่า
                    $data = array_pad($data, $headerCount, '');
                } else {
                    // ตัด column ส่วนเกินถ้ามากกว่า
                    $data = array_slice($data, 0, $headerCount);
                }
            }

            // ข้ามแถวที่ไม่มีข้อมูล
            if (empty(array_filter($data, function($value) { return trim($value) !== ''; }))) {
                continue;
            }

            $rowResult = processRow($data, $headers, $columnMap, $pdo, $rowNumber);

            if ($rowResult['success']) {
                $results['imported']++;
            } else {
                $results['errors']++;
                $results['details'][] = "แถว $rowNumber: " . $rowResult['error'];
            }
        }

        $pdo->commit();
        $results['success'] = true;

    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollback();
        }
        $results['message'] = 'เกิดข้อผิดพลาด: ' . $e->getMessage();
    }

    return $results;
}

// ฟังก์ชันสร้าง column mapping
function createColumnMapping($headers) {
    $mapping = [
        'Type' => 'type',
        'Brand' => 'brand',
        'Model' => 'model',
        'Tag' => 'tag',
        'Department' => 'department',
        'Status' => 'status',
        'Hostname' => 'hostname',
        'Operating System' => 'operating_system',
        'Serial Number' => 'serial_number',
        'Asset ID' => 'asset_id',
        'Warranty Expire' => 'warranty_expire',
        'Description' => 'description',
        'Set' => 'set_name'
    ];
    
    $columnMap = [];
    foreach ($headers as $index => $header) {
        $header = trim($header);
        if (isset($mapping[$header])) {
            $columnMap[$index] = $mapping[$header];
        }
    }
    
    return $columnMap;
}

// ฟังก์ชันประมวลผลแต่ละแถว
function processRow($data, $headers, $columnMap, $pdo, $rowNumber) {
    $result = ['success' => false, 'error' => ''];
    
    try {
        // เตรียมข้อมูลสำหรับ insert
        $insertData = [];
        
        foreach ($columnMap as $index => $dbColumn) {
            $value = isset($data[$index]) ? trim($data[$index]) : '';
            
            if (!empty($value)) {
                // ประมวลผลข้อมูลตามประเภท
                if ($dbColumn === 'warranty_expire') {
                    $value = parseDate($value);
                }
                $insertData[$dbColumn] = $value;
            }
        }
        
        // ตรวจสอบข้อมูลที่จำเป็น
        if (empty($insertData['type'])) {
            $result['error'] = 'ไม่มีข้อมูล Type';
            return $result;
        }
        
        // Tag สามารถซ้ำกันได้ - ไม่ต้องตรวจสอบ
        // Tag duplicate check removed - tags can be duplicated
        
        // เพิ่มข้อมูลระบบ
        $insertData['created_by'] = $_SESSION['username'] ?? 'system';
        $insertData['updated_by'] = $_SESSION['username'] ?? 'system';
        $insertData['created_date'] = date('Y-m-d H:i:s');
        $insertData['updated_date'] = date('Y-m-d H:i:s');
        
        // Insert ข้อมูล
        $columns = array_keys($insertData);
        $placeholders = ':' . implode(', :', $columns);
        $sql = "INSERT INTO assets (`" . implode("`, `", $columns) . "`) VALUES ($placeholders)";
        
        $stmt = $pdo->prepare($sql);
        if ($stmt->execute($insertData)) {
            $assetId = $pdo->lastInsertId();
            
            // บันทึก log
            $logStmt = $pdo->prepare("INSERT INTO asset_logs (asset_id, action_type, changed_by, description, changed_date) VALUES (?, 'CREATE', ?, 'Imported from CSV', ?)");
            $logStmt->execute([$assetId, $_SESSION['username'] ?? 'system', date('Y-m-d H:i:s')]);
            
            $result['success'] = true;
        } else {
            $result['error'] = 'ไม่สามารถบันทึกข้อมูลได้';
        }
        
    } catch (Exception $e) {
        $result['error'] = $e->getMessage();
    }
    
    return $result;
}

// ฟังก์ชันแปลงวันที่
function parseDate($dateString) {
    $formats = ['d/m/Y', 'Y-m-d', 'm/d/Y', 'd-m-Y'];
    
    foreach ($formats as $format) {
        $date = DateTime::createFromFormat($format, $dateString);
        if ($date !== false) {
            return $date->format('Y-m-d');
        }
    }
    
    return null;
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Import CSV - Asset Management System</title>
    <link href="https://fonts.googleapis.com/css2?family=Sarabun:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Sarabun', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        .card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .card-header h2 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 600;
        }

        .card-body {
            padding: 30px;
        }

        .upload-area {
            border: 3px dashed #667eea;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            background: #f8f9ff;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 20px 0;
        }

        .upload-area:hover {
            border-color: #5a67d8;
            background: #f0f2ff;
        }

        .upload-icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 15px;
        }

        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 1rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-outline {
            background: transparent;
            border: 2px solid #667eea;
            color: #667eea;
        }

        .btn-outline:hover {
            background: #667eea;
            color: white;
        }

        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .info-box {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }

        .file-input {
            display: none;
        }

        .text-center {
            text-align: center;
        }

        .mt-3 {
            margin-top: 1rem;
        }

        .error-details {
            max-height: 200px;
            overflow-y: auto;
            background: rgba(255,255,255,0.5);
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header">
                <h2><i class="fas fa-file-import"></i> Import CSV Assets</h2>
                <p>นำเข้าข้อมูล Asset จากไฟล์ CSV</p>
            </div>
            <div class="card-body">
                <?php if (!empty($message)): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                <?php endif; ?>

                <?php if (!empty($error)): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        <div><strong><?php echo htmlspecialchars($error); ?></strong></div>

                        <?php if (!empty($importResults['details'])): ?>
                            <div class="mt-3">
                                <strong>รายละเอียดข้อผิดพลาด:</strong>
                                <div class="error-details">
                                    <?php foreach (array_slice($importResults['details'], 0, 10) as $detail): ?>
                                        <div style="padding: 5px; margin: 2px 0; background: rgba(255,255,255,0.3); border-radius: 3px;">
                                            <i class="fas fa-times-circle" style="color: #dc3545;"></i>
                                            <?php echo htmlspecialchars($detail); ?>
                                        </div>
                                    <?php endforeach; ?>

                                    <?php if (count($importResults['details']) > 10): ?>
                                        <div style="text-align: center; margin-top: 10px; font-style: italic;">
                                            ... และอีก <?php echo count($importResults['details']) - 10; ?> ข้อผิดพลาด
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>

                <form action="" method="post" enctype="multipart/form-data" id="uploadForm">
                    <div class="upload-area" onclick="document.getElementById('csv_file').click()">
                        <div class="upload-icon">
                            <i class="fas fa-cloud-upload-alt"></i>
                        </div>
                        <h3>คลิกเพื่อเลือกไฟล์ CSV</h3>
                        <p>รองรับเฉพาะไฟล์ .csv เท่านั้น</p>
                        <input type="file" id="csv_file" name="csv_file" class="file-input" required accept=".csv">
                    </div>

                    <div class="text-center mt-3">
                        <button type="submit" class="btn btn-primary" id="uploadBtn">
                            <i class="fas fa-upload"></i> อัพโหลดและ Import
                        </button>
                        <a href="download_template.php" class="btn btn-outline">
                            <i class="fas fa-download"></i> ดาวน์โหลด Template
                        </a>
                        <a href="index.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> กลับหน้าหลัก
                        </a>
                    </div>
                </form>
                
                <div class="info-box">
                    <h6><i class="fas fa-info-circle"></i> คำแนะนำการใช้งาน:</h6>
                    <ul style="margin: 10px 0 0 20px; font-size: 0.9rem;">
                        <li><strong>Type</strong> - ฟิลด์จำเป็น ต้องระบุประเภท Asset</li>
                        <li><strong>Status</strong> - ใช้งาน, ชำรุด, สำรอง</li>
                        <li><strong>Warranty Expire</strong> - รูปแบบวันที่: dd/mm/yyyy หรือ yyyy-mm-dd</li>
                        <li><strong>Tag</strong> - ต้องไม่ซ้ำกับที่มีอยู่ในระบบ</li>
                        <li><strong>ไฟล์ CSV</strong> - ต้องมี headers ในบรรทัดแรก</li>
                        <li><strong>Encoding</strong> - รองรับ UTF-8 (มี BOM หรือไม่มีก็ได้)</li>
                    </ul>

                    <div style="margin-top: 15px; padding: 10px; background: #fff3cd; border-radius: 5px; border-left: 3px solid #ffc107;">
                        <strong><i class="fas fa-lightbulb"></i> เคล็ดลับ:</strong>
                        <ul style="margin: 5px 0 0 20px; font-size: 0.85rem;">
                            <li>ระบบจะปรับจำนวน column อัตโนมัติถ้าไม่ตรงกับ header</li>
                            <li>แถวว่างจะถูกข้ามไปโดยอัตโนมัติ</li>
                            <li>ข้อมูลที่ผิดพลาดจะแสดงรายละเอียดชัดเจน</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        document.getElementById('csv_file').addEventListener('change', function(e) {
            if (e.target.files.length > 0) {
                const fileName = e.target.files[0].name;
                const uploadArea = document.querySelector('.upload-area');
                uploadArea.innerHTML = `
                    <div class="upload-icon">
                        <i class="fas fa-file-check" style="color: #28a745;"></i>
                    </div>
                    <h3 style="color: #28a745;">ไฟล์ที่เลือก: ${fileName}</h3>
                    <p>คลิกเพื่อเลือกไฟล์ใหม่</p>
                `;
            }
        });

        document.getElementById('uploadForm').addEventListener('submit', function(e) {
            const uploadBtn = document.getElementById('uploadBtn');
            uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> กำลังประมวลผล...';
            uploadBtn.disabled = true;
        });
    </script>
</body>
</html>
