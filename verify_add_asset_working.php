<?php
// ยืนยันว่า add_asset.php ทำงานได้
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>✅ ยืนยันว่า add_asset.php ทำงานได้</h2>";

// เริ่ม session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// ตั้งค่า session
if (!isset($_SESSION['username'])) {
    $_SESSION['username'] = 'admin';
    $_SESSION['role'] = 'Admin';
    $_SESSION['user_id'] = 1;
    echo "<p style='color: blue;'>ℹ️ ตั้งค่า session: admin/Admin</p>";
}

// ตรวจสอบการเชื่อมต่อฐานข้อมูล
echo "<h3>1. ตรวจสอบการเชื่อมต่อฐานข้อมูล:</h3>";
try {
    $pdo = new PDO("mysql:host=localhost;dbname=asset_management;charset=utf8", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✅ เชื่อมต่อฐานข้อมูลสำเร็จ</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ ไม่สามารถเชื่อมต่อฐานข้อมูล: " . $e->getMessage() . "</p>";
    die();
}

// ตรวจสอบตาราง assets
echo "<h3>2. ตรวจสอบตาราง assets:</h3>";
try {
    $stmt = $pdo->query("DESCRIBE assets");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "<p style='color: green;'>✅ ตาราง assets มีอยู่</p>";
    echo "<p><strong>คอลัมน์:</strong> " . implode(', ', $columns) . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ ตาราง assets ไม่มี: " . $e->getMessage() . "</p>";
    die();
}

// ทดสอบการส่งข้อมูลไป add_asset.php
echo "<h3>3. ทดสอบการส่งข้อมูลไป add_asset.php:</h3>";

// ข้อมูลทดสอบ 3 ชุด
$testCases = [
    [
        'name' => 'Desktop Dell',
        'data' => [
            'type' => 'Desktop',
            'brand' => 'Dell',
            'model' => 'OptiPlex 7090',
            'tag' => 'TAG-DESKTOP-001',
            'department' => 'IT Department',
            'status' => 'ใช้งาน',
            'hostname' => 'PC-DESKTOP-001',
            'operating_system' => 'Windows 10',
            'serial_number' => 'SN-DESKTOP-001',
            'asset_id' => 'ASSET-DESKTOP-001',
            'warranty_expire' => '2025-12-31',
            'description' => 'Desktop Dell OptiPlex 7090',
            'set_name' => 'Desktop Set 1'
        ]
    ],
    [
        'name' => 'Laptop Lenovo',
        'data' => [
            'type' => 'Laptop',
            'brand' => 'Lenovo',
            'model' => 'ThinkPad X1',
            'tag' => 'TAG-LAPTOP-001',
            'department' => 'Sales Department',
            'status' => 'ใช้งาน',
            'hostname' => 'LAPTOP-001',
            'operating_system' => 'Windows 11',
            'serial_number' => 'SN-LAPTOP-001',
            'asset_id' => 'ASSET-LAPTOP-001',
            'warranty_expire' => '2026-06-30',
            'description' => 'Laptop Lenovo ThinkPad X1',
            'set_name' => 'Laptop Set 1'
        ]
    ],
    [
        'name' => 'Monitor LG',
        'data' => [
            'type' => 'Monitor',
            'brand' => 'LG',
            'model' => '27UP850',
            'tag' => 'TAG-MONITOR-001',
            'department' => 'Design Department',
            'status' => 'ใช้งาน',
            'hostname' => '',
            'operating_system' => '',
            'serial_number' => 'SN-MONITOR-001',
            'asset_id' => 'ASSET-MONITOR-001',
            'warranty_expire' => '2025-03-15',
            'description' => 'Monitor LG 27UP850 4K',
            'set_name' => 'Monitor Set 1'
        ]
    ]
];

$successCount = 0;
$totalTests = count($testCases);

foreach ($testCases as $index => $testCase) {
    echo "<h4>ทดสอบ " . ($index + 1) . ": {$testCase['name']}</h4>";
    
    // ส่งข้อมูลไป add_asset.php
    $postString = http_build_query($testCase['data']);
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-type: application/x-www-form-urlencoded',
            'content' => $postString
        ]
    ]);
    
    try {
        $result = file_get_contents('http://localhost/asset/add_asset.php', false, $context);
        
        if ($result !== false) {
            echo "<p style='color: green;'>✅ ส่งข้อมูลสำเร็จ</p>";
            
            // ตรวจสอบผลลัพธ์
            if (strpos($result, 'เพิ่ม Asset สำเร็จ') !== false) {
                echo "<p style='color: green;'>✅ พบข้อความสำเร็จ</p>";
                
                // ดึง ID ที่เพิ่ม
                if (preg_match('/ID: (\d+)/', $result, $matches)) {
                    $newId = $matches[1];
                    echo "<p style='color: green;'>✅ Asset ID: <strong>$newId</strong></p>";
                    
                    // ตรวจสอบข้อมูลในฐานข้อมูล
                    try {
                        $checkStmt = $pdo->prepare("SELECT * FROM assets WHERE id = ?");
                        $checkStmt->execute([$newId]);
                        $newAsset = $checkStmt->fetch();
                        
                        if ($newAsset) {
                            echo "<p style='color: green;'>✅ ข้อมูลถูกบันทึกในฐานข้อมูล</p>";
                            echo "<p><strong>Type:</strong> {$newAsset['type']}, <strong>Brand:</strong> {$newAsset['brand']}, <strong>Model:</strong> {$newAsset['model']}</p>";
                            $successCount++;
                        } else {
                            echo "<p style='color: red;'>❌ ไม่พบข้อมูลในฐานข้อมูล</p>";
                        }
                    } catch (Exception $e) {
                        echo "<p style='color: red;'>❌ ข้อผิดพลาดในการตรวจสอบฐานข้อมูล: " . $e->getMessage() . "</p>";
                    }
                } else {
                    echo "<p style='color: red;'>❌ ไม่พบ Asset ID ในผลลัพธ์</p>";
                }
                
            } elseif (strpos($result, 'error') !== false || strpos($result, 'Error') !== false || strpos($result, 'Fatal') !== false) {
                echo "<p style='color: red;'>❌ พบข้อผิดพลาดในผลลัพธ์</p>";
                
                // แสดงข้อผิดพลาด
                if (preg_match('/(error|Error|Fatal).*?<br>/i', $result, $matches)) {
                    echo "<p style='color: red;'><strong>ข้อผิดพลาด:</strong> " . strip_tags($matches[0]) . "</p>";
                }
            } else {
                echo "<p style='color: orange;'>⚠️ ไม่พบข้อความสำเร็จหรือข้อผิดพลาดที่ชัดเจน</p>";
            }
            
        } else {
            echo "<p style='color: red;'>❌ ไม่สามารถส่งข้อมูลได้</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ ข้อผิดพลาดในการส่งข้อมูล: " . $e->getMessage() . "</p>";
    }
    
    echo "<hr>";
}

// สรุปผลการทดสอบ
echo "<h3>4. สรุปผลการทดสอบ:</h3>";
echo "<div style='background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);'>";
echo "<p><strong>ทดสอบทั้งหมด:</strong> $totalTests ครั้ง</p>";
echo "<p><strong>สำเร็จ:</strong> $successCount ครั้ง</p>";
echo "<p><strong>ล้มเหลว:</strong> " . ($totalTests - $successCount) . " ครั้ง</p>";

if ($successCount === $totalTests) {
    echo "<p style='color: green; font-size: 1.2em; font-weight: bold;'>🎉 add_asset.php ทำงานได้สมบูรณ์!</p>";
} elseif ($successCount > 0) {
    echo "<p style='color: orange; font-size: 1.2em; font-weight: bold;'>⚠️ add_asset.php ทำงานได้บางส่วน</p>";
} else {
    echo "<p style='color: red; font-size: 1.2em; font-weight: bold;'>❌ add_asset.php ไม่ทำงาน</p>";
}
echo "</div>";

// แสดงสถิติ Assets ในฐานข้อมูล
echo "<h3>5. สถิติ Assets ในฐานข้อมูล:</h3>";
try {
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM assets");
    $count = $stmt->fetch()['total'];
    echo "<p><strong>จำนวน Assets ทั้งหมด:</strong> $count รายการ</p>";
    
    if ($count > 0) {
        // ตรวจสอบคอลัมน์วันที่ที่มีอยู่
        $stmt = $pdo->query("DESCRIBE assets");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $dateColumn = '';
        if (in_array('created_date', $columns)) {
            $dateColumn = 'created_date';
        } elseif (in_array('date_added', $columns)) {
            $dateColumn = 'date_added';
        }
        
        // สร้าง SQL ตามคอลัมน์ที่มีอยู่
        $selectFields = "id, type, brand, model, status";
        if ($dateColumn) {
            $selectFields .= ", $dateColumn as date_added";
        }
        
        $stmt = $pdo->query("SELECT $selectFields FROM assets ORDER BY id DESC LIMIT 10");
        $recentAssets = $stmt->fetchAll();
        
        echo "<h4>Assets 10 รายการล่าสุด:</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; background: white;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>Type</th>";
        echo "<th style='padding: 8px;'>Brand</th>";
        echo "<th style='padding: 8px;'>Model</th>";
        echo "<th style='padding: 8px;'>Status</th>";
        echo "<th style='padding: 8px;'>Date Added</th>";
        echo "</tr>";
        
        foreach ($recentAssets as $asset) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>{$asset['id']}</td>";
            echo "<td style='padding: 8px;'>{$asset['type']}</td>";
            echo "<td style='padding: 8px;'>{$asset['brand']}</td>";
            echo "<td style='padding: 8px;'>{$asset['model']}</td>";
            echo "<td style='padding: 8px;'>{$asset['status']}</td>";
            $dateAdded = $asset['date_added'] ?? 'N/A';
            echo "<td style='padding: 8px;'>$dateAdded</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ ข้อผิดพลาดในการดูสถิติ: " . $e->getMessage() . "</p>";
}

?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ยืนยันว่า add_asset.php ทำงานได้</title>
    <style>
        body {
            font-family: 'TH Sarabun New', Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h2 {
            background: #28a745;
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        h3 {
            background: #6c757d;
            color: white;
            padding: 10px;
            border-radius: 5px;
        }
        h4 {
            background: #17a2b8;
            color: white;
            padding: 8px;
            border-radius: 3px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
        }
        hr {
            border: none;
            height: 2px;
            background: #ddd;
            margin: 20px 0;
        }
    </style>
</head>
<body>

<div style="margin: 20px 0;">
    <a href="add_asset.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">➕ ไปหน้าเพิ่ม Asset</a>
    <a href="test_add_asset_final.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">🧪 ทดสอบ Manual</a>
    <a href="check_and_fix_columns.php" style="background: #e74c3c; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">🔧 แก้ไขคอลัมน์</a>
    <a href="index.php" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">🏠 หน้าหลัก</a>
</div>

</body>
</html>
