<?php
// แก้ไขชื่อคอลัมน์ในตาราง assets
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🔧 แก้ไขชื่อคอลัมน์ในตาราง Assets</h2>";

// เชื่อมต่อฐานข้อมูล
try {
    $pdo = new PDO("mysql:host=localhost;dbname=asset_management;charset=utf8", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✅ Database connected</p>";
} catch (Exception $e) {
    die("<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>");
}

// ตรวจสอบโครงสร้างตารางปัจจุบัน
echo "<h3>โครงสร้างตารางปัจจุบัน:</h3>";
try {
    $stmt = $pdo->query("DESCRIBE assets");
    $columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 8px;'>Field</th>";
    echo "<th style='padding: 8px;'>Type</th>";
    echo "<th style='padding: 8px;'>Null</th>";
    echo "<th style='padding: 8px;'>Key</th>";
    echo "<th style='padding: 8px;'>Default</th>";
    echo "</tr>";
    
    $existingColumns = [];
    foreach ($columns as $column) {
        $existingColumns[] = $column['Field'];
        echo "<tr>";
        echo "<td style='padding: 8px;'>{$column['Field']}</td>";
        echo "<td style='padding: 8px;'>{$column['Type']}</td>";
        echo "<td style='padding: 8px;'>{$column['Null']}</td>";
        echo "<td style='padding: 8px;'>{$column['Key']}</td>";
        echo "<td style='padding: 8px;'>{$column['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<p>คอลัมน์ที่มีอยู่: " . implode(', ', $existingColumns) . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error getting table structure: " . $e->getMessage() . "</p>";
}

// ตรวจสอบและเพิ่มคอลัมน์ที่ขาดหายไป
if ($_POST && isset($_POST['fix_columns'])) {
    echo "<h3>🔧 แก้ไขคอลัมน์:</h3>";
    
    // คอลัมน์ที่ต้องการ
    $requiredColumns = [
        'set_name' => 'VARCHAR(100)',
        'person_added' => 'VARCHAR(100)',
        'person_modified' => 'VARCHAR(100)',
        'date_added' => 'DATETIME DEFAULT CURRENT_TIMESTAMP',
        'date_modified' => 'DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
    ];
    
    foreach ($requiredColumns as $columnName => $columnDefinition) {
        if (!in_array($columnName, $existingColumns)) {
            try {
                $sql = "ALTER TABLE assets ADD COLUMN $columnName $columnDefinition";
                $pdo->exec($sql);
                echo "<p style='color: green;'>✅ เพิ่มคอลัมน์ '$columnName' สำเร็จ</p>";
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ ไม่สามารถเพิ่มคอลัมน์ '$columnName': " . $e->getMessage() . "</p>";
            }
        } else {
            echo "<p style='color: blue;'>ℹ️ คอลัมน์ '$columnName' มีอยู่แล้ว</p>";
        }
    }
    
    echo "<p style='color: green;'><strong>✅ เสร็จสิ้นการแก้ไขคอลัมน์</strong></p>";
    echo "<p><a href='fix_table_columns.php'>รีเฟรชหน้าเพื่อดูผลลัพธ์</a></p>";
}

// ทดสอบ INSERT หลังแก้ไข
if ($_POST && isset($_POST['test_insert_after_fix'])) {
    echo "<h3>🧪 ทดสอบ INSERT หลังแก้ไข:</h3>";
    
    try {
        $sql = "INSERT INTO assets (type, brand, model, tag, department, status, hostname, operating_system, serial_number, asset_id, warranty_expire, description, set_name, person_added, person_modified, date_added, date_modified) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";
        
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([
            'Desktop',
            'Dell',
            'OptiPlex 7090',
            'TAG-001',
            'IT Department',
            'ใช้งาน',
            'PC-001',
            'Windows 10',
            'SN123456789',
            'ASSET-001',
            '2025-12-31',
            'Test Asset Description',
            'Set A',
            'admin',
            'admin'
        ]);
        
        if ($result) {
            $newId = $pdo->lastInsertId();
            echo "<p style='color: green;'>✅ INSERT สำเร็จ! ID: $newId</p>";
            
            // แสดงข้อมูลที่เพิ่ม
            $checkStmt = $pdo->prepare("SELECT * FROM assets WHERE id = ?");
            $checkStmt->execute([$newId]);
            $newRecord = $checkStmt->fetch();
            
            echo "<h4>ข้อมูลที่เพิ่ม:</h4>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            foreach ($newRecord as $key => $value) {
                if (!is_numeric($key)) {
                    echo "<tr><td style='padding: 8px;'><strong>$key</strong></td><td style='padding: 8px;'>$value</td></tr>";
                }
            }
            echo "</table>";
            
            // ลบทันที
            $deleteStmt = $pdo->prepare("DELETE FROM assets WHERE id = ?");
            $deleteStmt->execute([$newId]);
            echo "<p style='color: blue;'>🗑️ ลบข้อมูลทดสอบแล้ว</p>";
            
        } else {
            echo "<p style='color: red;'>❌ INSERT ไม่สำเร็จ</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ INSERT error: " . $e->getMessage() . "</p>";
        echo "<p style='color: red;'>Error Code: " . $e->getCode() . "</p>";
    }
}

// แสดงคำสั่ง SQL สำหรับสร้างตารางใหม่
echo "<h3>📋 คำสั่ง SQL สำหรับสร้างตารางใหม่:</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 0.9rem;'>";
echo htmlspecialchars("
CREATE TABLE assets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    type VARCHAR(100),
    brand VARCHAR(100),
    model VARCHAR(100),
    tag VARCHAR(100),
    department VARCHAR(100),
    status VARCHAR(50) DEFAULT 'ใช้งาน',
    hostname VARCHAR(100),
    operating_system VARCHAR(100),
    serial_number VARCHAR(100),
    asset_id VARCHAR(100),
    warranty_expire DATE,
    description TEXT,
    set_name VARCHAR(100),
    person_added VARCHAR(100),
    person_modified VARCHAR(100),
    date_added DATETIME DEFAULT CURRENT_TIMESTAMP,
    date_modified DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
");
echo "</div>";

?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>แก้ไขชื่อคอลัมน์ตาราง Assets</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h2 {
            background: #dc3545;
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .action-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        button {
            background: #28a745;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
        }
        button:hover {
            background: #218838;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>

<div class="action-section">
    <h3>🔧 แก้ไขคอลัมน์ที่ขาดหายไป</h3>
    <p>เพิ่มคอลัมน์ที่จำเป็นในตาราง assets</p>
    <form method="POST">
        <input type="hidden" name="fix_columns" value="1">
        <button type="submit">🔧 แก้ไขคอลัมน์</button>
    </form>
</div>

<div class="action-section">
    <h3>🧪 ทดสอบ INSERT หลังแก้ไข</h3>
    <p>ทดสอบการเพิ่มข้อมูลหลังจากแก้ไขคอลัมน์แล้ว</p>
    <form method="POST">
        <input type="hidden" name="test_insert_after_fix" value="1">
        <button type="submit">🧪 ทดสอบ INSERT</button>
    </form>
</div>

<div class="warning">
    <strong>⚠️ หมายเหตุ:</strong> การแก้ไขคอลัมน์จะเพิ่มคอลัมน์ที่ขาดหายไปในตาราง assets เท่านั้น ไม่มีการลบหรือแก้ไขข้อมูลที่มีอยู่
</div>

<div style="margin: 20px 0;">
    <a href="check_table_structure.php" style="background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">🔍 ตรวจสอบตาราง</a>
    <a href="add_asset.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">➕ หน้าเพิ่ม Asset</a>
    <a href="index.php" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">🏠 หน้าหลัก</a>
</div>

</body>
</html>
