<?php
// ทดสอบ Badge ขนาดมินิ (เท่ากับตัวอักษร)
require_once 'includes/auth.php';

// ตรวจสอบการล็อกอิน
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

echo "<h2>ทดสอบ Badge ขนาดมินิ (เท่ากับตัวอักษร)</h2>";

try {
    // แสดงการเปรียบเทียบขนาด Badge
    echo "<h3>1. เปรียบเทียบขนาด Badge</h3>";
    
    echo "<div style='background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1); margin: 20px 0;'>";
    
    echo "<h4>Badge ขนาดเก่า (ใหญ่):</h4>";
    echo "<div style='margin: 15px 0; display: flex; align-items: center; gap: 15px;'>";
    echo "<span style='font-size: 0.95rem;'>บทบาท:</span>";
    echo "<span style='background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white; padding: 4px 10px; border-radius: 12px; font-size: 0.75rem; font-weight: 600;'>Admin</span>";
    echo "</div>";
    
    echo "<h4>Badge ขนาดใหม่ (มินิ - เท่ากับตัวอักษร):</h4>";
    echo "<div style='margin: 15px 0; display: flex; align-items: center; gap: 15px;'>";
    echo "<span style='font-size: 0.95rem;'>บทบาท:</span>";
    echo "<span style='background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white; padding: 2px 6px; border-radius: 6px; font-size: 0.7rem; font-weight: 600; line-height: 1; height: fit-content;'>Admin</span>";
    echo "</div>";
    
    echo "<h4>เปรียบเทียบกับข้อความปกติ:</h4>";
    echo "<div style='margin: 15px 0; display: flex; align-items: center; gap: 15px; font-size: 0.95rem;'>";
    echo "<span>ข้อความปกติ</span>";
    echo "<span style='background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white; padding: 2px 6px; border-radius: 6px; font-size: 0.7rem; font-weight: 600; line-height: 1; height: fit-content;'>Admin</span>";
    echo "<span style='background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 2px 6px; border-radius: 6px; font-size: 0.7rem; font-weight: 600; line-height: 1; height: fit-content;'>Active</span>";
    echo "<span>ข้อความต่อ</span>";
    echo "</div>";
    
    echo "</div>";
    
    // จำลอง Profile Modal ด้วย Badge ใหม่
    echo "<h3>2. จำลอง Profile Modal ด้วย Badge มินิ</h3>";
    
    echo "<div style='background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1); margin: 20px 0; max-width: 450px;'>";
    
    // Detail rows
    $details = [
        ['🏷️ Username', 'admin'],
        ['👤 ชื่อ-นามสกุล', 'ผู้ดูแลระบบ'],
        ['📧 Email', '<EMAIL>'],
        ['🎭 บทบาท', '<span style="background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white; padding: 2px 6px; border-radius: 6px; font-size: 0.7rem; font-weight: 600; line-height: 1; height: fit-content;">Admin</span>'],
        ['🟢 สถานะ', '<span style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 2px 6px; border-radius: 6px; font-size: 0.7rem; font-weight: 600; line-height: 1; height: fit-content;">Active</span>'],
        ['🕐 เข้าสู่ระบบล่าสุด', '25/12/2024 14:30:15'],
        ['📅 วันที่สร้างบัญชี', '01/01/2024 09:00:00']
    ];
    
    foreach ($details as $detail) {
        echo "<div style='display: flex; justify-content: space-between; align-items: center; padding: 12px 0; border-bottom: 1px solid #f1f5f9;'>";
        echo "<label style='font-weight: 600; color: #475569; margin: 0; min-width: 140px; display: flex; align-items: center; gap: 8px; font-size: 0.95rem;'>";
        echo "<span style='width: 4px; height: 4px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%;'></span>";
        echo $detail[0];
        echo "</label>";
        echo "<span style='color: #1e293b; text-align: right; flex: 1; font-weight: 500; font-size: 0.95rem; display: flex; justify-content: flex-end; align-items: center;'>";
        echo $detail[1];
        echo "</span>";
        echo "</div>";
    }
    
    echo "</div>";
    
    // แสดงตัวอย่าง Badge ทุกประเภท
    echo "<h3>3. Badge ทุกประเภทขนาดมินิ</h3>";
    
    echo "<div style='background: white; padding: 25px; border-radius: 15px; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1); margin: 20px 0;'>";
    
    echo "<h4>Role Badges:</h4>";
    echo "<div style='margin: 15px 0; display: flex; align-items: center; gap: 10px; flex-wrap: wrap;'>";
    echo "<span style='background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white; padding: 2px 6px; border-radius: 6px; font-size: 0.7rem; font-weight: 600; line-height: 1; height: fit-content;'>Admin</span>";
    echo "<span style='background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 2px 6px; border-radius: 6px; font-size: 0.7rem; font-weight: 600; line-height: 1; height: fit-content;'>User</span>";
    echo "</div>";
    
    echo "<h4>Status Badges:</h4>";
    echo "<div style='margin: 15px 0; display: flex; align-items: center; gap: 10px; flex-wrap: wrap;'>";
    echo "<span style='background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 2px 6px; border-radius: 6px; font-size: 0.7rem; font-weight: 600; line-height: 1; height: fit-content;'>Active</span>";
    echo "<span style='background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); color: white; padding: 2px 6px; border-radius: 6px; font-size: 0.7rem; font-weight: 600; line-height: 1; height: fit-content;'>Inactive</span>";
    echo "</div>";
    
    echo "<h4>ในบริบทของประโยค:</h4>";
    echo "<div style='margin: 15px 0; line-height: 1.6;'>";
    echo "<p style='font-size: 0.95rem; margin: 8px 0;'>ผู้ใช้ <span style='background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white; padding: 2px 6px; border-radius: 6px; font-size: 0.7rem; font-weight: 600; line-height: 1; height: fit-content;'>Admin</span> มีสถานะ <span style='background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 2px 6px; border-radius: 6px; font-size: 0.7rem; font-weight: 600; line-height: 1; height: fit-content;'>Active</span> ในระบบ</p>";
    echo "<p style='font-size: 0.95rem; margin: 8px 0;'>บัญชี <span style='background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 2px 6px; border-radius: 6px; font-size: 0.7rem; font-weight: 600; line-height: 1; height: fit-content;'>User</span> ที่มีสถานะ <span style='background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); color: white; padding: 2px 6px; border-radius: 6px; font-size: 0.7rem; font-weight: 600; line-height: 1; height: fit-content;'>Inactive</span> จะไม่สามารถเข้าใช้งานได้</p>";
    echo "</div>";
    
    echo "</div>";
    
    // ทดสอบ API
    echo "<h3>4. ทดสอบข้อมูลจริงจาก API</h3>";
    
    if (file_exists('get_profile_data.php')) {
        ob_start();
        include 'get_profile_data.php';
        $response = ob_get_clean();
        
        $data = json_decode($response, true);
        
        if ($data && isset($data['success']) && $data['success']) {
            echo "<p style='color: green;'>✅ API ทำงานได้ถูกต้อง</p>";
            
            $user = $data['user'];
            echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0;'>";
            echo "<h4>ข้อมูลผู้ใช้ปัจจุบัน (ด้วย Badge มินิ):</h4>";
            echo "<div style='display: flex; gap: 15px; align-items: center; flex-wrap: wrap; font-size: 0.95rem;'>";
            echo "<strong>Username:</strong> " . ($user['username'] ?? 'N/A');
            
            if (isset($user['role'])) {
                $roleColor = $user['role'] === 'Admin' ? 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)' : 'linear-gradient(135deg, #10b981 0%, #059669 100%)';
                echo "<span style='background: $roleColor; color: white; padding: 2px 6px; border-radius: 6px; font-size: 0.7rem; font-weight: 600; line-height: 1; height: fit-content;'>" . $user['role'] . "</span>";
            }
            
            if (isset($user['status'])) {
                $statusColor = $user['status'] === 'Inactive' ? 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)' : 'linear-gradient(135deg, #10b981 0%, #059669 100%)';
                echo "<span style='background: $statusColor; color: white; padding: 2px 6px; border-radius: 6px; font-size: 0.7rem; font-weight: 600; line-height: 1; height: fit-content;'>" . $user['status'] . "</span>";
            }
            echo "</div>";
            echo "</div>";
        }
    }
    
    echo "<h3>5. สรุปการปรับปรุง Badge เป็นขนาดมินิ</h3>";
    
    echo "<div style='background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 15px; margin: 20px 0;'>";
    echo "<h4 style='color: white; margin-top: 0;'>🎯 การปรับปรุงเป็นขนาดมินิ:</h4>";
    echo "<ul style='color: white;'>";
    echo "<li>📏 <strong>Ultra Compact:</strong> ลด padding เป็น 2px 6px (เล็กที่สุด)</li>";
    echo "<li>🔤 <strong>Tiny Font:</strong> ลดขนาดตัวอักษรเป็น 0.7rem</li>";
    echo "<li>🎨 <strong>Small Radius:</strong> ลด border-radius เป็น 6px</li>";
    echo "<li>📐 <strong>Minimal Height:</strong> ใช้ line-height: 1 และ height: fit-content</li>";
    echo "<li>✨ <strong>Subtle Shadow:</strong> ลดเงาเป็น 0 1px 3px</li>";
    echo "<li>🎯 <strong>Perfect Fit:</strong> ขนาดเท่ากับตัวอักษรปกติ</li>";
    echo "<li>⚡ <strong>Minimal Gap:</strong> ลด gap เป็น 2px</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #c3e6cb;'>";
    echo "<h4 style='color: #155724;'>✅ ผลลัพธ์ Badge มินิ:</h4>";
    echo "<ul style='color: #155724;'>";
    echo "<li>✅ Badge มีขนาดเท่ากับตัวอักษรปกติ</li>";
    echo "<li>✅ ไม่รบกวนการอ่านข้อความ</li>";
    echo "<li>✅ ประหยัดพื้นที่สูงสุด</li>";
    echo "<li>✅ ยังคงสีสันสวยงาม</li>";
    echo "<li>✅ อ่านได้ชัดเจนแม้ขนาดเล็ก</li>";
    echo "<li>✅ เหมาะสำหรับการใช้งานในประโยค</li>";
    echo "<li>✅ ดูเป็นธรรมชาติและไม่โดดเด่นเกินไป</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='index.php' style='background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 12px 24px; text-decoration: none; border-radius: 10px; margin-right: 10px; display: inline-flex; align-items: center; gap: 8px;'>🏠 ทดสอบใน index.php</a>";
    echo "<a href='users.php' style='background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 12px 24px; text-decoration: none; border-radius: 10px; display: inline-flex; align-items: center; gap: 8px;'>👥 ทดสอบใน users.php</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ เกิดข้อผิดพลาด</h3>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<p style='color: red; margin-top: 20px;'><strong>หมายเหตุ:</strong> กรุณาลบไฟล์ test_mini_badges.php หลังจากทดสอบเสร็จแล้ว</p>";
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบ Badge ขนาดมินิ</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        h2 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
    </style>
</head>
<body>
    <!-- เนื้อหาจะแสดงจาก PHP ด้านบน -->
</body>
</html>
