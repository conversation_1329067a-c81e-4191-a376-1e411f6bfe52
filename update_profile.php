<?php
require_once 'includes/auth.php';

// ตรวจสอบการล็อกอิน
if (!$auth->isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'ไม่ได้ล็อกอิน']);
    exit;
}

// ตรวจสอบ method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    $userId = $_SESSION['user_id'];
    
    // ตรวจสอบฟิลด์ที่มีอยู่ในฐานข้อมูล
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll();
    $availableFields = [];
    foreach ($columns as $column) {
        $availableFields[] = $column['Field'];
    }
    
    // ดึงข้อมูลผู้ใช้ปัจจุบัน
    $currentUser = $auth->getCurrentUser();
    if (!$currentUser) {
        echo json_encode(['success' => false, 'message' => 'ไม่พบข้อมูลผู้ใช้']);
        exit;
    }
    
    // รับข้อมูลจากฟอร์ม
    $fullName = trim($_POST['full_name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $currentPassword = $_POST['current_password'] ?? '';
    $newPassword = $_POST['new_password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    
    // ตรวจสอบข้อมูล
    $errors = [];
    
    // ตรวจสอบชื่อ-นามสกุล (ถ้ามีฟิลด์นี้)
    if (in_array('full_name', $availableFields) && empty($fullName)) {
        $errors[] = 'กรุณากรอกชื่อ-นามสกุล';
    }
    
    // ตรวจสอบ email format (ถ้ามีฟิลด์นี้และกรอกข้อมูล)
    if (in_array('email', $availableFields) && !empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'รูปแบบ email ไม่ถูกต้อง';
    }
    
    // ตรวจสอบรหัสผ่าน (ถ้าต้องการเปลี่ยน)
    if (!empty($newPassword)) {
        if (empty($currentPassword)) {
            $errors[] = 'กรุณากรอกรหัสผ่านปัจจุบัน';
        } else {
            // ตรวจสอบรหัสผ่านปัจจุบัน
            if (!password_verify($currentPassword, $currentUser['password'])) {
                $errors[] = 'รหัสผ่านปัจจุบันไม่ถูกต้อง';
            }
        }
        
        if (strlen($newPassword) < 6) {
            $errors[] = 'รหัสผ่านใหม่ต้องมีอย่างน้อย 6 ตัวอักษร';
        }
        
        if ($newPassword !== $confirmPassword) {
            $errors[] = 'รหัสผ่านใหม่และการยืนยันไม่ตรงกัน';
        }
    }
    
    // ถ้ามี error ให้ return
    if (!empty($errors)) {
        echo json_encode(['success' => false, 'message' => implode(', ', $errors)]);
        exit;
    }
    
    // สร้าง SQL update ตามฟิลด์ที่มีอยู่
    $setParts = [];
    $params = [];
    
    // อัพเดทชื่อ-นามสกุล
    if (in_array('full_name', $availableFields)) {
        $setParts[] = 'full_name = ?';
        $params[] = $fullName;
    }
    
    // อัพเดท email
    if (in_array('email', $availableFields)) {
        $setParts[] = 'email = ?';
        $params[] = $email;
    }
    
    // อัพเดทรหัสผ่าน (ถ้ามีการเปลี่ยน)
    if (!empty($newPassword) && in_array('password', $availableFields)) {
        $setParts[] = 'password = ?';
        $params[] = password_hash($newPassword, PASSWORD_DEFAULT);
    }
    
    // อัพเดท updated_date (ถ้ามีฟิลด์นี้)
    if (in_array('updated_date', $availableFields)) {
        $setParts[] = 'updated_date = NOW()';
    }
    
    // ถ้าไม่มีอะไรให้อัพเดท
    if (empty($setParts)) {
        echo json_encode(['success' => false, 'message' => 'ไม่มีข้อมูลที่ต้องอัพเดท']);
        exit;
    }
    
    // ทำการอัพเดท
    $sql = "UPDATE users SET " . implode(', ', $setParts) . " WHERE id = ?";
    $params[] = $userId;
    
    $stmt = $pdo->prepare($sql);
    $result = $stmt->execute($params);
    
    if ($result) {
        // อัพเดท session ข้อมูลใหม่
        if (in_array('full_name', $availableFields)) {
            $_SESSION['full_name'] = $fullName;
        }
        
        echo json_encode([
            'success' => true, 
            'message' => 'อัพเดทโปรไฟล์สำเร็จ'
        ]);
    } else {
        echo json_encode([
            'success' => false, 
            'message' => 'เกิดข้อผิดพลาดในการอัพเดทข้อมูล'
        ]);
    }
    
} catch (Exception $e) {
    error_log("Update profile error: " . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'เกิดข้อผิดพลาดในการอัพเดทโปรไฟล์'
    ]);
}
?>
