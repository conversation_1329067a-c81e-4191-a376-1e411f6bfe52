<?php
// อัพเดทโครงสร้างตาราง assets ให้ตรงกับที่ต้องการ
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🔧 อัพเดทโครงสร้างตาราง Assets</h2>";

// เชื่อมต่อฐานข้อมูล
try {
    $pdo = new PDO("mysql:host=localhost;dbname=asset_management;charset=utf8", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✅ เชื่อมต่อฐานข้อมูลสำเร็จ</p>";
} catch (Exception $e) {
    die("<p style='color: red;'>❌ ไม่สามารถเชื่อมต่อฐานข้อมูล: " . $e->getMessage() . "</p>");
}

// ตรวจสอบโครงสร้างตารางปัจจุบัน
echo "<h3>โครงสร้างตารางปัจจุบัน:</h3>";
try {
    $stmt = $pdo->query("DESCRIBE assets");
    $columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; background: white;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 10px;'>Field</th>";
    echo "<th style='padding: 10px;'>Type</th>";
    echo "<th style='padding: 10px;'>Null</th>";
    echo "<th style='padding: 10px;'>Key</th>";
    echo "<th style='padding: 10px;'>Default</th>";
    echo "</tr>";
    
    $existingColumns = [];
    foreach ($columns as $column) {
        $existingColumns[] = $column['Field'];
        echo "<tr>";
        echo "<td style='padding: 10px;'><strong>{$column['Field']}</strong></td>";
        echo "<td style='padding: 10px;'>{$column['Type']}</td>";
        echo "<td style='padding: 10px;'>{$column['Null']}</td>";
        echo "<td style='padding: 10px;'>{$column['Key']}</td>";
        echo "<td style='padding: 10px;'>{$column['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<p><strong>คอลัมน์ที่มีอยู่:</strong> " . implode(', ', $existingColumns) . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ ไม่สามารถดูโครงสร้างตาราง: " . $e->getMessage() . "</p>";
}

// อัพเดทโครงสร้างตาราง
if ($_POST && isset($_POST['update_structure'])) {
    echo "<h3>🔧 กำลังอัพเดทโครงสร้างตาราง...</h3>";
    
    $updates = [];
    
    // ตรวจสอบและเปลี่ยนชื่อคอลัมน์
    $columnMappings = [
        'person_added' => 'created_by',
        'person_modified' => 'updated_by',
        'date_added' => 'created_date',
        'date_modified' => 'updated_date'
    ];
    
    foreach ($columnMappings as $oldName => $newName) {
        if (in_array($oldName, $existingColumns) && !in_array($newName, $existingColumns)) {
            try {
                $sql = "ALTER TABLE assets CHANGE $oldName $newName VARCHAR(100)";
                if ($newName == 'created_date' || $newName == 'updated_date') {
                    $sql = "ALTER TABLE assets CHANGE $oldName $newName DATETIME";
                    if ($newName == 'created_date') {
                        $sql .= " DEFAULT CURRENT_TIMESTAMP";
                    } elseif ($newName == 'updated_date') {
                        $sql .= " DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP";
                    }
                }
                
                $pdo->exec($sql);
                echo "<p style='color: green;'>✅ เปลี่ยนชื่อคอลัมน์ '$oldName' เป็น '$newName' สำเร็จ</p>";
                $updates[] = "$oldName → $newName";
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ ไม่สามารถเปลี่ยนชื่อคอลัมน์ '$oldName': " . $e->getMessage() . "</p>";
            }
        } elseif (in_array($newName, $existingColumns)) {
            echo "<p style='color: blue;'>ℹ️ คอลัมน์ '$newName' มีอยู่แล้ว</p>";
        } elseif (!in_array($oldName, $existingColumns)) {
            // เพิ่มคอลัมน์ใหม่ถ้าไม่มี
            try {
                $columnType = "VARCHAR(100)";
                if ($newName == 'created_date') {
                    $columnType = "DATETIME DEFAULT CURRENT_TIMESTAMP";
                } elseif ($newName == 'updated_date') {
                    $columnType = "DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP";
                }
                
                $sql = "ALTER TABLE assets ADD COLUMN $newName $columnType";
                $pdo->exec($sql);
                echo "<p style='color: green;'>✅ เพิ่มคอลัมน์ '$newName' สำเร็จ</p>";
                $updates[] = "เพิ่ม $newName";
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ ไม่สามารถเพิ่มคอลัมน์ '$newName': " . $e->getMessage() . "</p>";
            }
        }
    }
    
    if (empty($updates)) {
        echo "<p style='color: blue;'>ℹ️ ไม่มีการเปลี่ยนแปลงโครงสร้างตาราง</p>";
    } else {
        echo "<p style='color: green;'><strong>✅ อัพเดทโครงสร้างตารางเสร็จสิ้น</strong></p>";
        echo "<ul>";
        foreach ($updates as $update) {
            echo "<li>$update</li>";
        }
        echo "</ul>";
    }
    
    echo "<p><a href='update_table_structure.php'>รีเฟรชหน้าเพื่อดูผลลัพธ์</a></p>";
}

// ทดสอบ INSERT หลังอัพเดท
if ($_POST && isset($_POST['test_insert'])) {
    echo "<h3>🧪 ทดสอบ INSERT หลังอัพเดท:</h3>";
    
    try {
        $sql = "INSERT INTO assets (type, brand, model, tag, department, status, hostname, operating_system, serial_number, asset_id, warranty_expire, description, set_name, created_by, updated_by, created_date, updated_date) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";
        
        echo "<h4>SQL ที่ใช้:</h4>";
        echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 0.9rem;'>";
        echo htmlspecialchars($sql);
        echo "</div>";
        
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([
            'Desktop',
            'Dell',
            'OptiPlex 7090',
            'TAG-TEST',
            'IT Department',
            'ใช้งาน',
            'PC-TEST',
            'Windows 10',
            'SN123456789',
            'ASSET-TEST',
            '2025-12-31',
            'Test Asset Description',
            'Test Set',
            'admin',
            'admin'
        ]);
        
        if ($result) {
            $newId = $pdo->lastInsertId();
            echo "<p style='color: green;'>✅ INSERT สำเร็จ! ID: $newId</p>";
            
            // แสดงข้อมูลที่เพิ่ม
            $checkStmt = $pdo->prepare("SELECT * FROM assets WHERE id = ?");
            $checkStmt->execute([$newId]);
            $newRecord = $checkStmt->fetch();
            
            echo "<h4>ข้อมูลที่เพิ่ม:</h4>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%; background: white;'>";
            echo "<tr style='background: #f8f9fa;'><th style='padding: 8px;'>Field</th><th style='padding: 8px;'>Value</th></tr>";
            foreach ($newRecord as $key => $value) {
                if (!is_numeric($key)) {
                    echo "<tr><td style='padding: 8px;'><strong>$key</strong></td><td style='padding: 8px;'>$value</td></tr>";
                }
            }
            echo "</table>";
            
            // ลบข้อมูลทดสอบ
            $deleteStmt = $pdo->prepare("DELETE FROM assets WHERE id = ?");
            $deleteStmt->execute([$newId]);
            echo "<p style='color: blue;'>🗑️ ลบข้อมูลทดสอบแล้ว</p>";
            
        } else {
            echo "<p style='color: red;'>❌ INSERT ไม่สำเร็จ</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ INSERT error: " . $e->getMessage() . "</p>";
        echo "<p style='color: red;'>Error Code: " . $e->getCode() . "</p>";
    }
}

// แสดงโครงสร้างตารางที่ต้องการ
echo "<h3>📋 โครงสร้างตารางที่ต้องการ:</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 0.9rem;'>";
echo htmlspecialchars("
CREATE TABLE assets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    type VARCHAR(100),
    brand VARCHAR(100),
    model VARCHAR(100),
    tag VARCHAR(100),
    department VARCHAR(100),
    status VARCHAR(50) DEFAULT 'ใช้งาน',
    hostname VARCHAR(100),
    operating_system VARCHAR(100),
    serial_number VARCHAR(100),
    asset_id VARCHAR(100),
    warranty_expire DATE,
    description TEXT,
    set_name VARCHAR(100),
    created_by VARCHAR(100),
    updated_by VARCHAR(100),
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
");
echo "</div>";

?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>อัพเดทโครงสร้างตาราง Assets</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h2 {
            background: #fd7e14;
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .action-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        button {
            background: #28a745;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
        }
        button:hover {
            background: #218838;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>

<div class="action-section">
    <h3>🔧 อัพเดทโครงสร้างตาราง</h3>
    <p>เปลี่ยนชื่อคอลัมน์ให้ตรงกับที่ต้องการ:</p>
    <ul>
        <li>person_added → created_by</li>
        <li>person_modified → updated_by</li>
        <li>date_added → created_date</li>
        <li>date_modified → updated_date</li>
    </ul>
    <form method="POST">
        <input type="hidden" name="update_structure" value="1">
        <button type="submit">🔧 อัพเดทโครงสร้าง</button>
    </form>
</div>

<div class="action-section">
    <h3>🧪 ทดสอบ INSERT หลังอัพเดท</h3>
    <p>ทดสอบการเพิ่มข้อมูลหลังจากอัพเดทโครงสร้างแล้ว</p>
    <form method="POST">
        <input type="hidden" name="test_insert" value="1">
        <button type="submit">🧪 ทดสอบ INSERT</button>
    </form>
</div>

<div class="warning">
    <strong>⚠️ หมายเหตุ:</strong> การอัพเดทโครงสร้างจะเปลี่ยนชื่อคอลัมน์ในตาราง assets ไม่มีการลบหรือแก้ไขข้อมูลที่มีอยู่
</div>

<div style="margin: 20px 0;">
    <a href="add_asset.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">➕ ไปหน้าเพิ่ม Asset</a>
    <a href="index.php" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">🏠 กลับหน้าหลัก</a>
</div>

</body>
</html>
