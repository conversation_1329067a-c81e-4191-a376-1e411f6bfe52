<?php
// ไฟล์สำหรับแก้ไข Asset ID ให้สามารถซ้ำกันได้
// รันไฟล์นี้เพียงครั้งเดียวแล้วลบทิ้ง

require_once 'config/database.php';

try {
    echo "<h2>กำลังแก้ไข Asset ID ให้สามารถซ้ำกันได้...</h2>";
    
    // ลบ UNIQUE constraint จาก asset_id
    try {
        $pdo->exec("ALTER TABLE assets DROP INDEX asset_id");
        echo "<p style='color: green;'>✅ ลบ UNIQUE constraint จาก asset_id สำเร็จ</p>";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), "check that column/key exists") !== false) {
            echo "<p style='color: orange;'>⚠️ UNIQUE constraint ไม่มีอยู่แล้ว (ข้ามขั้นตอนนี้)</p>";
        } else {
            throw $e;
        }
    }
    
    // เพิ่ม index ธรรมดาสำหรับ asset_id
    try {
        $pdo->exec("ALTER TABLE assets ADD INDEX idx_asset_id (asset_id)");
        echo "<p style='color: green;'>✅ เพิ่ม index สำหรับ asset_id สำเร็จ</p>";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), "Duplicate key name") !== false) {
            echo "<p style='color: orange;'>⚠️ Index มีอยู่แล้ว (ข้ามขั้นตอนนี้)</p>";
        } else {
            throw $e;
        }
    }
    
    // ตรวจสอบโครงสร้างตาราง
    $stmt = $pdo->query("SHOW CREATE TABLE assets");
    $result = $stmt->fetch();
    
    echo "<h3>โครงสร้างตารางหลังการแก้ไข:</h3>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px;'>";
    echo htmlspecialchars($result['Create Table']);
    echo "</pre>";
    
    // ทดสอบเพิ่มข้อมูล Asset ID ซ้ำ
    echo "<h3>ทดสอบเพิ่ม Asset ID ซ้ำ:</h3>";
    
    try {
        $stmt = $pdo->prepare("INSERT INTO assets (type, asset_id, created_by, updated_by) VALUES (?, ?, ?, ?)");
        $stmt->execute(['Test Type 1', 'TEST001', 'system', 'system']);
        echo "<p style='color: green;'>✅ เพิ่ม Asset ID: TEST001 (ครั้งที่ 1) สำเร็จ</p>";
        
        $stmt->execute(['Test Type 2', 'TEST001', 'system', 'system']);
        echo "<p style='color: green;'>✅ เพิ่ม Asset ID: TEST001 (ครั้งที่ 2) สำเร็จ - Asset ID สามารถซ้ำกันได้แล้ว!</p>";
        
        // ลบข้อมูลทดสอบ
        $pdo->exec("DELETE FROM assets WHERE asset_id = 'TEST001'");
        echo "<p style='color: blue;'>🗑️ ลบข้อมูลทดสอบแล้ว</p>";
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ เกิดข้อผิดพลาดในการทดสอบ: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2 style='color: green;'>🎉 การแก้ไขเสร็จสมบูรณ์!</h2>";
    echo "<p><strong>ตอนนี้ Asset ID สามารถซ้ำกันได้แล้ว</strong></p>";
    echo "<p><a href='index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>กลับไปหน้าหลัก</a></p>";
    echo "<p style='color: red; margin-top: 20px;'><strong>หมายเหตุ:</strong> กรุณาลบไฟล์ fix_asset_id.php หลังจากรันเสร็จแล้ว</p>";
    
} catch (PDOException $e) {
    echo "<h2 style='color: red;'>❌ เกิดข้อผิดพลาด</h2>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<p>กรุณาตรวจสอบการเชื่อมต่อฐานข้อมูลและลองใหม่</p>";
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>แก้ไข Asset ID - Asset Management System</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- เนื้อหาจะแสดงจาก PHP ด้านบน -->
    </div>
</body>
</html>
