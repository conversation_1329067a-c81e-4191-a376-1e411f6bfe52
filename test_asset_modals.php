<?php
// ทดสอบ Asset Modals (View และ Edit)
require_once 'includes/auth.php';

// ตรวจสอบการล็อกอิน
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

echo "<h2>ทดสอบ Asset Modals (View และ Edit)</h2>";

try {
    // ตรวจสอบไฟล์ที่จำเป็น
    echo "<h3>1. ตรวจสอบไฟล์ที่จำเป็น</h3>";
    
    $requiredFiles = [
        'get_asset_data.php' => 'API สำหรับดึงข้อมูล Asset',
        'update_asset.php' => 'API สำหรับอัพเดท Asset',
        'index.php' => 'หน้าหลักที่มี Asset Modals',
        'assets/style.css' => 'CSS สำหรับ Asset Modals'
    ];
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 8px;'>File</th>";
    echo "<th style='padding: 8px;'>Description</th>";
    echo "<th style='padding: 8px;'>Status</th>";
    echo "</tr>";
    
    foreach ($requiredFiles as $file => $description) {
        $exists = file_exists($file);
        $icon = $exists ? '✅' : '❌';
        $status = $exists ? 'มี' : 'ไม่มี';
        
        echo "<tr>";
        echo "<td style='padding: 8px;'>$file</td>";
        echo "<td style='padding: 8px;'>$description</td>";
        echo "<td style='padding: 8px;'>$icon $status</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // ตรวจสอบ Assets ในระบบ
    echo "<h3>2. ตรวจสอบ Assets ในระบบ</h3>";
    
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM assets");
    $assetCount = $stmt->fetch()['total'];
    
    if ($assetCount == 0) {
        echo "<p style='color: red;'>❌ ไม่มี Assets ในระบบ</p>";
        echo "<p>กรุณาเพิ่ม Asset ก่อนทดสอบ Modal</p>";
    } else {
        echo "<p style='color: green;'>✅ พบ Assets ในระบบ: $assetCount รายการ</p>";
        
        // แสดง Assets ตัวอย่าง
        $stmt = $pdo->query("SELECT id, type, brand, model, status FROM assets LIMIT 5");
        $assets = $stmt->fetchAll();
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>Type</th>";
        echo "<th style='padding: 8px;'>Brand</th>";
        echo "<th style='padding: 8px;'>Model</th>";
        echo "<th style='padding: 8px;'>Status</th>";
        echo "<th style='padding: 8px;'>Test Actions</th>";
        echo "</tr>";
        
        foreach ($assets as $asset) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>{$asset['id']}</td>";
            echo "<td style='padding: 8px;'>{$asset['type']}</td>";
            echo "<td style='padding: 8px;'>{$asset['brand']}</td>";
            echo "<td style='padding: 8px;'>{$asset['model']}</td>";
            echo "<td style='padding: 8px;'>{$asset['status']}</td>";
            echo "<td style='padding: 8px;'>";
            echo "<a href='get_asset_data.php?id={$asset['id']}' target='_blank' style='background: #007bff; color: white; padding: 5px 10px; text-decoration: none; border-radius: 5px; margin-right: 5px;'>📊 Test API</a>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // ตรวจสอบ JavaScript Functions
    echo "<h3>3. ตรวจสอบ JavaScript Functions ใน index.php</h3>";
    
    if (file_exists('index.php')) {
        $indexContent = file_get_contents('index.php');
        
        $jsFunctions = [
            'openViewAssetModal' => 'เปิด View Asset Modal',
            'openEditAssetModal' => 'เปิด Edit Asset Modal',
            'closeViewAssetModal' => 'ปิด View Asset Modal',
            'closeEditAssetModal' => 'ปิด Edit Asset Modal',
            'loadAssetData' => 'โหลดข้อมูล Asset',
            'populateViewModal' => 'แสดงข้อมูลใน View Modal',
            'populateEditModal' => 'แสดงข้อมูลใน Edit Modal',
            'restoreEditModalStructure' => 'กู้คืน HTML structure สำหรับ Edit Modal',
            'saveAsset' => 'บันทึกการแก้ไข Asset'
        ];
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>Function</th>";
        echo "<th style='padding: 8px;'>Purpose</th>";
        echo "<th style='padding: 8px;'>Status</th>";
        echo "</tr>";
        
        foreach ($jsFunctions as $function => $purpose) {
            $exists = strpos($indexContent, "function $function") !== false;
            $icon = $exists ? '✅' : '❌';
            $status = $exists ? 'มี' : 'ไม่มี';
            
            echo "<tr>";
            echo "<td style='padding: 8px; font-family: monospace;'>$function()</td>";
            echo "<td style='padding: 8px;'>$purpose</td>";
            echo "<td style='padding: 8px;'>$icon $status</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // ตรวจสอบ HTML Modals
    echo "<h3>4. ตรวจสอบ HTML Modals ใน index.php</h3>";
    
    if (file_exists('index.php')) {
        $indexContent = file_get_contents('index.php');
        
        $modalElements = [
            'viewAssetModal' => 'View Asset Modal container',
            'editAssetModal' => 'Edit Asset Modal container',
            'view_asset_id' => 'Element สำหรับแสดง Asset ID',
            'edit_asset_id_hidden' => 'Hidden input สำหรับ Asset ID',
            'editAssetForm' => 'Form สำหรับแก้ไข Asset'
        ];
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>Element ID</th>";
        echo "<th style='padding: 8px;'>Purpose</th>";
        echo "<th style='padding: 8px;'>Status</th>";
        echo "</tr>";
        
        foreach ($modalElements as $element => $purpose) {
            $exists = strpos($indexContent, "id=\"$element\"") !== false;
            $icon = $exists ? '✅' : '❌';
            $status = $exists ? 'มี' : 'ไม่มี';
            
            echo "<tr>";
            echo "<td style='padding: 8px; font-family: monospace;'>#$element</td>";
            echo "<td style='padding: 8px;'>$purpose</td>";
            echo "<td style='padding: 8px;'>$icon $status</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // ตรวจสอบ CSS สำหรับ Asset Modals
    echo "<h3>5. ตรวจสอบ CSS สำหรับ Asset Modals</h3>";
    
    if (file_exists('assets/style.css')) {
        $cssContent = file_get_contents('assets/style.css');
        
        $assetModalCss = [
            '.asset-modal' => 'CSS สำหรับ Asset Modal container',
            '.asset-details' => 'CSS สำหรับแสดงรายละเอียด Asset',
            '.asset-details .detail-row' => 'CSS สำหรับแถวข้อมูล',
            '.asset-modal .form-row' => 'CSS สำหรับแถวฟอร์ม',
            '.asset-modal .form-control' => 'CSS สำหรับ form controls'
        ];
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>CSS Class</th>";
        echo "<th style='padding: 8px;'>Purpose</th>";
        echo "<th style='padding: 8px;'>Status</th>";
        echo "</tr>";
        
        foreach ($assetModalCss as $class => $purpose) {
            $exists = strpos($cssContent, $class) !== false;
            $icon = $exists ? '✅' : '❌';
            $status = $exists ? 'มี' : 'ไม่มี';
            
            echo "<tr>";
            echo "<td style='padding: 8px; font-family: monospace;'>$class</td>";
            echo "<td style='padding: 8px;'>$purpose</td>";
            echo "<td style='padding: 8px;'>$icon $status</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // ทดสอบ API
    echo "<h3>6. ทดสอบ API</h3>";
    
    if ($assetCount > 0) {
        $stmt = $pdo->query("SELECT id FROM assets LIMIT 1");
        $testAsset = $stmt->fetch();
        $testId = $testAsset['id'];
        
        echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0;'>";
        echo "<h4>ทดสอบ get_asset_data.php:</h4>";
        echo "<p>Asset ID สำหรับทดสอบ: $testId</p>";
        echo "<a href='get_asset_data.php?id=$testId' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔍 ทดสอบ API</a>";
        echo "</div>";
    }
    
    echo "<h3>7. สรุปการทดสอบ</h3>";
    
    $allFilesExist = file_exists('get_asset_data.php') && file_exists('update_asset.php') && file_exists('index.php');
    
    if ($allFilesExist && $assetCount > 0) {
        echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #c3e6cb;'>";
        echo "<h4 style='color: #155724;'>✅ Asset Modals พร้อมใช้งาน</h4>";
        echo "<ul style='color: #155724;'>";
        echo "<li>✅ ไฟล์ API ครบถ้วน</li>";
        echo "<li>✅ JavaScript functions ครบถ้วน</li>";
        echo "<li>✅ HTML Modals ครบถ้วน</li>";
        echo "<li>✅ CSS สำหรับ Modals ครบถ้วน</li>";
        echo "<li>✅ มี Assets สำหรับทดสอบ</li>";
        echo "</ul>";
        echo "</div>";
        
        echo "<h4>วิธีใช้งาน:</h4>";
        echo "<ol>";
        echo "<li>ไปหน้า index.php</li>";
        echo "<li>คลิกปุ่ม 👁️ เพื่อดูรายละเอียด Asset</li>";
        echo "<li>คลิกปุ่ม ✏️ เพื่อแก้ไข Asset</li>";
        echo "<li>Modal จะเปิดขึ้นมาโดยไม่ต้องโหลดหน้าใหม่</li>";
        echo "</ol>";
    } else {
        echo "<div style='background: #f8d7da; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #f5c6cb;'>";
        echo "<h4 style='color: #721c24;'>⚠️ Asset Modals ยังไม่พร้อมใช้งาน</h4>";
        echo "<p style='color: #721c24;'>กรุณาตรวจสอบปัญหาที่พบ</p>";
        echo "</div>";
    }
    
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🏠 ไปหน้าหลัก</a>";
    if ($assetCount > 0) {
        echo "<a href='get_asset_data.php?id=" . $assets[0]['id'] . "' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔍 ทดสอบ API</a>";
    }
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ เกิดข้อผิดพลาด</h3>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<p style='color: red; margin-top: 20px;'><strong>หมายเหตุ:</strong> กรุณาลบไฟล์ test_asset_modals.php หลังจากทดสอบเสร็จแล้ว</p>";
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบ Asset Modals</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        h2 {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 10px 25px rgba(0, 123, 255, 0.3);
        }
        table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <!-- เนื้อหาจะแสดงจาก PHP ด้านบน -->
</body>
</html>
