<?php
// ทดสอบ add_asset.php แบบ Full Insert
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🔍 ทดสอบ add_asset.php แบบ Full Insert</h2>";

// เริ่ม session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// ตั้งค่า session
if (!isset($_SESSION['username'])) {
    $_SESSION['username'] = 'admin';
    $_SESSION['role'] = 'Admin';
    $_SESSION['user_id'] = 1;
    echo "<p style='color: blue;'>ℹ️ ตั้งค่า session: admin/Admin</p>";
}

// สร้าง function getCurrentUsername
if (!function_exists('getCurrentUsername')) {
    function getCurrentUsername() {
        return $_SESSION['username'] ?? 'admin';
    }
}

// ตรวจสอบการเชื่อมต่อฐานข้อมูล
echo "<h3>1. ตรวจสอบการเชื่อมต่อฐานข้อมูล:</h3>";
try {
    $pdo = new PDO("mysql:host=localhost;dbname=asset_management;charset=utf8", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✅ เชื่อมต่อฐานข้อมูลสำเร็จ</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ ไม่สามารถเชื่อมต่อฐานข้อมูล: " . $e->getMessage() . "</p>";
    die();
}

// ตรวจสอบตาราง assets
echo "<h3>2. ตรวจสอบตาราง assets:</h3>";
try {
    $stmt = $pdo->query("DESCRIBE assets");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "<p style='color: green;'>✅ ตาราง assets มีอยู่</p>";
    echo "<p><strong>คอลัมน์ทั้งหมด:</strong> " . implode(', ', $columns) . "</p>";
    
    // ตรวจสอบคอลัมน์ที่สำคัญ
    $requiredColumns = ['type', 'brand', 'model', 'tag', 'department', 'status', 'hostname', 'operating_system', 'serial_number', 'asset_id', 'warranty_expire', 'description', 'set_name'];
    $userColumns = ['created_by', 'person_added', 'updated_by', 'person_modified'];
    $dateColumns = ['created_date', 'date_added', 'updated_date', 'date_modified'];
    
    echo "<h4>ตรวจสอบคอลัมน์:</h4>";
    echo "<ul>";
    foreach ($requiredColumns as $col) {
        $exists = in_array($col, $columns);
        $icon = $exists ? '✅' : '❌';
        echo "<li>$icon <strong>$col</strong></li>";
    }
    
    echo "<li><strong>User columns:</strong></li>";
    foreach ($userColumns as $col) {
        $exists = in_array($col, $columns);
        $icon = $exists ? '✅' : '⚪';
        echo "<li>$icon <strong>$col</strong></li>";
    }
    
    echo "<li><strong>Date columns:</strong></li>";
    foreach ($dateColumns as $col) {
        $exists = in_array($col, $columns);
        $icon = $exists ? '✅' : '⚪';
        echo "<li>$icon <strong>$col</strong></li>";
    }
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ ตาราง assets ไม่มี: " . $e->getMessage() . "</p>";
    die();
}

// ทดสอบ Full Insert
echo "<h3>3. ทดสอบ Full Insert:</h3>";
if ($_POST && isset($_POST['full_insert'])) {
    try {
        // ข้อมูลครบถ้วนสำหรับทดสอบ
        $testData = [
            'type' => trim($_POST['type'] ?? 'Desktop'),
            'brand' => trim($_POST['brand'] ?? 'Dell'),
            'model' => trim($_POST['model'] ?? 'OptiPlex 7090'),
            'tag' => trim($_POST['tag'] ?? 'TAG-001'),
            'department' => trim($_POST['department'] ?? 'IT Department'),
            'status' => trim($_POST['status'] ?? 'ใช้งาน'),
            'hostname' => trim($_POST['hostname'] ?? 'PC-001'),
            'operating_system' => trim($_POST['operating_system'] ?? 'Windows 10'),
            'serial_number' => trim($_POST['serial_number'] ?? 'SN123456789'),
            'asset_id' => trim($_POST['asset_id'] ?? 'ASSET-001'),
            'warranty_expire' => !empty($_POST['warranty_expire']) ? $_POST['warranty_expire'] : '2025-12-31',
            'description' => trim($_POST['description'] ?? 'Test Asset Description'),
            'set_name' => trim($_POST['set_name'] ?? 'Test Set'),
            'created_by' => getCurrentUsername(),
            'updated_by' => getCurrentUsername()
        ];
        
        echo "<h4>ข้อมูลที่จะทดสอบ:</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'><th style='padding: 8px;'>Field</th><th style='padding: 8px;'>Value</th></tr>";
        foreach ($testData as $key => $value) {
            echo "<tr><td style='padding: 8px;'><strong>$key</strong></td><td style='padding: 8px;'>$value</td></tr>";
        }
        echo "</table>";
        
        // ตรวจสอบโครงสร้างตารางก่อน
        $stmt = $pdo->query("DESCRIBE assets");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        // สร้าง SQL แบบ dynamic ตามโครงสร้างตารางจริง
        $fields = [];
        $values = [];
        $placeholders = [];
        
        // ฟิลด์พื้นฐาน
        $basicFields = [
            'type' => $testData['type'],
            'brand' => $testData['brand'],
            'model' => $testData['model'],
            'tag' => $testData['tag'],
            'department' => $testData['department'],
            'status' => $testData['status'],
            'hostname' => $testData['hostname'],
            'operating_system' => $testData['operating_system'],
            'serial_number' => $testData['serial_number'],
            'asset_id' => $testData['asset_id'],
            'warranty_expire' => $testData['warranty_expire'],
            'description' => $testData['description'],
            'set_name' => $testData['set_name']
        ];
        
        // เพิ่มฟิลด์พื้นฐาน
        foreach ($basicFields as $field => $value) {
            if (in_array($field, $columns)) {
                $fields[] = $field;
                $values[] = $value;
                $placeholders[] = '?';
                echo "<p style='color: green;'>✅ เพิ่มฟิลด์: <strong>$field</strong> = $value</p>";
            } else {
                echo "<p style='color: red;'>❌ ไม่พบฟิลด์: <strong>$field</strong></p>";
            }
        }
        
        // เพิ่มฟิลด์ user - ตรวจสอบชื่อที่มีอยู่
        if (in_array('created_by', $columns)) {
            $fields[] = 'created_by';
            $values[] = $testData['created_by'];
            $placeholders[] = '?';
            echo "<p style='color: green;'>✅ เพิ่มฟิลด์: <strong>created_by</strong> = {$testData['created_by']}</p>";
        } elseif (in_array('person_added', $columns)) {
            $fields[] = 'person_added';
            $values[] = $testData['created_by'];
            $placeholders[] = '?';
            echo "<p style='color: green;'>✅ เพิ่มฟิลด์: <strong>person_added</strong> = {$testData['created_by']}</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ ไม่พบฟิลด์ created_by หรือ person_added</p>";
        }
        
        if (in_array('updated_by', $columns)) {
            $fields[] = 'updated_by';
            $values[] = $testData['updated_by'];
            $placeholders[] = '?';
            echo "<p style='color: green;'>✅ เพิ่มฟิลด์: <strong>updated_by</strong> = {$testData['updated_by']}</p>";
        } elseif (in_array('person_modified', $columns)) {
            $fields[] = 'person_modified';
            $values[] = $testData['updated_by'];
            $placeholders[] = '?';
            echo "<p style='color: green;'>✅ เพิ่มฟิลด์: <strong>person_modified</strong> = {$testData['updated_by']}</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ ไม่พบฟิลด์ updated_by หรือ person_modified</p>";
        }
        
        // เพิ่มฟิลด์วันที่ - ตรวจสอบชื่อที่มีอยู่
        $currentDateTime = date('Y-m-d H:i:s');
        
        if (in_array('created_date', $columns)) {
            $fields[] = 'created_date';
            $values[] = $currentDateTime;
            $placeholders[] = '?';
            echo "<p style='color: green;'>✅ เพิ่มฟิลด์: <strong>created_date</strong> = $currentDateTime</p>";
        } elseif (in_array('date_added', $columns)) {
            $fields[] = 'date_added';
            $values[] = $currentDateTime;
            $placeholders[] = '?';
            echo "<p style='color: green;'>✅ เพิ่มฟิลด์: <strong>date_added</strong> = $currentDateTime</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ ไม่พบฟิลด์ created_date หรือ date_added</p>";
        }
        
        if (in_array('updated_date', $columns)) {
            $fields[] = 'updated_date';
            $values[] = $currentDateTime;
            $placeholders[] = '?';
            echo "<p style='color: green;'>✅ เพิ่มฟิลด์: <strong>updated_date</strong> = $currentDateTime</p>";
        } elseif (in_array('date_modified', $columns)) {
            $fields[] = 'date_modified';
            $values[] = $currentDateTime;
            $placeholders[] = '?';
            echo "<p style='color: green;'>✅ เพิ่มฟิลด์: <strong>date_modified</strong> = $currentDateTime</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ ไม่พบฟิลด์ updated_date หรือ date_modified</p>";
        }
        
        // สร้าง SQL
        $sql = "INSERT INTO assets (" . implode(', ', $fields) . ") VALUES (" . implode(', ', $placeholders) . ")";
        
        echo "<h4>SQL ที่จะใช้:</h4>";
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 0.9rem;'>";
        echo htmlspecialchars($sql);
        echo "</div>";
        
        echo "<h4>ฟิลด์และค่าที่จะใส่:</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'><th style='padding: 8px;'>Index</th><th style='padding: 8px;'>Field</th><th style='padding: 8px;'>Value</th></tr>";
        for ($i = 0; $i < count($fields); $i++) {
            echo "<tr><td style='padding: 8px;'>$i</td><td style='padding: 8px;'><strong>{$fields[$i]}</strong></td><td style='padding: 8px;'>{$values[$i]}</td></tr>";
        }
        echo "</table>";
        
        echo "<p><strong>จำนวนฟิลด์:</strong> " . count($fields) . "</p>";
        echo "<p><strong>จำนวนค่า:</strong> " . count($values) . "</p>";
        echo "<p><strong>จำนวน placeholders:</strong> " . count($placeholders) . "</p>";
        
        if (count($fields) === count($values) && count($values) === count($placeholders)) {
            echo "<p style='color: green;'>✅ จำนวนฟิลด์และค่าตรงกัน</p>";
            
            // ทำการ INSERT
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute($values);
            
            if ($result) {
                $newId = $pdo->lastInsertId();
                echo "<p style='color: green;'>✅ Full Insert สำเร็จ! ID: $newId</p>";
                
                // แสดงข้อมูลที่เพิ่ม
                $checkStmt = $pdo->prepare("SELECT * FROM assets WHERE id = ?");
                $checkStmt->execute([$newId]);
                $newAsset = $checkStmt->fetch();
                
                echo "<h4>ข้อมูล Asset ที่เพิ่มแล้ว:</h4>";
                echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
                echo "<tr style='background: #f8f9fa;'><th style='padding: 8px;'>Field</th><th style='padding: 8px;'>Value</th></tr>";
                foreach ($newAsset as $key => $value) {
                    if (!is_numeric($key)) {
                        echo "<tr><td style='padding: 8px;'><strong>$key</strong></td><td style='padding: 8px;'>$value</td></tr>";
                    }
                }
                echo "</table>";
                
                echo "<p style='color: blue;'>ℹ️ Asset ถูกเพิ่มในระบบแล้ว (ไม่ลบ)</p>";
                
            } else {
                echo "<p style='color: red;'>❌ Full Insert ไม่สำเร็จ</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ จำนวนฟิลด์และค่าไม่ตรงกัน</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ ข้อผิดพลาด: " . $e->getMessage() . "</p>";
        echo "<p style='color: red;'>Error Code: " . $e->getCode() . "</p>";
        echo "<p style='color: red;'>File: " . $e->getFile() . "</p>";
        echo "<p style='color: red;'>Line: " . $e->getLine() . "</p>";
        echo "<h5>Stack Trace:</h5>";
        echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; font-size: 0.8rem;'>";
        echo htmlspecialchars($e->getTraceAsString());
        echo "</pre>";
    }
}

// ทดสอบการเรียก add_asset.php ด้วยข้อมูลครบถ้วน
echo "<h3>4. ทดสอบการเรียก add_asset.php:</h3>";
if ($_POST && isset($_POST['test_add_asset_full'])) {
    echo "<h4>กำลังทดสอบการเรียก add_asset.php ด้วยข้อมูลครบถ้วน...</h4>";
    
    // ข้อมูลครบถ้วนสำหรับส่งไป add_asset.php
    $postData = http_build_query([
        'type' => 'Desktop',
        'brand' => 'Dell',
        'model' => 'OptiPlex 7090',
        'tag' => 'TAG-FULL-001',
        'department' => 'IT Department',
        'status' => 'ใช้งาน',
        'hostname' => 'PC-FULL-001',
        'operating_system' => 'Windows 10',
        'serial_number' => 'SN987654321',
        'asset_id' => 'ASSET-FULL-001',
        'warranty_expire' => '2025-12-31',
        'description' => 'Full Test Asset Description',
        'set_name' => 'Full Test Set'
    ]);
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-type: application/x-www-form-urlencoded',
            'content' => $postData
        ]
    ]);
    
    try {
        $result = file_get_contents('http://localhost/asset/add_asset.php', false, $context);
        
        if ($result !== false) {
            echo "<p style='color: green;'>✅ เรียก add_asset.php สำเร็จ</p>";
            
            // ตรวจสอบผลลัพธ์
            if (strpos($result, 'เพิ่ม Asset สำเร็จ') !== false) {
                echo "<p style='color: green;'>✅ พบข้อความสำเร็จในผลลัพธ์</p>";
            } elseif (strpos($result, 'error') !== false || strpos($result, 'Error') !== false || strpos($result, 'Fatal') !== false) {
                echo "<p style='color: red;'>❌ พบข้อผิดพลาดในผลลัพธ์</p>";
            } else {
                echo "<p style='color: blue;'>ℹ️ ไม่พบข้อความสำเร็จหรือข้อผิดพลาดที่ชัดเจน</p>";
            }
            
            // แสดงส่วนหนึ่งของผลลัพธ์
            echo "<h5>ส่วนหนึ่งของผลลัพธ์:</h5>";
            echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; max-height: 400px; overflow-y: auto; font-family: monospace; font-size: 0.8rem;'>";
            echo htmlspecialchars(substr($result, 0, 3000));
            if (strlen($result) > 3000) {
                echo "...(ตัดทอน)";
            }
            echo "</div>";
            
        } else {
            echo "<p style='color: red;'>❌ ไม่สามารถเรียก add_asset.php ได้</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ ข้อผิดพลาดในการเรียก add_asset.php: " . $e->getMessage() . "</p>";
    }
}

?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบ add_asset.php แบบ Full Insert</title>
    <style>
        body {
            font-family: 'TH Sarabun New', Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h2 {
            background: #dc3545;
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        h3 {
            background: #6c757d;
            color: white;
            padding: 10px;
            border-radius: 5px;
        }
        .form-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
        }
        .form-group {
            flex: 1;
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 16px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #218838;
        }
        .btn-danger {
            background: #dc3545;
        }
        .btn-danger:hover {
            background: #c82333;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>

<div class="form-section">
    <h3>🧪 ทดสอบ Full Insert ในหน้านี้</h3>
    <form method="POST">
        <div class="form-row">
            <div class="form-group">
                <label for="type">Type *</label>
                <select id="type" name="type" required>
                    <option value="">เลือก Type</option>
                    <option value="Desktop">Desktop</option>
                    <option value="Laptop">Laptop</option>
                    <option value="Monitor">Monitor</option>
                    <option value="All-in-one">All-in-one</option>
                    <option value="Multifunction Laser Printer">Multifunction Laser Printer</option>
                    <option value="Barcode Printer">Barcode Printer</option>
                    <option value="Barcode Scanner">Barcode Scanner</option>
                    <option value="Tablet">Tablet</option>
                    <option value="UPS">UPS</option>
                    <option value="Queue">Queue</option>
                    <option value="IP Phone">IP Phone</option>
                    <option value="Teleconference">Teleconference</option>
                    <option value="Switch">Switch</option>
                    <option value="Access Point">Access Point</option>
                    <option value="Peripheral">Peripheral</option>
                </select>
            </div>
            <div class="form-group">
                <label for="brand">Brand</label>
                <select id="brand" name="brand">
                    <option value="">เลือก Brand</option>
                    <option value="-">-</option>
                    <option value="Dell">Dell</option>
                    <option value="Lenovo">Lenovo</option>
                    <option value="Microsoft">Microsoft</option>
                    <option value="Apple">Apple</option>
                    <option value="Zebra">Zebra</option>
                    <option value="HP">HP</option>
                    <option value="Philips">Philips</option>
                    <option value="Acer">Acer</option>
                    <option value="LG">LG</option>
                    <option value="Cisco">Cisco</option>
                </select>
            </div>
        </div>
        
        <div class="form-row">
            <div class="form-group">
                <label for="model">Model</label>
                <input type="text" id="model" name="model" value="OptiPlex 7090">
            </div>
            <div class="form-group">
                <label for="tag">Tag</label>
                <input type="text" id="tag" name="tag" value="TAG-001">
            </div>
        </div>
        
        <div class="form-row">
            <div class="form-group">
                <label for="department">Department</label>
                <input type="text" id="department" name="department" value="IT Department">
            </div>
            <div class="form-group">
                <label for="status">Status</label>
                <select id="status" name="status">
                    <option value="ใช้งาน">ใช้งาน</option>
                    <option value="ชำรุด">ชำรุด</option>
                    <option value="สำรอง">สำรอง</option>
                </select>
            </div>
        </div>
        
        <div class="form-row">
            <div class="form-group">
                <label for="hostname">Hostname</label>
                <input type="text" id="hostname" name="hostname" value="PC-001">
            </div>
            <div class="form-group">
                <label for="operating_system">Operating System</label>
                <select id="operating_system" name="operating_system">
                    <option value="Windows 10">Windows 10</option>
                    <option value="Windows 11">Windows 11</option>
                    <option value="MacOS">MacOS</option>
                </select>
            </div>
        </div>
        
        <div class="form-row">
            <div class="form-group">
                <label for="serial_number">Serial Number</label>
                <input type="text" id="serial_number" name="serial_number" value="SN123456789">
            </div>
            <div class="form-group">
                <label for="asset_id">Asset ID</label>
                <input type="text" id="asset_id" name="asset_id" value="ASSET-001">
            </div>
        </div>
        
        <div class="form-row">
            <div class="form-group">
                <label for="warranty_expire">Warranty Expire</label>
                <input type="date" id="warranty_expire" name="warranty_expire" value="2025-12-31">
            </div>
            <div class="form-group">
                <label for="set_name">Set</label>
                <input type="text" id="set_name" name="set_name" value="Test Set">
            </div>
        </div>
        
        <div class="form-group">
            <label for="description">Description</label>
            <textarea id="description" name="description" rows="3">Test Asset Description</textarea>
        </div>
        
        <div class="form-group">
            <input type="hidden" name="full_insert" value="1">
            <button type="submit" class="btn-success">🧪 ทดสอบ Full Insert</button>
        </div>
    </form>
</div>

<div class="form-section">
    <h3>🔗 ทดสอบเรียก add_asset.php แบบ Full</h3>
    <form method="POST">
        <input type="hidden" name="test_add_asset_full" value="1">
        <button type="submit" class="btn-danger">🔗 ทดสอบเรียก add_asset.php</button>
    </form>
</div>

<div style="margin: 20px 0;">
    <a href="add_asset.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">➕ ไปหน้าเพิ่ม Asset</a>
    <a href="simple_test_add.php" style="background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">🧪 ทดสอบง่าย</a>
    <a href="index.php" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">🏠 หน้าหลัก</a>
</div>

</body>
</html>
