<?php
// ทดสอบ Profile Modal ที่สวยงาม
require_once 'includes/auth.php';

// ตรวจสอบการล็อกอิน
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

echo "<h2>ทดสอบ Profile Modal ที่สวยงาม</h2>";

try {
    // ตรวจสอบ CSS ใหม่
    echo "<h3>1. ตรวจสอบ CSS ที่อัพเดท</h3>";
    
    if (file_exists('assets/style.css')) {
        $cssContent = file_get_contents('assets/style.css');
        
        $newCssClasses = [
            '.profile-modal' => 'Modal container ใหม่',
            '.profile-user-info' => 'ส่วนแสดงข้อมูลผู้ใช้',
            '.profile-username' => 'แสดงชื่อผู้ใช้',
            '.profile-role-display' => 'แสดง role แบบสวยงาม',
            '.profile-edit-form' => 'ฟอร์มแก้ไขที่สวยงาม',
            '.avatar-circle::before' => 'Animation shimmer',
            '@keyframes shimmer' => 'Animation สำหรับ avatar',
            '.detail-row:hover' => 'Hover effect',
            '.role-badge.admin' => 'Badge สำหรับ Admin',
            '.status-badge.inactive' => 'Badge สำหรับ Inactive'
        ];
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>CSS Class/Animation</th>";
        echo "<th style='padding: 8px;'>Description</th>";
        echo "<th style='padding: 8px;'>Status</th>";
        echo "</tr>";
        
        foreach ($newCssClasses as $class => $description) {
            $exists = strpos($cssContent, $class) !== false;
            $icon = $exists ? '✅' : '❌';
            $status = $exists ? 'มี' : 'ไม่มี';
            
            echo "<tr>";
            echo "<td style='padding: 8px; font-family: monospace;'>$class</td>";
            echo "<td style='padding: 8px;'>$description</td>";
            echo "<td style='padding: 8px;'>$icon $status</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>❌ ไม่พบไฟล์ assets/style.css</p>";
    }
    
    // ตรวจสอบ HTML ใหม่
    echo "<h3>2. ตรวจสอบ HTML Structure ใหม่</h3>";
    
    $htmlFiles = ['index.php', 'users.php'];
    $newHtmlElements = [
        'profile_username_display' => 'แสดงชื่อผู้ใช้ใน header',
        'profile_role_display' => 'แสดง role ใน header',
        'profile_role_text' => 'ข้อความ role',
        'profile-edit-form' => 'Container ฟอร์มแก้ไข',
        'placeholder=' => 'Placeholder ในฟอร์ม'
    ];
    
    foreach ($htmlFiles as $file) {
        if (file_exists($file)) {
            echo "<h4>ไฟล์: $file</h4>";
            $content = file_get_contents($file);
            
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 8px;'>Element/Attribute</th>";
            echo "<th style='padding: 8px;'>Description</th>";
            echo "<th style='padding: 8px;'>Status</th>";
            echo "</tr>";
            
            foreach ($newHtmlElements as $element => $description) {
                $exists = strpos($content, $element) !== false;
                $icon = $exists ? '✅' : '❌';
                $status = $exists ? 'มี' : 'ไม่มี';
                
                echo "<tr>";
                echo "<td style='padding: 8px; font-family: monospace;'>$element</td>";
                echo "<td style='padding: 8px;'>$description</td>";
                echo "<td style='padding: 8px;'>$icon $status</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
    // ตรวจสอบ JavaScript ใหม่
    echo "<h3>3. ตรวจสอบ JavaScript ที่อัพเดท</h3>";
    
    $jsUpdates = [
        'profile_username_display' => 'อัพเดทชื่อผู้ใช้ใน header',
        'profile_role_text' => 'อัพเดท role text',
        'role-badge admin' => 'กำหนด class admin',
        'status-badge inactive' => 'กำหนด class inactive',
        'roleIcon' => 'เปลี่ยนไอคอน role'
    ];
    
    foreach ($htmlFiles as $file) {
        if (file_exists($file)) {
            echo "<h4>JavaScript ใน $file:</h4>";
            $content = file_get_contents($file);
            
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 8px;'>JavaScript Feature</th>";
            echo "<th style='padding: 8px;'>Description</th>";
            echo "<th style='padding: 8px;'>Status</th>";
            echo "</tr>";
            
            foreach ($jsUpdates as $feature => $description) {
                $exists = strpos($content, $feature) !== false;
                $icon = $exists ? '✅' : '❌';
                $status = $exists ? 'มี' : 'ไม่มี';
                
                echo "<tr>";
                echo "<td style='padding: 8px; font-family: monospace;'>$feature</td>";
                echo "<td style='padding: 8px;'>$description</td>";
                echo "<td style='padding: 8px;'>$icon $status</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
    // ตรวจสอบ Emoji และ Icons
    echo "<h3>4. ตรวจสอบ Emoji และ Icons</h3>";
    
    $emojiList = [
        '👤' => 'User icon',
        '👑' => 'Admin crown',
        '🏷️' => 'Username label',
        '📧' => 'Email label',
        '🎭' => 'Role label',
        '🟢' => 'Status label',
        '🕐' => 'Time label',
        '📅' => 'Date label',
        '🔐' => 'Current password',
        '🔑' => 'New password',
        '✅' => 'Confirm password',
        '✏️' => 'Edit button',
        '💾' => 'Save button'
    ];
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 8px;'>Emoji</th>";
    echo "<th style='padding: 8px;'>Usage</th>";
    echo "<th style='padding: 8px;'>Status in Files</th>";
    echo "</tr>";
    
    foreach ($emojiList as $emoji => $usage) {
        $foundInFiles = [];
        foreach ($htmlFiles as $file) {
            if (file_exists($file)) {
                $content = file_get_contents($file);
                if (strpos($content, $emoji) !== false) {
                    $foundInFiles[] = $file;
                }
            }
        }
        
        $status = !empty($foundInFiles) ? '✅ ' . implode(', ', $foundInFiles) : '❌ ไม่พบ';
        
        echo "<tr>";
        echo "<td style='padding: 8px; font-size: 1.2rem; text-align: center;'>$emoji</td>";
        echo "<td style='padding: 8px;'>$usage</td>";
        echo "<td style='padding: 8px;'>$status</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // ทดสอบ API
    echo "<h3>5. ทดสอบ API Response</h3>";
    
    if (file_exists('get_profile_data.php')) {
        ob_start();
        include 'get_profile_data.php';
        $response = ob_get_clean();
        
        $data = json_decode($response, true);
        
        if ($data && isset($data['success']) && $data['success']) {
            echo "<p style='color: green;'>✅ API ทำงานได้ถูกต้อง</p>";
            echo "<h4>ข้อมูลที่ได้รับ:</h4>";
            echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
            echo "<strong>Username:</strong> " . ($data['user']['username'] ?? 'N/A') . "<br>";
            echo "<strong>Full Name:</strong> " . ($data['user']['full_name'] ?? 'N/A') . "<br>";
            echo "<strong>Email:</strong> " . ($data['user']['email'] ?? 'N/A') . "<br>";
            echo "<strong>Role:</strong> " . ($data['user']['role'] ?? 'N/A') . "<br>";
            echo "<strong>Status:</strong> " . ($data['user']['status'] ?? 'N/A') . "<br>";
            echo "</div>";
        } else {
            echo "<p style='color: red;'>❌ API มีปัญหา</p>";
        }
    }
    
    echo "<h3>6. สรุปการปรับปรุง</h3>";
    
    echo "<div style='background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 15px; margin: 20px 0;'>";
    echo "<h4 style='color: white; margin-top: 0;'>🎨 การปรับปรุงที่ทำ:</h4>";
    echo "<ul style='color: white;'>";
    echo "<li>✨ <strong>Gradient Background:</strong> พื้นหลังไล่โทนสวยงาม</li>";
    echo "<li>🎭 <strong>Avatar Animation:</strong> เอฟเฟกต์ shimmer บน avatar</li>";
    echo "<li>👑 <strong>Role Display:</strong> แสดง role แบบพิเศษใน header</li>";
    echo "<li>🎯 <strong>Hover Effects:</strong> เอฟเฟกต์เมื่อ hover บนแถวข้อมูล</li>";
    echo "<li>🏷️ <strong>Emoji Labels:</strong> ใช้ emoji แทนไอคอน</li>";
    echo "<li>💎 <strong>Enhanced Badges:</strong> Badge สำหรับ role และ status</li>";
    echo "<li>📱 <strong>Better Forms:</strong> ฟอร์มที่สวยงามและใช้งานง่าย</li>";
    echo "<li>🎨 <strong>Color Coding:</strong> สีที่แตกต่างสำหรับ Admin/User</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #c3e6cb;'>";
    echo "<h4 style='color: #155724;'>✅ ฟีเจอร์ใหม่:</h4>";
    echo "<ul style='color: #155724;'>";
    echo "<li>🎨 <strong>Modern Design:</strong> ดีไซน์ทันสมัยด้วย gradient และ shadow</li>";
    echo "<li>⚡ <strong>Smooth Animations:</strong> การเคลื่อนไหวที่นุ่มนวล</li>";
    echo "<li>🎯 <strong>Interactive Elements:</strong> ปุ่มและฟอร์มที่ตอบสนอง</li>";
    echo "<li>📱 <strong>Mobile Friendly:</strong> ใช้งานได้ดีบนมือถือ</li>";
    echo "<li>🔍 <strong>Visual Hierarchy:</strong> การจัดลำดับข้อมูลที่ชัดเจน</li>";
    echo "<li>🎪 <strong>Consistent Styling:</strong> สไตล์ที่สอดคล้องกันทั้งระบบ</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='index.php' style='background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 12px 24px; text-decoration: none; border-radius: 10px; margin-right: 10px; display: inline-flex; align-items: center; gap: 8px;'>🏠 ทดสอบใน index.php</a>";
    echo "<a href='users.php' style='background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 12px 24px; text-decoration: none; border-radius: 10px; margin-right: 10px; display: inline-flex; align-items: center; gap: 8px;'>👥 ทดสอบใน users.php</a>";
    echo "<a href='get_profile_data.php' style='background: #6c757d; color: white; padding: 12px 24px; text-decoration: none; border-radius: 10px; display: inline-flex; align-items: center; gap: 8px;'>🔍 ดู API Response</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ เกิดข้อผิดพลาด</h3>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<p style='color: red; margin-top: 20px;'><strong>หมายเหตุ:</strong> กรุณาลบไฟล์ test_beautiful_profile.php หลังจากทดสอบเสร็จแล้ว</p>";
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบ Profile Modal ที่สวยงาม</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        h2 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
        table {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <!-- เนื้อหาจะแสดงจาก PHP ด้านบน -->
</body>
</html>
