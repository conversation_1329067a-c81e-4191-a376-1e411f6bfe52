<?php
require_once 'includes/auth.php';

// ตรวจสอบการล็อกอินและสิทธิ์ Admin
if (!$auth->isLoggedIn() || !isAdmin()) {
    header('Location: login.php');
    exit;
}

$message = '';
$messageType = '';
$user = null;

// ตรวจสอบ ID ผู้ใช้
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: users.php');
    exit;
}

$userId = (int)$_GET['id'];

// ดึงข้อมูลผู้ใช้
try {
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$userId]);
    $user = $stmt->fetch();
    
    if (!$user) {
        header('Location: users.php');
        exit;
    }
} catch (PDOException $e) {
    $message = 'เกิดข้อผิดพลาดในการดึงข้อมูลผู้ใช้';
    $messageType = 'danger';
}

// ตรวจสอบฟิลด์ที่มีอยู่ในฐานข้อมูล
$availableFields = [];
try {
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll();
    foreach ($columns as $column) {
        $availableFields[] = $column['Field'];
    }
} catch (PDOException $e) {
    $message = 'ไม่สามารถตรวจสอบโครงสร้างฐานข้อมูลได้';
    $messageType = 'danger';
}

$hasFullName = in_array('full_name', $availableFields);
$hasEmail = in_array('email', $availableFields);
$hasRole = in_array('role', $availableFields);
$hasStatus = in_array('status', $availableFields);

// ประมวลผลการอัพเดท
if ($_POST && $user) {
    $username = trim($_POST['username'] ?? '');
    $fullName = $hasFullName ? trim($_POST['full_name'] ?? '') : '';
    $email = $hasEmail ? trim($_POST['email'] ?? '') : '';
    $role = $hasRole ? ($_POST['role'] ?? 'User') : 'User';
    $status = $hasStatus ? ($_POST['status'] ?? 'Active') : 'Active';
    $password = $_POST['password'] ?? '';
    
    // ตรวจสอบข้อมูล
    if (empty($username)) {
        $message = 'กรุณากรอก Username';
        $messageType = 'danger';
    } elseif (strlen($username) < 3) {
        $message = 'Username ต้องมีอย่างน้อย 3 ตัวอักษร';
        $messageType = 'danger';
    } elseif ($hasFullName && empty($fullName)) {
        $message = 'กรุณากรอกชื่อ-นามสกุล';
        $messageType = 'danger';
    } elseif ($hasEmail && !empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $message = 'รูปแบบ Email ไม่ถูกต้อง';
        $messageType = 'danger';
    } else {
        // ตรวจสอบ username ซ้ำ (ยกเว้นตัวเอง)
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = ? AND id != ?");
        $stmt->execute([$username, $userId]);
        
        if ($stmt->fetchColumn() > 0) {
            $message = 'Username นี้มีอยู่แล้ว';
            $messageType = 'danger';
        } else {
            try {
                // สร้าง SQL update ตามฟิลด์ที่มีอยู่
                $setParts = ['username = ?'];
                $params = [$username];
                
                if ($hasFullName) {
                    $setParts[] = 'full_name = ?';
                    $params[] = $fullName;
                }
                
                if ($hasEmail) {
                    $setParts[] = 'email = ?';
                    $params[] = $email;
                }
                
                if ($hasRole) {
                    $setParts[] = 'role = ?';
                    $params[] = $role;
                }
                
                if ($hasStatus) {
                    $setParts[] = 'status = ?';
                    $params[] = $status;
                }
                
                // อัพเดทรหัสผ่าน (ถ้ามีการกรอก)
                if (!empty($password)) {
                    if (strlen($password) < 6) {
                        $message = 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร';
                        $messageType = 'danger';
                    } else {
                        $setParts[] = 'password = ?';
                        $params[] = password_hash($password, PASSWORD_DEFAULT);
                    }
                }
                
                // อัพเดท updated_date (ถ้ามีฟิลด์นี้)
                if (in_array('updated_date', $availableFields)) {
                    $setParts[] = 'updated_date = NOW()';
                }
                
                if (empty($message)) {
                    $sql = "UPDATE users SET " . implode(', ', $setParts) . " WHERE id = ?";
                    $params[] = $userId;
                    
                    $stmt = $pdo->prepare($sql);
                    $result = $stmt->execute($params);
                    
                    if ($result) {
                        $message = 'อัพเดทข้อมูลผู้ใช้สำเร็จ';
                        $messageType = 'success';
                        
                        // ดึงข้อมูลใหม่
                        $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
                        $stmt->execute([$userId]);
                        $user = $stmt->fetch();
                    } else {
                        $message = 'เกิดข้อผิดพลาดในการอัพเดทข้อมูล';
                        $messageType = 'danger';
                    }
                }
            } catch (PDOException $e) {
                $message = 'เกิดข้อผิดพลาดในการอัพเดทข้อมูล: ' . $e->getMessage();
                $messageType = 'danger';
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>แก้ไขผู้ใช้ - Asset Management System</title>
    <link rel="stylesheet" href="assets/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Sarabun:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 แก้ไขผู้ใช้</h1>
            <div class="user-info">
                <span>👤 <?= htmlspecialchars($_SESSION['full_name'] ?? $_SESSION['username']) ?></span>
                <span class="role-badge <?= strtolower($_SESSION['role'] ?? 'user') ?>"><?= $_SESSION['role'] ?? 'User' ?></span>
            </div>
        </div>

        <nav class="nav">
            <ul>
                <li><a href="index.php">รายการ Assets</a></li>
                <li><a href="users.php" class="active">จัดการผู้ใช้</a></li>
                <li><a href="#" onclick="openProfileModal()">โปรไฟล์</a></li>
                <li><a href="logout.php">ออกจากระบบ</a></li>
            </ul>
        </nav>

        <div class="content">
            <?php if ($message): ?>
                <div class="alert alert-<?= $messageType ?>">
                    <?= htmlspecialchars($message) ?>
                </div>
            <?php endif; ?>

            <?php if ($user): ?>
                <div class="form-container">
                    <div class="form-header">
                        <h2>✏️ แก้ไขข้อมูลผู้ใช้: <?= htmlspecialchars($user['username']) ?></h2>
                        <a href="users.php" class="btn btn-secondary">← กลับ</a>
                    </div>

                    <form method="POST" action="" class="edit-user-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="username">🏷️ Username *</label>
                                <input type="text" id="username" name="username" class="form-control" 
                                       value="<?= htmlspecialchars($user['username']) ?>" 
                                       required minlength="3" placeholder="กรอก Username">
                            </div>

                            <?php if ($hasFullName): ?>
                            <div class="form-group">
                                <label for="full_name">👤 ชื่อ-นามสกุล *</label>
                                <input type="text" id="full_name" name="full_name" class="form-control" 
                                       value="<?= htmlspecialchars($user['full_name'] ?? '') ?>" 
                                       required placeholder="กรอกชื่อ-นามสกุล">
                            </div>
                            <?php endif; ?>
                        </div>

                        <div class="form-row">
                            <?php if ($hasEmail): ?>
                            <div class="form-group">
                                <label for="email">📧 Email</label>
                                <input type="email" id="email" name="email" class="form-control" 
                                       value="<?= htmlspecialchars($user['email'] ?? '') ?>" 
                                       placeholder="กรอก Email">
                            </div>
                            <?php endif; ?>

                            <?php if ($hasRole): ?>
                            <div class="form-group">
                                <label for="role">🎭 บทบาท</label>
                                <select id="role" name="role" class="form-control">
                                    <option value="User" <?= ($user['role'] ?? '') === 'User' ? 'selected' : '' ?>>👤 User</option>
                                    <option value="Admin" <?= ($user['role'] ?? '') === 'Admin' ? 'selected' : '' ?>>👑 Admin</option>
                                </select>
                            </div>
                            <?php endif; ?>
                        </div>

                        <div class="form-row">
                            <?php if ($hasStatus): ?>
                            <div class="form-group">
                                <label for="status">🟢 สถานะ</label>
                                <select id="status" name="status" class="form-control">
                                    <option value="Active" <?= ($user['status'] ?? '') === 'Active' ? 'selected' : '' ?>>🟢 Active</option>
                                    <option value="Inactive" <?= ($user['status'] ?? '') === 'Inactive' ? 'selected' : '' ?>>🔴 Inactive</option>
                                </select>
                            </div>
                            <?php endif; ?>

                            <div class="form-group">
                                <label for="password">🔑 รหัสผ่านใหม่</label>
                                <input type="password" id="password" name="password" class="form-control" 
                                       placeholder="กรอกรหัสผ่านใหม่ (ถ้าต้องการเปลี่ยน)" minlength="6">
                                <small class="form-text">ปล่อยว่างไว้หากไม่ต้องการเปลี่ยนรหัสผ่าน</small>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn btn-success">
                                💾 บันทึกการเปลี่ยนแปลง
                            </button>
                            <a href="users.php" class="btn btn-secondary">ยกเลิก</a>
                        </div>
                    </form>
                </div>

                <!-- ข้อมูลเพิ่มเติม -->
                <div class="info-card">
                    <h3>📊 ข้อมูลเพิ่มเติม</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <label>🆔 User ID:</label>
                            <span><?= $user['id'] ?></span>
                        </div>
                        <?php if (isset($user['created_date'])): ?>
                        <div class="info-item">
                            <label>📅 วันที่สร้าง:</label>
                            <span><?= $user['created_date'] ? date('d/m/Y H:i:s', strtotime($user['created_date'])) : '-' ?></span>
                        </div>
                        <?php endif; ?>
                        <?php if (isset($user['updated_date'])): ?>
                        <div class="info-item">
                            <label>🔄 แก้ไขล่าสุด:</label>
                            <span><?= $user['updated_date'] ? date('d/m/Y H:i:s', strtotime($user['updated_date'])) : '-' ?></span>
                        </div>
                        <?php endif; ?>
                        <?php if (isset($user['last_login'])): ?>
                        <div class="info-item">
                            <label>🕐 เข้าสู่ระบบล่าสุด:</label>
                            <span><?= $user['last_login'] ? date('d/m/Y H:i:s', strtotime($user['last_login'])) : '-' ?></span>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script>
        // Form validation
        document.querySelector('.edit-user-form').addEventListener('submit', function(e) {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            
            if (username.length < 3) {
                e.preventDefault();
                alert('Username ต้องมีอย่างน้อย 3 ตัวอักษร');
                return;
            }
            
            if (password && password.length < 6) {
                e.preventDefault();
                alert('รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร');
                return;
            }
            
            <?php if ($hasFullName): ?>
            const fullName = document.getElementById('full_name').value.trim();
            if (!fullName) {
                e.preventDefault();
                alert('กรุณากรอกชื่อ-นามสกุล');
                return;
            }
            <?php endif; ?>
        });

        // Auto focus on username field
        document.getElementById('username').focus();
    </script>
</body>
</html>
