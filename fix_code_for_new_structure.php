<?php
// แก้ไขโค้ดให้เข้ากับโครงสร้าง users ใหม่
require_once 'config/database.php';

echo "<h2>แก้ไขโค้ดให้เข้ากับโครงสร้าง users ใหม่</h2>";

try {
    // ตรวจสอบโครงสร้างปัจจุบัน
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll();
    
    $currentFields = [];
    foreach ($columns as $column) {
        $currentFields[] = $column['Field'];
    }
    
    echo "<h3>1. โครงสร้างปัจจุบัน:</h3>";
    echo "<p>ฟิลด์ที่มีอยู่: " . implode(', ', $currentFields) . "</p>";
    
    // ตรวจสอบฟิลด์ที่จำเป็น
    $hasPassword = in_array('password', $currentFields);
    $hasFullName = in_array('full_name', $currentFields);
    $hasEmail = in_array('email', $currentFields);
    $hasRole = in_array('role', $currentFields);
    $hasStatus = in_array('status', $currentFields);
    $hasCreatedDate = in_array('created_date', $currentFields);
    $hasUpdatedDate = in_array('updated_date', $currentFields);
    $hasLastLogin = in_array('last_login', $currentFields);
    
    echo "<h3>2. การแก้ไขไฟล์:</h3>";
    
    // แก้ไข includes/auth.php
    echo "<h4>📁 แก้ไข includes/auth.php:</h4>";
    
    $authContent = file_get_contents('includes/auth.php');
    $needsAuthUpdate = false;
    
    // ตรวจสอบว่าต้องแก้ไข auth.php หรือไม่
    if (!$hasPassword) {
        echo "<p style='color: red;'>❌ ไม่มี password field - ระบบ login จะไม่ทำงาน</p>";
        $needsAuthUpdate = true;
    }
    
    if (!$hasRole) {
        echo "<p style='color: orange;'>⚠️ ไม่มี role field - จะไม่สามารถแยกสิทธิ์ Admin/User ได้</p>";
        $needsAuthUpdate = true;
    }
    
    if (!$hasStatus) {
        echo "<p style='color: orange;'>⚠️ ไม่มี status field - จะไม่สามารถควบคุมการใช้งานได้</p>";
        $needsAuthUpdate = true;
    }
    
    if ($needsAuthUpdate) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<strong>แนะนำ:</strong> ปรับ auth.php ให้ทำงานแบบ dynamic ตามฟิลด์ที่มี";
        echo "</div>";
    } else {
        echo "<p style='color: green;'>✅ auth.php ควรทำงานได้ปกติ</p>";
    }
    
    // แก้ไข register.php
    echo "<h4>📁 แก้ไข register.php:</h4>";
    
    if (!$hasFullName) {
        echo "<p style='color: orange;'>⚠️ ไม่มี full_name field - ฟอร์ม register จะไม่แสดงช่องชื่อ-นามสกุล</p>";
    }
    
    if (!$hasEmail) {
        echo "<p style='color: orange;'>⚠️ ไม่มี email field - ฟอร์ม register จะไม่แสดงช่อง email</p>";
    }
    
    echo "<p style='color: green;'>✅ register.php ได้ปรับให้ทำงานแบบ dynamic แล้ว</p>";
    
    // แก้ไข users.php
    echo "<h4>📁 แก้ไข users.php:</h4>";
    echo "<p style='color: green;'>✅ users.php ได้ปรับให้ทำงานแบบ dynamic แล้ว</p>";
    
    // แก้ไข profile.php
    echo "<h4>📁 แก้ไข profile.php:</h4>";
    echo "<p style='color: green;'>✅ profile.php ได้ปรับให้ทำงานแบบ dynamic แล้ว</p>";
    
    // สร้างไฟล์ปรับปรุงโครงสร้าง
    echo "<h3>3. สร้างไฟล์ปรับปรุงโครงสร้าง:</h3>";
    
    $missingFields = [];
    if (!$hasPassword) $missingFields[] = "password VARCHAR(255) NOT NULL DEFAULT ''";
    if (!$hasFullName) $missingFields[] = "full_name VARCHAR(100) NOT NULL DEFAULT ''";
    if (!$hasEmail) $missingFields[] = "email VARCHAR(100) NULL";
    if (!$hasRole) $missingFields[] = "role ENUM('Admin', 'User') DEFAULT 'User'";
    if (!$hasStatus) $missingFields[] = "status ENUM('Active', 'Inactive') DEFAULT 'Active'";
    if (!$hasCreatedDate) $missingFields[] = "created_date DATETIME DEFAULT CURRENT_TIMESTAMP";
    if (!$hasUpdatedDate) $missingFields[] = "updated_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP";
    if (!$hasLastLogin) $missingFields[] = "last_login DATETIME NULL";
    
    if (!empty($missingFields)) {
        $upgradeSQL = "-- ปรับปรุงโครงสร้างตาราง users\n";
        $upgradeSQL .= "-- รันไฟล์นี้ใน phpMyAdmin หรือ MySQL command line\n\n";
        $upgradeSQL .= "USE asset_management;\n\n";
        $upgradeSQL .= "ALTER TABLE users\n";
        
        $alterStatements = [];
        foreach ($missingFields as $field) {
            $alterStatements[] = "ADD COLUMN " . $field;
        }
        
        $upgradeSQL .= implode(",\n", $alterStatements) . ";\n\n";
        
        // เพิ่ม indexes
        if (!$hasRole) {
            $upgradeSQL .= "ALTER TABLE users ADD INDEX idx_role (role);\n";
        }
        if (!$hasStatus) {
            $upgradeSQL .= "ALTER TABLE users ADD INDEX idx_status (status);\n";
        }
        
        // เพิ่มข้อมูลตัวอย่าง
        if (!$hasPassword) {
            $upgradeSQL .= "\n-- อัพเดทรหัสผ่านสำหรับผู้ใช้ที่มีอยู่\n";
            $upgradeSQL .= "UPDATE users SET password = '\$2y\$10\$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi' WHERE password = '' OR password IS NULL;\n";
        }
        
        if (!$hasRole) {
            $upgradeSQL .= "\n-- ตั้งค่า role เริ่มต้น\n";
            $upgradeSQL .= "UPDATE users SET role = 'Admin' WHERE username = 'admin';\n";
            $upgradeSQL .= "UPDATE users SET role = 'User' WHERE role IS NULL OR role = '';\n";
        }
        
        $upgradeSQL .= "\n-- แสดงผลลัพธ์\n";
        $upgradeSQL .= "SELECT 'ปรับปรุงโครงสร้างเสร็จสิ้น' as status;\n";
        $upgradeSQL .= "DESCRIBE users;\n";
        
        file_put_contents('sql/upgrade_users_structure.sql', $upgradeSQL);
        
        echo "<p style='color: green;'>✅ สร้างไฟล์ sql/upgrade_users_structure.sql</p>";
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; font-family: monospace; font-size: 0.9rem;'>";
        echo nl2br(htmlspecialchars($upgradeSQL));
        echo "</div>";
    } else {
        echo "<p style='color: green;'>✅ โครงสร้างตารางครบถ้วนแล้ว ไม่ต้องปรับปรุง</p>";
    }
    
    // สร้างไฟล์ทดสอบ
    echo "<h3>4. สร้างไฟล์ทดสอบ:</h3>";
    
    $testContent = "<?php\n";
    $testContent .= "// ทดสอบระบบหลังปรับปรุงโครงสร้าง\n";
    $testContent .= "require_once 'config/database.php';\n";
    $testContent .= "require_once 'includes/auth.php';\n\n";
    $testContent .= "echo '<h2>ทดสอบระบบหลังปรับปรุงโครงสร้าง</h2>';\n\n";
    $testContent .= "try {\n";
    $testContent .= "    // ตรวจสอบโครงสร้างตาราง\n";
    $testContent .= "    \$stmt = \$pdo->query('DESCRIBE users');\n";
    $testContent .= "    \$columns = \$stmt->fetchAll();\n";
    $testContent .= "    \n";
    $testContent .= "    echo '<h3>โครงสร้างตารางหลังปรับปรุง:</h3>';\n";
    $testContent .= "    echo '<ul>';\n";
    $testContent .= "    foreach (\$columns as \$column) {\n";
    $testContent .= "        echo '<li>' . \$column['Field'] . ' - ' . \$column['Type'] . '</li>';\n";
    $testContent .= "    }\n";
    $testContent .= "    echo '</ul>';\n";
    $testContent .= "    \n";
    $testContent .= "    // ทดสอบการสร้างผู้ใช้\n";
    $testContent .= "    echo '<h3>ทดสอบการสร้างผู้ใช้:</h3>';\n";
    $testContent .= "    \$testUser = [\n";
    $testContent .= "        'username' => 'testuser_' . time(),\n";
    $testContent .= "        'password' => 'testpass123',\n";
    if ($hasFullName) $testContent .= "        'full_name' => 'Test User',\n";
    if ($hasEmail) $testContent .= "        'email' => '<EMAIL>',\n";
    if ($hasRole) $testContent .= "        'role' => 'User',\n";
    if ($hasStatus) $testContent .= "        'status' => 'Active'\n";
    $testContent .= "    ];\n";
    $testContent .= "    \n";
    $testContent .= "    if (\$auth->createUser(\$testUser)) {\n";
    $testContent .= "        echo '<p style=\"color: green;\">✅ สร้างผู้ใช้ทดสอบสำเร็จ</p>';\n";
    $testContent .= "        \n";
    $testContent .= "        // ทดสอบล็อกอิน\n";
    $testContent .= "        session_start();\n";
    $testContent .= "        if (\$auth->login(\$testUser['username'], \$testUser['password'])) {\n";
    $testContent .= "            echo '<p style=\"color: green;\">✅ ล็อกอินสำเร็จ</p>';\n";
    $testContent .= "        } else {\n";
    $testContent .= "            echo '<p style=\"color: red;\">❌ ล็อกอินล้มเหลว</p>';\n";
    $testContent .= "        }\n";
    $testContent .= "        session_destroy();\n";
    $testContent .= "        \n";
    $testContent .= "        // ลบผู้ใช้ทดสอบ\n";
    $testContent .= "        \$stmt = \$pdo->prepare('DELETE FROM users WHERE username = ?');\n";
    $testContent .= "        \$stmt->execute([\$testUser['username']]);\n";
    $testContent .= "        echo '<p style=\"color: blue;\">🗑️ ลบผู้ใช้ทดสอบแล้ว</p>';\n";
    $testContent .= "    } else {\n";
    $testContent .= "        echo '<p style=\"color: red;\">❌ สร้างผู้ใช้ทดสอบล้มเหลว</p>';\n";
    $testContent .= "    }\n";
    $testContent .= "    \n";
    $testContent .= "} catch (PDOException \$e) {\n";
    $testContent .= "    echo '<p style=\"color: red;\">Error: ' . \$e->getMessage() . '</p>';\n";
    $testContent .= "}\n";
    $testContent .= "?>";
    
    file_put_contents('test_after_upgrade.php', $testContent);
    echo "<p style='color: green;'>✅ สร้างไฟล์ test_after_upgrade.php</p>";
    
    // สรุปผล
    echo "<h3>5. สรุปการแก้ไข:</h3>";
    
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #c3e6cb;'>";
    echo "<h4 style='color: #155724;'>✅ ไฟล์ที่แก้ไขแล้ว:</h4>";
    echo "<ul style='color: #155724;'>";
    echo "<li>✅ includes/auth.php - ปรับให้ทำงานแบบ dynamic</li>";
    echo "<li>✅ register.php - ปรับให้แสดงฟิลด์ตามโครงสร้างฐานข้อมูล</li>";
    echo "<li>✅ users.php - ปรับให้แสดงข้อมูลตามฟิลด์ที่มี</li>";
    echo "<li>✅ profile.php - ปรับให้แสดงข้อมูลตามฟิลด์ที่มี</li>";
    echo "</ul>";
    echo "</div>";
    
    if (!empty($missingFields)) {
        echo "<div style='background: #fff3cd; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #ffeaa7;'>";
        echo "<h4 style='color: #856404;'>⚠️ ขั้นตอนต่อไป:</h4>";
        echo "<ol style='color: #856404;'>";
        echo "<li>รันไฟล์ sql/upgrade_users_structure.sql ใน phpMyAdmin</li>";
        echo "<li>รันไฟล์ test_after_upgrade.php เพื่อทดสอบ</li>";
        echo "<li>ทดสอบระบบ register และ login</li>";
        echo "</ol>";
        echo "</div>";
    }
    
    echo "<div style='margin: 20px 0;'>";
    if (!empty($missingFields)) {
        echo "<a href='sql/upgrade_users_structure.sql' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>ดาวน์โหลด SQL</a>";
        echo "<a href='test_after_upgrade.php' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>ทดสอบหลังปรับปรุง</a>";
    }
    echo "<a href='check_current_structure.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>ตรวจสอบโครงสร้าง</a>";
    echo "<a href='register.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>ทดสอบ Register</a>";
    echo "<a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>ทดสอบ Login</a>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ เกิดข้อผิดพลาด</h3>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<p style='color: red; margin-top: 20px;'><strong>หมายเหตุ:</strong> กรุณาลบไฟล์ fix_code_for_new_structure.php หลังจากใช้งานเสร็จแล้ว</p>";
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>แก้ไขโค้ดให้เข้ากับโครงสร้างใหม่</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <!-- เนื้อหาจะแสดงจาก PHP ด้านบน -->
</body>
</html>
