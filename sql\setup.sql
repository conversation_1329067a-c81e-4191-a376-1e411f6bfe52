-- Asset Management System Database Setup
-- สร้างฐานข้อมูลและตาราง

CREATE DATABASE IF NOT EXISTS asset_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE asset_management;

-- ตาราง assets สำหรับเก็บข้อมูล asset
CREATE TABLE IF NOT EXISTS assets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    type VARCHAR(100) NOT NULL,
    brand VARCHAR(100),
    model VARCHAR(100),
    tag VARCHAR(50) UNIQUE,
    department VARCHAR(100),
    status ENUM('ใช้งาน', 'ชำรุด', 'สำรอง') DEFAULT 'ใช้งาน',
    hostname VARCHAR(100),
    operating_system VARCHAR(100),
    serial_number VARCHAR(100),
    asset_id VARCHAR(50) NULL,
    warranty_expire DATE,
    description TEXT,
    set_name VARCHAR(100),
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by VARCHAR(100),
    INDEX idx_type (type),
    INDEX idx_brand (brand),
    INDEX idx_department (department),
    INDEX idx_status (status),
    INDEX idx_tag (tag),
    INDEX idx_asset_id (asset_id)
);

-- ตาราง asset_logs สำหรับเก็บ log การเปลี่ยนแปลง
CREATE TABLE IF NOT EXISTS asset_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    asset_id INT NOT NULL,
    action_type ENUM('CREATE', 'UPDATE', 'DELETE') NOT NULL,
    field_name VARCHAR(100),
    old_value TEXT,
    new_value TEXT,
    changed_by VARCHAR(100),
    changed_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    description TEXT,
    FOREIGN KEY (asset_id) REFERENCES assets(id) ON DELETE CASCADE,
    INDEX idx_asset_id (asset_id),
    INDEX idx_action_type (action_type),
    INDEX idx_changed_date (changed_date)
);

-- ตาราง users สำหรับจัดการผู้ใช้และ Role
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    role ENUM('Admin', 'User') DEFAULT 'User',
    status ENUM('Active', 'Inactive') DEFAULT 'Active',
    last_login DATETIME NULL,
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_username (username),
    INDEX idx_role (role),
    INDEX idx_status (status)
);

-- เพิ่มข้อมูลผู้ใช้ตัวอย่าง (password: admin123 และ user123)
INSERT INTO users (username, password, full_name, email, role) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Administrator', '<EMAIL>', 'Admin'),
('user1', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'John Doe', '<EMAIL>', 'User');

-- ข้อมูลตัวอย่าง assets
INSERT INTO assets (type, brand, model, tag, department, status, hostname, operating_system, serial_number, asset_id, warranty_expire, description, set_name, created_by, updated_by) VALUES
('Desktop', 'Dell', 'OptiPlex 7090', 'PC001', 'IT Department', 'ใช้งาน', 'IT-PC-001', 'Windows 11', 'DL123456789', 'AST001', '2025-12-31', 'Desktop computer for IT staff', 'Office Set 1', 'admin', 'admin'),
('Laptop', 'HP', 'EliteBook 840', 'LT001', 'Sales', 'ใช้งาน', 'SALES-LT-001', 'Windows 11', 'HP987654321', 'AST002', '2024-06-30', 'Sales team laptop', 'Mobile Set 1', 'admin', 'admin'),
('Monitor', 'Samsung', '24" LED', 'MON001', 'IT Department', 'สำรอง', NULL, '-', 'SM111222333', 'AST003', '2024-12-31', '24 inch LED monitor', 'Office Set 1', 'admin', 'admin'),
('Multifunction Laser Printer', 'Canon', 'imageRUNNER 2625i', 'PRT001', 'Admin', 'ชำรุด', 'ADMIN-PRT-001', '-', 'CN445566778', 'AST004', '2025-03-15', 'Multifunction laser printer for admin department', 'Office Set 2', 'admin', 'admin'),
('Switch', 'Cisco', 'Catalyst 2960', 'SW001', 'IT Department', 'ใช้งาน', 'IT-SW-001', '-', 'CS998877665', 'AST005', '2026-01-20', '24-port managed switch', 'Network Set 1', 'admin', 'admin');
