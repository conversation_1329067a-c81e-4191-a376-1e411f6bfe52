<?php
// ทดสอบระบบ Login ที่ปรับปรุงแล้ว
require_once 'config/database.php';
require_once 'includes/auth.php';

echo "<h2>ทดสอบระบบ Login ที่ปรับปรุงแล้ว</h2>";

try {
    // ตรวจสอบตาราง users
    echo "<h3>1. ตรวจสอบตาราง users</h3>";
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✅ ตาราง users มีอยู่</p>";
        
        // ตรวจสอบ columns
        echo "<h4>Columns ในตาราง users:</h4>";
        $stmt = $pdo->query("DESCRIBE users");
        $columns = $stmt->fetchAll();
        
        echo "<ul>";
        foreach ($columns as $column) {
            $icon = $column['Field'] === 'password' ? '🔒' : '📝';
            echo "<li>$icon {$column['Field']} - {$column['Type']}</li>";
        }
        echo "</ul>";
        
        // ตรวจสอบข้อมูลผู้ใช้
        echo "<h4>ข้อมูลผู้ใช้ที่มีอยู่:</h4>";
        $stmt = $pdo->query("SELECT id, username, full_name, email, role, status, created_date FROM users");
        $users = $stmt->fetchAll();
        
        if (empty($users)) {
            echo "<p style='color: orange;'>⚠️ ไม่มีข้อมูลผู้ใช้</p>";
        } else {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 8px;'>ID</th>";
            echo "<th style='padding: 8px;'>Username</th>";
            echo "<th style='padding: 8px;'>Full Name</th>";
            echo "<th style='padding: 8px;'>Email</th>";
            echo "<th style='padding: 8px;'>Role</th>";
            echo "<th style='padding: 8px;'>Status</th>";
            echo "</tr>";
            
            foreach ($users as $user) {
                echo "<tr>";
                echo "<td style='padding: 8px;'>{$user['id']}</td>";
                echo "<td style='padding: 8px;'>{$user['username']}</td>";
                echo "<td style='padding: 8px;'>" . ($user['full_name'] ?? '-') . "</td>";
                echo "<td style='padding: 8px;'>" . ($user['email'] ?? '-') . "</td>";
                echo "<td style='padding: 8px;'>" . ($user['role'] ?? 'Admin') . "</td>";
                echo "<td style='padding: 8px;'>" . ($user['status'] ?? 'Active') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ ไม่พบตาราง users</p>";
        exit;
    }
    
    // ทดสอบการล็อกอิน
    echo "<h3>2. ทดสอบการล็อกอิน</h3>";
    
    $testAccounts = [
        ['username' => 'admin', 'password' => 'admin123', 'expected' => true],
        ['username' => 'user1', 'password' => 'admin123', 'expected' => true],
        ['username' => 'admin', 'password' => 'wrongpass', 'expected' => false],
        ['username' => 'nonexistent', 'password' => 'admin123', 'expected' => false]
    ];
    
    foreach ($testAccounts as $test) {
        echo "<h4>ทดสอบ: {$test['username']} / {$test['password']}</h4>";
        
        // ล็อกเอาท์ก่อน (ถ้ามี session)
        if (isset($_SESSION)) {
            session_destroy();
        }
        session_start();
        
        $result = $auth->login($test['username'], $test['password']);
        
        if ($result === $test['expected']) {
            if ($result) {
                echo "<p style='color: green;'>✅ ล็อกอินสำเร็จ</p>";
                echo "<ul>";
                echo "<li>User ID: " . ($_SESSION['user_id'] ?? 'N/A') . "</li>";
                echo "<li>Username: " . ($_SESSION['username'] ?? 'N/A') . "</li>";
                echo "<li>Full Name: " . ($_SESSION['full_name'] ?? 'N/A') . "</li>";
                echo "<li>Role: " . ($_SESSION['role'] ?? 'N/A') . "</li>";
                echo "</ul>";
            } else {
                echo "<p style='color: green;'>✅ ล็อกอินล้มเหลว (ตามที่คาดหวัง)</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ ผลลัพธ์ไม่ตรงตามที่คาดหวัง</p>";
        }
        
        // ล็อกเอาท์
        if (isset($_SESSION)) {
            session_destroy();
        }
    }
    
    // ทดสอบฟังก์ชันอื่นๆ
    echo "<h3>3. ทดสอบฟังก์ชันอื่นๆ</h3>";
    
    // ทดสอบ getAllUsers
    echo "<h4>ทดสอบ getAllUsers():</h4>";
    $allUsers = $auth->getAllUsers();
    echo "<p>พบผู้ใช้ " . count($allUsers) . " คน</p>";
    
    // ทดสอบ getUserById
    if (!empty($allUsers)) {
        $firstUser = $allUsers[0];
        echo "<h4>ทดสอบ getUserById({$firstUser['id']}):</h4>";
        $userById = $auth->getUserById($firstUser['id']);
        if ($userById) {
            echo "<p style='color: green;'>✅ พบผู้ใช้: {$userById['username']}</p>";
        } else {
            echo "<p style='color: red;'>❌ ไม่พบผู้ใช้</p>";
        }
    }
    
    // ทดสอบ createUser (ถ้าไม่มี test user)
    echo "<h4>ทดสอบ createUser():</h4>";
    $testUserData = [
        'username' => 'testuser_' . time(),
        'password' => 'testpass123',
        'full_name' => 'Test User',
        'email' => '<EMAIL>',
        'role' => 'User',
        'status' => 'Active'
    ];
    
    $createResult = $auth->createUser($testUserData);
    if ($createResult) {
        echo "<p style='color: green;'>✅ สร้างผู้ใช้ทดสอบสำเร็จ: {$testUserData['username']}</p>";
        
        // ทดสอบล็อกอินด้วยผู้ใช้ใหม่
        session_start();
        $loginResult = $auth->login($testUserData['username'], $testUserData['password']);
        if ($loginResult) {
            echo "<p style='color: green;'>✅ ล็อกอินด้วยผู้ใช้ใหม่สำเร็จ</p>";
        } else {
            echo "<p style='color: red;'>❌ ล็อกอินด้วยผู้ใช้ใหม่ล้มเหลว</p>";
        }
        session_destroy();
        
        // ลบผู้ใช้ทดสอบ
        $stmt = $pdo->prepare("DELETE FROM users WHERE username = ?");
        $stmt->execute([$testUserData['username']]);
        echo "<p style='color: blue;'>🗑️ ลบผู้ใช้ทดสอบแล้ว</p>";
    } else {
        echo "<p style='color: red;'>❌ สร้างผู้ใช้ทดสอบล้มเหลว</p>";
    }
    
    echo "<h3>4. สรุปผลการทดสอบ</h3>";
    echo "<p style='color: green;'>✅ ระบบ Login อิงจากตาราง users ทำงานได้ถูกต้อง</p>";
    echo "<ul>";
    echo "<li>✅ ตรวจสอบรหัสผ่านด้วย password_verify()</li>";
    echo "<li>✅ เข้ารหัสรหัสผ่านด้วย password_hash()</li>";
    echo "<li>✅ อัพเดท last_login อัตโนมัติ</li>";
    echo "<li>✅ จัดการ session อย่างปลอดภัย</li>";
    echo "<li>✅ ตรวจสอบ role และ status</li>";
    echo "<li>✅ รองรับข้อมูลเก่าที่ไม่มีรหัสผ่าน</li>";
    echo "</ul>";
    
    echo "<p><a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>ไปหน้าล็อกอิน</a></p>";
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ เกิดข้อผิดพลาด</h3>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<p style='color: red; margin-top: 20px;'><strong>หมายเหตุ:</strong> กรุณาลบไฟล์ test_improved_login.php หลังจากทดสอบเสร็จแล้ว</p>";
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบระบบ Login ที่ปรับปรุงแล้ว</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <!-- เนื้อหาจะแสดงจาก PHP ด้านบน -->
</body>
</html>
