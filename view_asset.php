<?php
require_once 'includes/functions.php';

$assetManager = new AssetManager($pdo);

// ตรวจสอบ ID
$id = $_GET['id'] ?? 0;
if (!$id) {
    header('Location: index.php');
    exit;
}

// ดึงข้อมูล asset และ logs
$asset = $assetManager->getAssetById($id);
if (!$asset) {
    header('Location: index.php');
    exit;
}

$logs = $assetManager->getAssetLogs($id);
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ดู Asset - Asset Management System</title>
    <link rel="stylesheet" href="assets/style.css">
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>ระบบจัดการ Asset</h1>
        </div>
    </div>

    <div class="container">
        <nav class="nav">
            <ul>
                <li><a href="index.php">รายการ Assets</a></li>
                <li><a href="add_asset.php">เพิ่ม Asset</a></li>
                <li><a href="edit_asset.php?id=<?= $id ?>">แก้ไข Asset</a></li>
            </ul>
        </nav>

        <!-- Asset Details -->
        <div class="card">
            <div class="card-header">
                <h2>รายละเอียด Asset: <?= htmlspecialchars($asset['asset_id']) ?></h2>
                <div style="float: right;">
                    <?= getStatusBadge($asset['status']) ?>
                </div>
            </div>
            <div class="card-body">
                <div class="form-row">
                    <div class="form-group">
                        <label>Type:</label>
                        <p><?= htmlspecialchars($asset['type']) ?></p>
                    </div>
                    <div class="form-group">
                        <label>Brand:</label>
                        <p><?= htmlspecialchars($asset['brand']) ?: '-' ?></p>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>Model:</label>
                        <p><?= htmlspecialchars($asset['model']) ?: '-' ?></p>
                    </div>
                    <div class="form-group">
                        <label>Tag:</label>
                        <p><?= htmlspecialchars($asset['tag']) ?: '-' ?></p>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>Department:</label>
                        <p><?= htmlspecialchars($asset['department']) ?: '-' ?></p>
                    </div>
                    <div class="form-group">
                        <label>Status:</label>
                        <p><?= htmlspecialchars($asset['status']) ?></p>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>Hostname:</label>
                        <p><?= htmlspecialchars($asset['hostname']) ?: '-' ?></p>
                    </div>
                    <div class="form-group">
                        <label>Operating System:</label>
                        <p><?= htmlspecialchars($asset['operating_system']) ?: '-' ?></p>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>Serial Number:</label>
                        <p><?= htmlspecialchars($asset['serial_number']) ?: '-' ?></p>
                    </div>
                    <div class="form-group">
                        <label>Asset ID:</label>
                        <p><?= htmlspecialchars($asset['asset_id']) ?></p>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>Warranty Expire:</label>
                        <p><?= formatDate($asset['warranty_expire']) ?></p>
                    </div>
                    <div class="form-group">
                        <label>Set:</label>
                        <p><?= htmlspecialchars($asset['asset_set']) ?: '-' ?></p>
                    </div>
                </div>

                <div class="form-group">
                    <label>Description:</label>
                    <p><?= htmlspecialchars($asset['description']) ?: '-' ?></p>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>วันที่เพิ่ม:</label>
                        <p><?= formatDateTime($asset['created_date']) ?></p>
                    </div>
                    <div class="form-group">
                        <label>คนเพิ่ม:</label>
                        <p><?= htmlspecialchars($asset['created_by']) ?></p>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>วันที่แก้ไขล่าสุด:</label>
                        <p><?= formatDateTime($asset['updated_date']) ?></p>
                    </div>
                    <div class="form-group">
                        <label>คนแก้ไขล่าสุด:</label>
                        <p><?= htmlspecialchars($asset['updated_by']) ?></p>
                    </div>
                </div>

                <div class="form-group">
                    <a href="edit_asset.php?id=<?= $id ?>" class="btn btn-warning">แก้ไข</a>
                    <a href="delete_asset.php?id=<?= $id ?>" class="btn btn-danger" 
                       onclick="return confirm('คุณแน่ใจหรือไม่ที่จะลบ Asset นี้?')">ลบ</a>
                    <a href="index.php" class="btn btn-secondary">กลับ</a>
                </div>
            </div>
        </div>

        <!-- Asset Logs -->
        <div class="card">
            <div class="card-header">
                <h2>ประวัติการเปลี่ยนแปลง (<?= count($logs) ?> รายการ)</h2>
            </div>
            <div class="card-body">
                <?php if (empty($logs)): ?>
                    <div class="empty-state">
                        <h3>ไม่มีประวัติการเปลี่ยนแปลง</h3>
                        <p>ยังไม่มีการบันทึกการเปลี่ยนแปลงสำหรับ Asset นี้</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($logs as $log): ?>
                        <div class="log-entry <?= strtolower($log['action_type']) ?>">
                            <strong><?= $log['action_type'] ?></strong>
                            <?php if ($log['field_name']): ?>
                                - Field: <strong><?= htmlspecialchars($log['field_name']) ?></strong>
                            <?php endif; ?>
                            
                            <?php if ($log['action_type'] === 'UPDATE' && $log['field_name']): ?>
                                <br>
                                <small>
                                    เปลี่ยนจาก: <code><?= htmlspecialchars($log['old_value']) ?: '(ว่าง)' ?></code>
                                    เป็น: <code><?= htmlspecialchars($log['new_value']) ?: '(ว่าง)' ?></code>
                                </small>
                            <?php endif; ?>
                            
                            <?php if ($log['description']): ?>
                                <br>
                                <small><?= htmlspecialchars($log['description']) ?></small>
                            <?php endif; ?>
                            
                            <div class="log-meta">
                                โดย: <?= htmlspecialchars($log['changed_by']) ?> | 
                                เมื่อ: <?= formatDateTime($log['changed_date']) ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</body>
</html>
