<?php
// ไฟล์สำหรับอัพเดท Asset ID ให้ไม่จำเป็นต้องใส่ข้อมูล
// รันไฟล์นี้เพียงครั้งเดียวแล้วลบทิ้ง

require_once 'config/database.php';

try {
    echo "<h2>กำลังอัพเดท Asset ID ให้ไม่จำเป็นต้องใส่ข้อมูล...</h2>";
    
    // แก้ไข column asset_id ให้สามารถเป็น NULL ได้
    try {
        $pdo->exec("ALTER TABLE assets MODIFY COLUMN asset_id VARCHAR(50) NULL");
        echo "<p style='color: green;'>✅ แก้ไข asset_id column ให้เป็น NULL ได้สำเร็จ</p>";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), "already") !== false) {
            echo "<p style='color: orange;'>⚠️ asset_id column เป็น NULL ได้อยู่แล้ว</p>";
        } else {
            throw $e;
        }
    }
    
    // ตรวจสอบโครงสร้างตาราง
    $stmt = $pdo->query("DESCRIBE assets");
    $columns = $stmt->fetchAll();
    
    echo "<h3>โครงสร้างตาราง assets หลังการแก้ไข:</h3>";
    echo "<table style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='border: 1px solid #ddd; padding: 10px;'>Field</th>";
    echo "<th style='border: 1px solid #ddd; padding: 10px;'>Type</th>";
    echo "<th style='border: 1px solid #ddd; padding: 10px;'>Null</th>";
    echo "<th style='border: 1px solid #ddd; padding: 10px;'>Key</th>";
    echo "<th style='border: 1px solid #ddd; padding: 10px;'>Default</th>";
    echo "</tr>";
    
    foreach ($columns as $column) {
        $highlight = ($column['Field'] === 'asset_id') ? 'background: #fff3cd;' : '';
        echo "<tr style='{$highlight}'>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$column['Field']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$column['Type']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$column['Null']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$column['Key']}</td>";
        echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$column['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // ทดสอบเพิ่มข้อมูลโดยไม่มี Asset ID
    echo "<h3>ทดสอบเพิ่ม Asset โดยไม่มี Asset ID:</h3>";
    
    try {
        $stmt = $pdo->prepare("INSERT INTO assets (type, brand, model, description, created_by, updated_by) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute(['Desktop', 'Dell', 'Test Model', 'Test asset without Asset ID', 'system', 'system']);
        $testId = $pdo->lastInsertId();
        echo "<p style='color: green;'>✅ เพิ่ม Asset โดยไม่มี Asset ID สำเร็จ (ID: {$testId})</p>";
        
        // ลบข้อมูลทดสอบ
        $pdo->exec("DELETE FROM assets WHERE id = {$testId}");
        echo "<p style='color: blue;'>🗑️ ลบข้อมูลทดสอบแล้ว</p>";
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>❌ เกิดข้อผิดพลาดในการทดสอบ: " . $e->getMessage() . "</p>";
    }
    
    // แสดงข้อมูล Asset ที่ไม่มี Asset ID
    echo "<h3>Asset ที่ไม่มี Asset ID:</h3>";
    $stmt = $pdo->query("SELECT id, type, brand, model, asset_id, created_date FROM assets WHERE asset_id IS NULL OR asset_id = '' ORDER BY created_date DESC");
    $emptyAssetIds = $stmt->fetchAll();
    
    if (empty($emptyAssetIds)) {
        echo "<p style='color: blue;'>ไม่มี Asset ที่ไม่มี Asset ID</p>";
    } else {
        echo "<table style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='border: 1px solid #ddd; padding: 10px;'>ID</th>";
        echo "<th style='border: 1px solid #ddd; padding: 10px;'>Type</th>";
        echo "<th style='border: 1px solid #ddd; padding: 10px;'>Brand</th>";
        echo "<th style='border: 1px solid #ddd; padding: 10px;'>Model</th>";
        echo "<th style='border: 1px solid #ddd; padding: 10px;'>Asset ID</th>";
        echo "<th style='border: 1px solid #ddd; padding: 10px;'>Created Date</th>";
        echo "</tr>";
        
        foreach ($emptyAssetIds as $asset) {
            echo "<tr>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$asset['id']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$asset['type']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$asset['brand']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$asset['model']}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px; color: #dc3545;'>" . ($asset['asset_id'] ?: 'NULL') . "</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$asset['created_date']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h2 style='color: green;'>🎉 การอัพเดทเสร็จสมบูรณ์!</h2>";
    echo "<p><strong>ตอนนี้ Asset ID ไม่จำเป็นต้องใส่ข้อมูลแล้ว</strong></p>";
    echo "<ul>";
    echo "<li>✅ สามารถเพิ่ม Asset โดยไม่ต้องใส่ Asset ID</li>";
    echo "<li>✅ Asset ID สามารถเป็นค่าว่างหรือ NULL ได้</li>";
    echo "<li>✅ ยังคงสามารถใส่ Asset ID ได้ตามปกติ</li>";
    echo "<li>✅ Asset ID ยังคงสามารถซ้ำกันได้</li>";
    echo "</ul>";
    echo "<p><a href='index.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>กลับไปหน้าหลัก</a></p>";
    echo "<p style='color: red; margin-top: 20px;'><strong>หมายเหตุ:</strong> กรุณาลบไฟล์ update_asset_id_nullable.php หลังจากรันเสร็จแล้ว</p>";
    
} catch (PDOException $e) {
    echo "<h2 style='color: red;'>❌ เกิดข้อผิดพลาด</h2>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<p>กรุณาตรวจสอบการเชื่อมต่อฐานข้อมูลและลองใหม่</p>";
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>อัพเดท Asset ID - Asset Management System</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- เนื้อหาจะแสดงจาก PHP ด้านบน -->
    </div>
</body>
</html>
