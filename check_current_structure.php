<?php
// ตรวจสอบโครงสร้างตาราง users ปัจจุบัน
require_once 'config/database.php';

echo "<h2>ตรวจสอบโครงสร้างตาราง users ปัจจุบัน</h2>";

try {
    // ตรวจสอบว่าตาราง users มีอยู่หรือไม่
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if ($stmt->rowCount() == 0) {
        echo "<p style='color: red;'>❌ ไม่พบตาราง users</p>";
        echo "<p>กรุณาสร้างตาราง users ก่อน หรือรัน setup.sql</p>";
        exit;
    }
    
    echo "<p style='color: green;'>✅ พบตาราง users</p>";
    
    // แสดงโครงสร้างตาราง
    echo "<h3>โครงสร้างตาราง users ปัจจุบัน:</h3>";
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 10px; text-align: left;'>Field</th>";
    echo "<th style='padding: 10px; text-align: left;'>Type</th>";
    echo "<th style='padding: 10px; text-align: left;'>Null</th>";
    echo "<th style='padding: 10px; text-align: left;'>Key</th>";
    echo "<th style='padding: 10px; text-align: left;'>Default</th>";
    echo "<th style='padding: 10px; text-align: left;'>Extra</th>";
    echo "</tr>";
    
    $currentFields = [];
    foreach ($columns as $column) {
        $currentFields[] = $column['Field'];
        echo "<tr>";
        echo "<td style='padding: 8px; font-weight: bold;'>{$column['Field']}</td>";
        echo "<td style='padding: 8px;'>{$column['Type']}</td>";
        echo "<td style='padding: 8px;'>{$column['Null']}</td>";
        echo "<td style='padding: 8px;'>{$column['Key']}</td>";
        echo "<td style='padding: 8px;'>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "<td style='padding: 8px;'>{$column['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // แสดงข้อมูลตัวอย่าง
    echo "<h3>ข้อมูลตัวอย่างในตาราง:</h3>";
    $stmt = $pdo->query("SELECT COUNT(*) FROM users");
    $userCount = $stmt->fetchColumn();
    
    if ($userCount > 0) {
        echo "<p>พบผู้ใช้ในระบบ: $userCount คน</p>";
        
        // แสดงผู้ใช้ 3 คนแรก
        $selectFields = array_intersect(['id', 'username', 'full_name', 'email', 'role', 'status', 'created_date'], $currentFields);
        $sql = "SELECT " . implode(', ', $selectFields) . " FROM users LIMIT 3";
        $stmt = $pdo->query($sql);
        $users = $stmt->fetchAll();
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        foreach ($selectFields as $field) {
            echo "<th style='padding: 8px;'>$field</th>";
        }
        echo "</tr>";
        
        foreach ($users as $user) {
            echo "<tr>";
            foreach ($selectFields as $field) {
                $value = $user[$field] ?? 'NULL';
                if ($field === 'password') {
                    $value = '***hidden***';
                }
                echo "<td style='padding: 8px;'>$value</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ ไม่มีข้อมูลผู้ใช้ในตาราง</p>";
    }
    
    // วิเคราะห์โครงสร้างและแนะนำการแก้ไข
    echo "<h3>วิเคราะห์โครงสร้างและแนะนำการแก้ไข:</h3>";
    
    // ตรวจสอบฟิลด์ที่จำเป็น
    $requiredFields = ['id', 'username', 'password'];
    $optionalFields = ['full_name', 'email', 'role', 'status', 'created_date', 'updated_date', 'last_login'];
    
    echo "<h4>ฟิลด์ที่จำเป็น:</h4>";
    echo "<ul>";
    foreach ($requiredFields as $field) {
        $exists = in_array($field, $currentFields);
        $icon = $exists ? '✅' : '❌';
        echo "<li>$icon <strong>$field</strong> - " . ($exists ? 'มี' : 'ไม่มี') . "</li>";
    }
    echo "</ul>";
    
    echo "<h4>ฟิลด์เสริม:</h4>";
    echo "<ul>";
    foreach ($optionalFields as $field) {
        $exists = in_array($field, $currentFields);
        $icon = $exists ? '✅' : '⚪';
        echo "<li>$icon <strong>$field</strong> - " . ($exists ? 'มี' : 'ไม่มี') . "</li>";
    }
    echo "</ul>";
    
    // สร้างโค้ดแก้ไขที่แนะนำ
    echo "<h3>โค้ดที่ต้องแก้ไข:</h3>";
    
    $hasPassword = in_array('password', $currentFields);
    $hasFullName = in_array('full_name', $currentFields);
    $hasEmail = in_array('email', $currentFields);
    $hasRole = in_array('role', $currentFields);
    $hasStatus = in_array('status', $currentFields);
    $hasCreatedDate = in_array('created_date', $currentFields);
    $hasUpdatedDate = in_array('updated_date', $currentFields);
    $hasLastLogin = in_array('last_login', $currentFields);
    
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h4>ไฟล์ที่ต้องแก้ไข:</h4>";
    echo "<ol>";
    echo "<li><strong>includes/auth.php</strong> - ปรับ SQL queries ตามฟิลด์ที่มี</li>";
    echo "<li><strong>register.php</strong> - ปรับฟอร์มตามฟิลด์ที่มี</li>";
    echo "<li><strong>users.php</strong> - ปรับตารางแสดงผู้ใช้</li>";
    echo "<li><strong>profile.php</strong> - ปรับการแสดงข้อมูลโปรไฟล์</li>";
    echo "<li><strong>add_user.php</strong> - ปรับฟอร์มเพิ่มผู้ใช้</li>";
    echo "</ol>";
    echo "</div>";
    
    // แสดงการตั้งค่าที่แนะนำ
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #c3e6cb;'>";
    echo "<h4 style='color: #155724;'>การตั้งค่าที่แนะนำ:</h4>";
    echo "<ul style='color: #155724;'>";
    
    if (!$hasPassword) {
        echo "<li>❌ <strong>เพิ่ม password field</strong> - จำเป็นสำหรับระบบ login</li>";
    }
    
    if (!$hasFullName) {
        echo "<li>⚪ เพิ่ม full_name field - สำหรับเก็บชื่อ-นามสกุล</li>";
    }
    
    if (!$hasEmail) {
        echo "<li>⚪ เพิ่ม email field - สำหรับติดต่อผู้ใช้</li>";
    }
    
    if (!$hasRole) {
        echo "<li>⚪ เพิ่ม role field - สำหรับแยกสิทธิ์ Admin/User</li>";
    }
    
    if (!$hasStatus) {
        echo "<li>⚪ เพิ่ม status field - สำหรับควบคุมการใช้งาน</li>";
    }
    
    if (!$hasCreatedDate) {
        echo "<li>⚪ เพิ่ม created_date field - สำหรับบันทึกวันที่สร้าง</li>";
    }
    
    if (!$hasLastLogin) {
        echo "<li>⚪ เพิ่ม last_login field - สำหรับติดตามการใช้งาน</li>";
    }
    
    echo "</ul>";
    echo "</div>";
    
    // สร้าง SQL สำหรับเพิ่มฟิลด์ที่ขาด
    $missingFields = [];
    if (!$hasPassword) $missingFields[] = "ADD COLUMN password VARCHAR(255) NOT NULL DEFAULT ''";
    if (!$hasFullName) $missingFields[] = "ADD COLUMN full_name VARCHAR(100) NOT NULL DEFAULT ''";
    if (!$hasEmail) $missingFields[] = "ADD COLUMN email VARCHAR(100) NULL";
    if (!$hasRole) $missingFields[] = "ADD COLUMN role ENUM('Admin', 'User') DEFAULT 'User'";
    if (!$hasStatus) $missingFields[] = "ADD COLUMN status ENUM('Active', 'Inactive') DEFAULT 'Active'";
    if (!$hasCreatedDate) $missingFields[] = "ADD COLUMN created_date DATETIME DEFAULT CURRENT_TIMESTAMP";
    if (!$hasUpdatedDate) $missingFields[] = "ADD COLUMN updated_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP";
    if (!$hasLastLogin) $missingFields[] = "ADD COLUMN last_login DATETIME NULL";
    
    if (!empty($missingFields)) {
        echo "<h4>SQL สำหรับเพิ่มฟิลด์ที่ขาด:</h4>";
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; margin: 10px 0;'>";
        echo "ALTER TABLE users<br>";
        echo implode(",<br>", $missingFields) . ";";
        echo "</div>";
    }
    
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='register.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>ทดสอบ Register</a>";
    echo "<a href='login.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>ทดสอบ Login</a>";
    echo "<a href='users.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>ดู Users</a>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<h3 style='color: red;'>❌ เกิดข้อผิดพลาด</h3>";
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<p>กรุณาตรวจสอบการเชื่อมต่อฐานข้อมูลใน config/database.php</p>";
}

echo "<p style='color: red; margin-top: 20px;'><strong>หมายเหตุ:</strong> กรุณาลบไฟล์ check_current_structure.php หลังจากตรวจสอบเสร็จแล้ว</p>";
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ตรวจสอบโครงสร้างตาราง users</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        table {
            font-size: 0.9rem;
        }
        th {
            background-color: #e9ecef !important;
        }
        code {
            background: #f8f9fa;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <!-- เนื้อหาจะแสดงจาก PHP ด้านบน -->
</body>
</html>
