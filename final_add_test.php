<?php
// ทดสอบการเพิ่ม Asset ขั้นสุดท้าย
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🎯 Final Add Asset Test</h2>";

// เชื่อมต่อฐานข้อมูล
try {
    $pdo = new PDO("mysql:host=localhost;dbname=asset_management;charset=utf8", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✅ Database connected</p>";
} catch (Exception $e) {
    die("<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>");
}

// ตรวจสอบโครงสร้างตาราง
echo "<h3>โครงสร้างตารางปัจจุบัน:</h3>";
try {
    $stmt = $pdo->query("DESCRIBE assets");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<p>คอลัมน์ที่มีอยู่: <strong>" . implode(', ', $columns) . "</strong></p>";
    
    // ตรวจสอบคอลัมน์ที่สำคัญ
    $requiredColumns = ['type', 'brand', 'status'];
    $optionalColumns = ['set_name', 'person_added', 'person_modified', 'date_added', 'date_modified'];
    
    echo "<h4>การตรวจสอบคอลัมน์:</h4>";
    echo "<ul>";
    foreach ($requiredColumns as $col) {
        $exists = in_array($col, $columns);
        $icon = $exists ? '✅' : '❌';
        echo "<li>$icon <strong>$col</strong> (จำเป็น): " . ($exists ? 'มี' : 'ไม่มี') . "</li>";
    }
    foreach ($optionalColumns as $col) {
        $exists = in_array($col, $columns);
        $icon = $exists ? '✅' : '⚠️';
        echo "<li>$icon <strong>$col</strong> (ไม่จำเป็น): " . ($exists ? 'มี' : 'ไม่มี') . "</li>";
    }
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error getting table structure: " . $e->getMessage() . "</p>";
}

// ทดสอบการเพิ่ม Asset
if ($_POST && isset($_POST['add_asset'])) {
    echo "<h3>🚀 เพิ่ม Asset...</h3>";
    
    $type = trim($_POST['type'] ?? '');
    $brand = trim($_POST['brand'] ?? '');
    $model = trim($_POST['model'] ?? '');
    $status = trim($_POST['status'] ?? 'ใช้งาน');
    
    if (empty($type)) {
        echo "<p style='color: red;'>❌ Type is required</p>";
    } else {
        try {
            // สร้าง SQL แบบ dynamic
            $fields = [];
            $values = [];
            $placeholders = [];
            
            // ข้อมูลที่ต้องการใส่
            $dataMapping = [
                'type' => $type,
                'brand' => $brand,
                'model' => $model,
                'status' => $status
            ];
            
            // เลือกเฉพาะฟิลด์ที่มีอยู่และมีค่า
            foreach ($dataMapping as $field => $value) {
                if (in_array($field, $columns) && !empty($value)) {
                    $fields[] = $field;
                    $values[] = $value;
                    $placeholders[] = '?';
                }
            }
            
            // เพิ่มฟิลด์วันที่ถ้ามี
            if (in_array('date_added', $columns)) {
                $fields[] = 'date_added';
                $values[] = date('Y-m-d H:i:s');
                $placeholders[] = '?';
            }
            
            if (in_array('date_modified', $columns)) {
                $fields[] = 'date_modified';
                $values[] = date('Y-m-d H:i:s');
                $placeholders[] = '?';
            }
            
            // เพิ่มฟิลด์ person ถ้ามี
            if (in_array('person_added', $columns)) {
                $fields[] = 'person_added';
                $values[] = 'admin';
                $placeholders[] = '?';
            }
            
            if (in_array('person_modified', $columns)) {
                $fields[] = 'person_modified';
                $values[] = 'admin';
                $placeholders[] = '?';
            }
            
            $sql = "INSERT INTO assets (" . implode(', ', $fields) . ") VALUES (" . implode(', ', $placeholders) . ")";
            
            echo "<p><strong>SQL:</strong> $sql</p>";
            echo "<p><strong>Values:</strong> " . implode(', ', array_map(function($v) { return "'$v'"; }, $values)) . "</p>";
            
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute($values);
            
            if ($result) {
                $newId = $pdo->lastInsertId();
                echo "<p style='color: green;'>✅ Asset เพิ่มสำเร็จ! ID: $newId</p>";
                
                // แสดงข้อมูลที่เพิ่ม
                $checkStmt = $pdo->prepare("SELECT * FROM assets WHERE id = ?");
                $checkStmt->execute([$newId]);
                $newAsset = $checkStmt->fetch();
                
                echo "<h4>ข้อมูล Asset ที่เพิ่ม:</h4>";
                echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
                echo "<tr style='background: #f8f9fa;'><th style='padding: 8px;'>Field</th><th style='padding: 8px;'>Value</th></tr>";
                foreach ($newAsset as $key => $value) {
                    if (!is_numeric($key)) {
                        echo "<tr><td style='padding: 8px;'><strong>$key</strong></td><td style='padding: 8px;'>$value</td></tr>";
                    }
                }
                echo "</table>";
                
                echo "<p style='color: blue;'>ℹ️ Asset ถูกเพิ่มในระบบแล้ว (ไม่ลบ)</p>";
                
            } else {
                echo "<p style='color: red;'>❌ ไม่สามารถเพิ่ม Asset ได้</p>";
            }
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
            echo "<p style='color: red;'>Error Code: " . $e->getCode() . "</p>";
        }
    }
}

?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Add Asset Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h2 {
            background: #28a745;
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        .form-section {
            background: white;
            padding: 25px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 16px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
        }
        button:hover {
            background: #0056b3;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
        }
        th, td {
            padding: 10px;
            text-align: left;
            border: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            color: #155724;
        }
    </style>
</head>
<body>

<div class="form-section">
    <h3>🎯 เพิ่ม Asset ใหม่</h3>
    <form method="POST">
        <div class="form-group">
            <label for="type">Type * (จำเป็น)</label>
            <select id="type" name="type" required>
                <option value="">เลือก Type</option>
                <option value="Desktop">Desktop</option>
                <option value="Laptop">Laptop</option>
                <option value="Monitor">Monitor</option>
                <option value="All-in-one">All-in-one</option>
                <option value="Multifunction Laser Printer">Multifunction Laser Printer</option>
                <option value="Barcode Printer">Barcode Printer</option>
                <option value="Barcode Scanner">Barcode Scanner</option>
                <option value="Tablet">Tablet</option>
                <option value="UPS">UPS</option>
                <option value="Queue">Queue</option>
                <option value="IP Phone">IP Phone</option>
                <option value="Teleconference">Teleconference</option>
                <option value="Switch">Switch</option>
                <option value="Access Point">Access Point</option>
                <option value="Peripheral">Peripheral</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="brand">Brand</label>
            <select id="brand" name="brand">
                <option value="">เลือก Brand</option>
                <option value="Dell">Dell</option>
                <option value="Lenovo">Lenovo</option>
                <option value="Microsoft">Microsoft</option>
                <option value="Apple">Apple</option>
                <option value="Zebra">Zebra</option>
                <option value="HP">HP</option>
                <option value="Philips">Philips</option>
                <option value="Acer">Acer</option>
                <option value="LG">LG</option>
                <option value="Cisco">Cisco</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="model">Model</label>
            <input type="text" id="model" name="model" placeholder="ระบุรุ่น">
        </div>
        
        <div class="form-group">
            <label for="status">Status</label>
            <select id="status" name="status">
                <option value="ใช้งาน">ใช้งาน</option>
                <option value="ชำรุด">ชำรุด</option>
                <option value="สำรอง">สำรอง</option>
            </select>
        </div>
        
        <div class="form-group">
            <input type="hidden" name="add_asset" value="1">
            <button type="submit">🎯 เพิ่ม Asset</button>
        </div>
    </form>
</div>

<div style="margin: 20px 0;">
    <a href="fix_table_columns.php" style="background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">🔧 แก้ไขคอลัมน์</a>
    <a href="add_asset.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">➕ หน้าเพิ่ม Asset</a>
    <a href="index.php" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">🏠 หน้าหลัก</a>
</div>

</body>
</html>
