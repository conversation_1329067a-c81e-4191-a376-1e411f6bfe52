<?php
// ทดสอบการเพิ่ม Asset แบบเร็ว
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>⚡ Quick Add Asset Test</h2>";

// เชื่อมต่อฐานข้อมูล
try {
    $pdo = new PDO("mysql:host=localhost;dbname=asset_management;charset=utf8", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✅ Database connected</p>";
} catch (Exception $e) {
    die("<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>");
}

// ตรวจสอบตาราง
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM assets");
    $count = $stmt->fetchColumn();
    echo "<p style='color: green;'>✅ Assets table OK ($count records)</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Table error: " . $e->getMessage() . "</p>";
}

// ทดสอบการเพิ่ม
if ($_POST && isset($_POST['quick_add'])) {
    echo "<h3>🚀 Adding Asset...</h3>";
    
    $type = $_POST['type'] ?? '';
    $brand = $_POST['brand'] ?? '';
    
    if (empty($type)) {
        echo "<p style='color: red;'>❌ Type is required</p>";
    } else {
        try {
            // ตรวจสอบโครงสร้างตาราง
            $stmt = $pdo->query("DESCRIBE assets");
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            echo "<p>Available columns: " . implode(', ', $columns) . "</p>";
            
            // เตรียมข้อมูล
            $data = [
                'type' => $type,
                'brand' => $brand,
                'status' => 'ใช้งาน'
            ];
            
            // สร้าง SQL แบบ dynamic
            $fields = [];
            $values = [];
            $placeholders = [];
            
            foreach ($data as $field => $value) {
                if (in_array($field, $columns) && !empty($value)) {
                    $fields[] = $field;
                    $values[] = $value;
                    $placeholders[] = '?';
                }
            }
            
            // เพิ่มฟิลด์วันที่
            if (in_array('date_added', $columns)) {
                $fields[] = 'date_added';
                $values[] = date('Y-m-d H:i:s');
                $placeholders[] = '?';
            }
            
            $sql = "INSERT INTO assets (" . implode(', ', $fields) . ") VALUES (" . implode(', ', $placeholders) . ")";
            
            echo "<p><strong>SQL:</strong> $sql</p>";
            echo "<p><strong>Values:</strong> " . implode(', ', $values) . "</p>";
            
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute($values);
            
            if ($result) {
                $newId = $pdo->lastInsertId();
                echo "<p style='color: green;'>✅ Asset added successfully! ID: $newId</p>";
                
                // แสดงข้อมูลที่เพิ่ม
                $checkStmt = $pdo->prepare("SELECT * FROM assets WHERE id = ?");
                $checkStmt->execute([$newId]);
                $newAsset = $checkStmt->fetch();
                
                echo "<h4>Added Asset:</h4>";
                echo "<table border='1' style='border-collapse: collapse;'>";
                foreach ($newAsset as $key => $value) {
                    if (!is_numeric($key)) {
                        echo "<tr><td><strong>$key</strong></td><td>$value</td></tr>";
                    }
                }
                echo "</table>";
                
            } else {
                echo "<p style='color: red;'>❌ Failed to add asset</p>";
            }
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
            echo "<p style='color: red;'>Error Code: " . $e->getCode() . "</p>";
        }
    }
}

?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Add Asset Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h2 {
            background: #ffc107;
            color: #212529;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        .form-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #28a745;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
        }
        button:hover {
            background: #218838;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>

<div class="form-section">
    <h3>⚡ Quick Add Asset</h3>
    <form method="POST">
        <div class="form-group">
            <label for="type">Type * (จำเป็น)</label>
            <select id="type" name="type" required>
                <option value="">เลือก Type</option>
                <option value="Desktop">Desktop</option>
                <option value="Laptop">Laptop</option>
                <option value="Monitor">Monitor</option>
                <option value="Printer">Printer</option>
                <option value="All-in-one">All-in-one</option>
                <option value="Tablet">Tablet</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="brand">Brand (ไม่จำเป็น)</label>
            <select id="brand" name="brand">
                <option value="">เลือก Brand</option>
                <option value="Dell">Dell</option>
                <option value="Lenovo">Lenovo</option>
                <option value="HP">HP</option>
                <option value="Apple">Apple</option>
                <option value="Microsoft">Microsoft</option>
                <option value="Acer">Acer</option>
                <option value="LG">LG</option>
            </select>
        </div>
        
        <div class="form-group">
            <input type="hidden" name="quick_add" value="1">
            <button type="submit">⚡ เพิ่ม Asset เร็ว</button>
        </div>
    </form>
</div>

<div style="margin: 20px 0;">
    <a href="check_table_structure.php" style="background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">🔍 ตรวจสอบตาราง</a>
    <a href="add_asset.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">➕ หน้าเพิ่ม Asset</a>
    <a href="index.php" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">🏠 หน้าหลัก</a>
</div>

</body>
</html>
