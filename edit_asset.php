<?php
require_once 'includes/functions.php';

$assetManager = new AssetManager($pdo);
$message = '';
$messageType = '';

// ตรวจสอบ ID
$id = $_GET['id'] ?? 0;
if (!$id) {
    header('Location: index.php');
    exit;
}

// ดึงข้อมูล asset
$asset = $assetManager->getAssetById($id);
if (!$asset) {
    header('Location: index.php');
    exit;
}

// ประมวลผลฟอร์ม
if ($_POST) {
    $data = [
        'type' => $_POST['type'] ?? '',
        'brand' => $_POST['brand'] ?? '',
        'model' => $_POST['model'] ?? '',
        'tag' => $_POST['tag'] ?? '',
        'department' => $_POST['department'] ?? '',
        'status' => $_POST['status'] ?? 'Active',
        'hostname' => $_POST['hostname'] ?? '',
        'operating_system' => $_POST['operating_system'] ?? '',
        'serial_number' => $_POST['serial_number'] ?? '',
        'asset_id' => $_POST['asset_id'] ?? '',
        'warranty_expire' => $_POST['warranty_expire'] ?? null,
        'description' => $_POST['description'] ?? '',
        'asset_set' => $_POST['asset_set'] ?? '',
        'updated_by' => $_POST['updated_by'] ?? 'admin'
    ];

    // ตรวจสอบข้อมูลที่จำเป็น
    if (empty($data['type'])) {
        $message = 'กรุณากรอกข้อมูล Type';
        $messageType = 'danger';
    } else {
        try {
            $result = $assetManager->updateAsset($id, $data);
            if ($result) {
                $message = 'อัพเดท Asset สำเร็จ';
                $messageType = 'success';
                // Refresh asset data
                $asset = $assetManager->getAssetById($id);
            } else {
                $message = 'เกิดข้อผิดพลาดในการอัพเดท Asset';
                $messageType = 'danger';
            }
        } catch (Exception $e) {
            $message = 'เกิดข้อผิดพลาด: ' . $e->getMessage();
            $messageType = 'danger';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>แก้ไข Asset - Asset Management System</title>
    <link rel="stylesheet" href="assets/style.css">
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>ระบบจัดการ Asset</h1>
        </div>
    </div>

    <div class="container">
        <nav class="nav">
            <ul>
                <li><a href="index.php">รายการ Assets</a></li>
                <li><a href="add_asset.php">เพิ่ม Asset</a></li>
                <li><a href="view_asset.php?id=<?= $id ?>">ดู Asset</a></li>
            </ul>
        </nav>

        <?php if ($message): ?>
            <div class="alert alert-<?= $messageType ?>">
                <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>

        <div class="card">
            <div class="card-header">
                <h2>แก้ไข Asset: <?= htmlspecialchars($asset['asset_id']) ?></h2>
            </div>
            <div class="card-body">
                <form method="POST" action="">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="type">Type *</label>
                            <select id="type" name="type" class="form-control" required>
                                <option value="">เลือก Type</option>
                                <option value="Desktop" <?= $asset['type'] === 'Desktop' ? 'selected' : '' ?>>Desktop</option>
                                <option value="Laptop" <?= $asset['type'] === 'Laptop' ? 'selected' : '' ?>>Laptop</option>
                                <option value="Monitor" <?= $asset['type'] === 'Monitor' ? 'selected' : '' ?>>Monitor</option>
                                <option value="All-in-one" <?= $asset['type'] === 'All-in-one' ? 'selected' : '' ?>>All-in-one</option>
                                <option value="Multifunction Laser Printer" <?= $asset['type'] === 'Multifunction Laser Printer' ? 'selected' : '' ?>>Multifunction Laser Printer</option>
                                <option value="Barcode Printer" <?= $asset['type'] === 'Barcode Printer' ? 'selected' : '' ?>>Barcode Printer</option>
                                <option value="Barcode Scanner" <?= $asset['type'] === 'Barcode Scanner' ? 'selected' : '' ?>>Barcode Scanner</option>
                                <option value="Tablet" <?= $asset['type'] === 'Tablet' ? 'selected' : '' ?>>Tablet</option>
                                <option value="UPS" <?= $asset['type'] === 'UPS' ? 'selected' : '' ?>>UPS</option>
                                <option value="Queue" <?= $asset['type'] === 'Queue' ? 'selected' : '' ?>>Queue</option>
                                <option value="IP Phone" <?= $asset['type'] === 'IP Phone' ? 'selected' : '' ?>>IP Phone</option>
                                <option value="Teleconference" <?= $asset['type'] === 'Teleconference' ? 'selected' : '' ?>>Teleconference</option>
                                <option value="Switch" <?= $asset['type'] === 'Switch' ? 'selected' : '' ?>>Switch</option>
                                <option value="Access Point" <?= $asset['type'] === 'Access Point' ? 'selected' : '' ?>>Access Point</option>
                                <option value="Peripheral" <?= $asset['type'] === 'Peripheral' ? 'selected' : '' ?>>Peripheral</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="brand">Brand</label>
                            <select id="brand" name="brand" class="form-control">
                                <option value="">เลือก Brand</option>
                                <option value="-" <?= $asset['brand'] === '-' ? 'selected' : '' ?>>-</option>
                                <option value="Dell" <?= $asset['brand'] === 'Dell' ? 'selected' : '' ?>>Dell</option>
                                <option value="Lenovo" <?= $asset['brand'] === 'Lenovo' ? 'selected' : '' ?>>Lenovo</option>
                                <option value="Microsoft" <?= $asset['brand'] === 'Microsoft' ? 'selected' : '' ?>>Microsoft</option>
                                <option value="Apple" <?= $asset['brand'] === 'Apple' ? 'selected' : '' ?>>Apple</option>
                                <option value="Zebra" <?= $asset['brand'] === 'Zebra' ? 'selected' : '' ?>>Zebra</option>
                                <option value="HP" <?= $asset['brand'] === 'HP' ? 'selected' : '' ?>>HP</option>
                                <option value="Philips" <?= $asset['brand'] === 'Philips' ? 'selected' : '' ?>>Philips</option>
                                <option value="Acer" <?= $asset['brand'] === 'Acer' ? 'selected' : '' ?>>Acer</option>
                                <option value="LG" <?= $asset['brand'] === 'LG' ? 'selected' : '' ?>>LG</option>
                                <option value="Cisco" <?= $asset['brand'] === 'Cisco' ? 'selected' : '' ?>>Cisco</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="model">Model</label>
                            <input type="text" id="model" name="model" class="form-control"
                                   value="<?= htmlspecialchars($asset['model']) ?>">
                        </div>
                        <div class="form-group">
                            <label for="tag">Tag</label>
                            <input type="text" id="tag" name="tag" class="form-control"
                                   value="<?= htmlspecialchars($asset['tag']) ?>">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="department">Department</label>
                            <input type="text" id="department" name="department" class="form-control"
                                   value="<?= htmlspecialchars($asset['department']) ?>">
                        </div>
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select id="status" name="status" class="form-control">
                                <option value="ใช้งาน" <?= $asset['status'] === 'ใช้งาน' ? 'selected' : '' ?>>ใช้งาน</option>
                                <option value="ชำรุด" <?= $asset['status'] === 'ชำรุด' ? 'selected' : '' ?>>ชำรุด</option>
                                <option value="สำรอง" <?= $asset['status'] === 'สำรอง' ? 'selected' : '' ?>>สำรอง</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="hostname">Hostname</label>
                            <input type="text" id="hostname" name="hostname" class="form-control"
                                   value="<?= htmlspecialchars($asset['hostname']) ?>">
                        </div>
                        <div class="form-group">
                            <label for="operating_system">Operating System</label>
                            <select id="operating_system" name="operating_system" class="form-control">
                                <option value="">เลือก Operating System</option>
                                <option value="-" <?= $asset['operating_system'] === '-' ? 'selected' : '' ?>>-</option>
                                <option value="Windows 7" <?= $asset['operating_system'] === 'Windows 7' ? 'selected' : '' ?>>Windows 7</option>
                                <option value="Windows 10" <?= $asset['operating_system'] === 'Windows 10' ? 'selected' : '' ?>>Windows 10</option>
                                <option value="Windows 11" <?= $asset['operating_system'] === 'Windows 11' ? 'selected' : '' ?>>Windows 11</option>
                                <option value="MacOS" <?= $asset['operating_system'] === 'MacOS' ? 'selected' : '' ?>>MacOS</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="serial_number">Serial Number</label>
                            <input type="text" id="serial_number" name="serial_number" class="form-control"
                                   value="<?= htmlspecialchars($asset['serial_number']) ?>">
                        </div>
                        <div class="form-group">
                            <label for="asset_id">Asset ID</label>
                            <input type="text" id="asset_id" name="asset_id" class="form-control"
                                   value="<?= htmlspecialchars($asset['asset_id']) ?>">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="warranty_expire">Warranty Expire</label>
                            <input type="date" id="warranty_expire" name="warranty_expire" class="form-control"
                                   value="<?= $asset['warranty_expire'] ?>">
                        </div>
                        <div class="form-group">
                            <label for="asset_set">Set</label>
                            <input type="text" id="asset_set" name="asset_set" class="form-control"
                                   value="<?= htmlspecialchars($asset['asset_set']) ?>">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea id="description" name="description" class="form-control" rows="3"><?= htmlspecialchars($asset['description']) ?></textarea>
                    </div>

                    <div class="form-group">
                        <label for="updated_by">คนแก้ไข</label>
                        <input type="text" id="updated_by" name="updated_by" class="form-control" required
                               value="admin">
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-success">บันทึกการแก้ไข</button>
                        <a href="view_asset.php?id=<?= $id ?>" class="btn btn-primary">ดู Asset</a>
                        <a href="index.php" class="btn btn-secondary">ยกเลิก</a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Asset Info -->
        <div class="card">
            <div class="card-header">
                <h2>ข้อมูลเพิ่มเติม</h2>
            </div>
            <div class="card-body">
                <div class="form-row">
                    <div class="form-group">
                        <label>วันที่เพิ่ม:</label>
                        <p><?= formatDateTime($asset['created_date']) ?></p>
                    </div>
                    <div class="form-group">
                        <label>คนเพิ่ม:</label>
                        <p><?= htmlspecialchars($asset['created_by']) ?></p>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>วันที่แก้ไขล่าสุด:</label>
                        <p><?= formatDateTime($asset['updated_date']) ?></p>
                    </div>
                    <div class="form-group">
                        <label>คนแก้ไขล่าสุด:</label>
                        <p><?= htmlspecialchars($asset['updated_by']) ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
