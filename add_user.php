<?php
require_once 'includes/auth.php';
require_once 'includes/functions.php';

// ตรวจสอบสิทธิ์ Admin
requireAdmin();

$message = '';
$messageType = '';

// ประมวลผลการเพิ่มผู้ใช้
if ($_POST) {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    $fullName = trim($_POST['full_name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $role = $_POST['role'] ?? 'User';
    $status = $_POST['status'] ?? 'Active';
    
    // ตรวจสอบข้อมูล
    if (empty($username) || empty($password) || empty($confirmPassword) || empty($fullName)) {
        $message = 'กรุณากรอกข้อมูลที่จำเป็นให้ครบถ้วน';
        $messageType = 'danger';
    } elseif (strlen($username) < 3) {
        $message = 'Username ต้องมีอย่างน้อย 3 ตัวอักษร';
        $messageType = 'danger';
    } elseif (strlen($password) < 6) {
        $message = 'รหัสผ่านต้องมีอย่างน้อย 6 ตัวอักษร';
        $messageType = 'danger';
    } elseif ($password !== $confirmPassword) {
        $message = 'รหัสผ่านไม่ตรงกัน';
        $messageType = 'danger';
    } elseif (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $message = 'รูปแบบ Email ไม่ถูกต้อง';
        $messageType = 'danger';
    } else {
        // ตรวจสอบว่า username ซ้ำหรือไม่
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE username = ?");
        $stmt->execute([$username]);
        
        if ($stmt->fetchColumn() > 0) {
            $message = 'Username นี้มีผู้ใช้แล้ว กรุณาเลือก Username อื่น';
            $messageType = 'danger';
        } else {
            // สร้างผู้ใช้ใหม่
            $userData = [
                'username' => $username,
                'password' => $password,
                'full_name' => $fullName,
                'email' => $email,
                'role' => $role,
                'status' => $status
            ];
            
            if ($auth->createUser($userData)) {
                header('Location: users.php?message=' . urlencode('เพิ่มผู้ใช้สำเร็จ') . '&type=success');
                exit;
            } else {
                $message = 'เกิดข้อผิดพลาดในการเพิ่มผู้ใช้ กรุณาลองใหม่';
                $messageType = 'danger';
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>เพิ่มผู้ใช้ - Asset Management System</title>
    <link rel="stylesheet" href="assets/style.css">
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>ระบบจัดการ Asset</h1>
            <p class="subtitle">Asset Management System - จัดการทรัพย์สินองค์กรอย่างมีประสิทธิภาพ</p>
        </div>
    </div>

    <div class="container">
        <nav class="nav">
            <ul>
                <li><a href="index.php">รายการ Assets</a></li>
                <li><a href="users.php">จัดการผู้ใช้</a></li>
                <li><a href="profile.php">โปรไฟล์</a></li>
                <li><a href="logout.php">ออกจากระบบ</a></li>
            </ul>
        </nav>

        <!-- User Info Bar -->
        <div class="user-info-bar">
            <div class="container">
                <span class="welcome-text">ยินดีต้อนรับ, <strong><?= getCurrentUserFullName() ?></strong></span>
                <span class="role-badge badge badge-success"><?= getCurrentUserRole() ?></span>
            </div>
        </div>

        <?php if ($message): ?>
            <div class="alert alert-<?= $messageType ?>">
                <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>

        <!-- Breadcrumb -->
        <div class="breadcrumb">
            <a href="users.php">จัดการผู้ใช้</a> > เพิ่มผู้ใช้ใหม่
        </div>

        <div class="card">
            <div class="card-header">
                <h2>➕ เพิ่มผู้ใช้ใหม่</h2>
            </div>
            <div class="card-body">
                <form method="POST" action="">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="username">Username *</label>
                            <input type="text" id="username" name="username" class="form-control" 
                                   placeholder="กรอก Username (อย่างน้อย 3 ตัวอักษร)" required
                                   value="<?= htmlspecialchars($_POST['username'] ?? '') ?>" minlength="3">
                        </div>
                        
                        <div class="form-group">
                            <label for="full_name">ชื่อ-นามสกุล *</label>
                            <input type="text" id="full_name" name="full_name" class="form-control" 
                                   placeholder="กรอกชื่อ-นามสกุล" required
                                   value="<?= htmlspecialchars($_POST['full_name'] ?? '') ?>">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="email">Email</label>
                            <input type="email" id="email" name="email" class="form-control" 
                                   placeholder="กรอก Email"
                                   value="<?= htmlspecialchars($_POST['email'] ?? '') ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="role">Role *</label>
                            <select id="role" name="role" class="form-control" required>
                                <option value="User" <?= ($_POST['role'] ?? '') === 'User' ? 'selected' : '' ?>>User</option>
                                <option value="Admin" <?= ($_POST['role'] ?? '') === 'Admin' ? 'selected' : '' ?>>Admin</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="password">รหัสผ่าน *</label>
                            <input type="password" id="password" name="password" class="form-control" 
                                   placeholder="กรอกรหัสผ่าน (อย่างน้อย 6 ตัวอักษร)" required minlength="6">
                        </div>
                        
                        <div class="form-group">
                            <label for="confirm_password">ยืนยันรหัสผ่าน *</label>
                            <input type="password" id="confirm_password" name="confirm_password" class="form-control" 
                                   placeholder="ยืนยันรหัสผ่าน" required minlength="6">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="status">Status *</label>
                            <select id="status" name="status" class="form-control" required>
                                <option value="Active" <?= ($_POST['status'] ?? 'Active') === 'Active' ? 'selected' : '' ?>>Active</option>
                                <option value="Inactive" <?= ($_POST['status'] ?? '') === 'Inactive' ? 'selected' : '' ?>>Inactive</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <!-- Empty for spacing -->
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <a href="users.php" class="btn btn-secondary">ยกเลิก</a>
                        <button type="submit" class="btn btn-success">💾 เพิ่มผู้ใช้</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // ตรวจสอบรหัสผ่านตรงกัน
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;
            
            if (password !== confirmPassword) {
                this.setCustomValidity('รหัสผ่านไม่ตรงกัน');
            } else {
                this.setCustomValidity('');
            }
        });
        
        document.getElementById('password').addEventListener('input', function() {
            const confirmPassword = document.getElementById('confirm_password');
            if (confirmPassword.value) {
                confirmPassword.dispatchEvent(new Event('input'));
            }
        });
        
        // ตรวจสอบ Username
        document.getElementById('username').addEventListener('input', function() {
            const username = this.value;
            if (username.length > 0 && username.length < 3) {
                this.setCustomValidity('Username ต้องมีอย่างน้อย 3 ตัวอักษร');
            } else {
                this.setCustomValidity('');
            }
        });
        
        // Auto focus on username field
        document.getElementById('username').focus();
    </script>
</body>
</html>
