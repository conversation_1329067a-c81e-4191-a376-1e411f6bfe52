/* Asset Management System Styles */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Sarabun:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Fix button interaction issues */
button, .btn, a, input, select, textarea {
    pointer-events: auto !important;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

/* Ensure clickable elements work */
button:not(:disabled), .btn:not(:disabled), a:not([disabled]) {
    cursor: pointer !important;
    pointer-events: auto !important;
}

body {
    font-family: 'Sarabun', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background-color: #f8f9fa;
    color: #2d3748;
    line-height: 1.6;
    font-weight: 400;
    font-size: 16px;
    /* Optimized for 1920x1080 resolution */
    min-width: 1920px;
    overflow-x: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

.container {
    max-width: 1800px; /* Optimized for 1920x1080 */
    margin: 0 auto;
    padding: 20px 60px; /* Increased horizontal padding */
    width: 100%;
}

/* Header */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 0;
    margin-bottom: 0;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.header .container {
    position: relative;
    z-index: 1;
}

.header h1 {
    text-align: center;
    font-size: 2.2rem;
    font-weight: 600;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    letter-spacing: 0;
}

.header .subtitle {
    text-align: center;
    font-size: 1rem;
    margin-top: 5px;
    opacity: 0.85;
    font-weight: 400;
    letter-spacing: 0;
}

/* Navigation */
.nav {
    background: white;
    padding: 0;
    margin-bottom: 30px;
    border-radius: 0 0 15px 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    position: relative;
    top: -1px;
}

.nav ul {
    list-style: none;
    display: flex;
    justify-content: center;
    gap: 0;
    margin: 0;
    padding: 0;
}

.nav li {
    flex: 1;
    max-width: 200px;
}

.nav a {
    display: block;
    text-decoration: none;
    color: #667eea;
    font-weight: 600;
    padding: 15px 25px;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    border-radius: 0;
    font-size: 0.9rem;
    letter-spacing: 0.01em;
}

.nav a:first-child {
    border-radius: 0 0 0 15px;
}

.nav a:last-child {
    border-radius: 0 0 15px 0;
}

.nav a::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 3px;
    background: #667eea;
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav a:hover::before, .nav a.active::before {
    width: 80%;
}

.nav a:hover, .nav a.active {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    color: #667eea;
    transform: translateY(-2px);
}

.nav a.active {
    font-weight: 700;
    color: #5a6fd8;
}

/* User Info Bar */
.user-info-bar {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 10px 0;
    margin-bottom: 20px;
}

.user-info-bar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.welcome-text {
    color: #495057;
    font-size: 0.9rem;
    font-weight: 500;
}

.role-badge {
    font-size: 0.75rem;
    padding: 4px 8px;
}

/* Cards */
.card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    overflow: hidden;
}

.card-header {
    background: #f8f9fa;
    padding: 20px;
    border-bottom: 1px solid #dee2e6;
}

.card-header h2 {
    color: #2d3748;
    font-size: 1.5rem;
    font-weight: 600;
    letter-spacing: -0.01em;
}

.card-body {
    padding: 20px;
}

/* Forms */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #4a5568;
    font-size: 0.9rem;
    letter-spacing: 0.01em;
}

.form-control {
    width: 100%;
    padding: 12px 14px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.9rem;
    font-family: inherit;
    font-weight: 400;
    transition: all 0.3s ease;
    background-color: #ffffff;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 600;
    font-family: inherit;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    letter-spacing: 0.01em;
    pointer-events: auto;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #218838;
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background: #e0a800;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-sm {
    padding: 8px 16px;
    font-size: 12px;
}

/* Tables */
.table-responsive {
    overflow-x: auto;
    margin-bottom: 20px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background: white;
    border-radius: 15px;
    overflow: hidden;
    margin: 0;
    table-layout: fixed;
}

.table th,
.table td {
    padding: 12px 10px;
    text-align: left;
    border-bottom: 1px solid #f1f3f4;
    vertical-align: middle;
    font-size: 0.8rem;
    line-height: 1.3;
    white-space: nowrap;
}

.table th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    font-weight: 600;
    color: #4a5568;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: sticky;
    top: 0;
    z-index: 10;
    font-family: 'Sarabun', sans-serif;
    padding: 10px 8px;
    position: relative;
    resize: horizontal;
    overflow: hidden;
}

/* Resizable column handle */
.table th::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 5px;
    height: 100%;
    background: transparent;
    cursor: col-resize;
    z-index: 20;
}

.table th:hover::after {
    background: rgba(0, 123, 255, 0.3);
}

.table th:last-child::after {
    display: none;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.table tbody tr:nth-child(even) {
    background: rgba(248, 249, 250, 0.5);
}

.table tbody tr:nth-child(even):hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
}

/* Table action column */
.table td:last-child {
    width: 100px;
    text-align: center;
    padding: 8px 6px;
}

.table th:last-child {
    width: 100px;
    text-align: center;
    padding: 8px 6px;
}

/* Specific column widths for better layout */
.table td:nth-child(1), .table th:nth-child(1) { width: 90px; }  /* Asset ID */
.table td:nth-child(2), .table th:nth-child(2) { width: 80px; }  /* Type */
.table td:nth-child(3), .table th:nth-child(3) { width: 65px; }  /* Brand */
.table td:nth-child(4), .table th:nth-child(4) { width: 85px; }  /* Model */
.table td:nth-child(5), .table th:nth-child(5) { width: 75px; }  /* Tag */
.table td:nth-child(6), .table th:nth-child(6) { width: 100px; } /* Department */
.table td:nth-child(7), .table th:nth-child(7) { width: 65px; }  /* Status */
.table td:nth-child(8), .table th:nth-child(8) { width: 100px; } /* Serial Number */
.table td:nth-child(9), .table th:nth-child(9) { width: 90px; }  /* Hostname */
.table td:nth-child(10), .table th:nth-child(10) { width: 70px; } /* Date */
.table td:nth-child(11), .table th:nth-child(11) { width: 90px; } /* Actions */

/* Date column specific styling */
.table td:nth-child(10) {
    font-size: 0.68rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 70px;
    padding: 6px 2px;
    text-align: center;
}

.table th:nth-child(10) {
    font-size: 0.7rem;
    text-align: center;
}

/* Badges */
.badge {
    display: inline-block;
    padding: 4px 8px;
    font-size: 0.7rem;
    font-weight: 600;
    border-radius: 12px;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    line-height: 1;
}

.badge-success {
    background: #d4edda;
    color: #155724;
}

.badge-warning {
    background: #fff3cd;
    color: #856404;
}

.badge-danger {
    background: #f8d7da;
    color: #721c24;
}

.badge-secondary {
    background: #e2e3e5;
    color: #383d41;
}

/* Search and Filter */
.search-filter {
    background: white;
    padding: 25px;
    border-radius: 15px;
    margin-bottom: 25px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.search-filter h3 {
    margin: 0 0 20px 0;
    color: #2d3748;
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
    letter-spacing: -0.01em;
}

.search-filter h3::before {
    content: '🔍';
    font-size: 1.1rem;
}

.search-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1fr auto;
    gap: 12px;
    align-items: end;
}

.search-actions {
    display: flex;
    gap: 10px;
    align-items: end;
}

/* Alerts */
.alert {
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-danger {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Responsive */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .header {
        padding: 10px 0;
    }

    .header h1 {
        font-size: 1.7rem;
    }

    .header .subtitle {
        font-size: 0.9rem;
        margin-top: 3px;
    }

    .nav a {
        padding: 12px 20px;
        font-size: 0.85rem;
    }

    .form-row,
    .search-row {
        grid-template-columns: 1fr;
    }

    .nav ul {
        flex-direction: column;
        gap: 10px;
    }

    .table {
        font-size: 0.7rem;
    }

    .table th,
    .table td {
        padding: 6px 3px;
        white-space: nowrap;
    }

    .table th {
        font-size: 0.65rem;
        padding: 6px 3px;
    }

    .action-buttons {
        gap: 1px;
    }

    .action-buttons .btn {
        width: 24px;
        height: 24px;
        font-size: 0.6rem;
        padding: 2px;
    }

    .table td:last-child {
        width: 75px;
        padding: 6px 2px;
    }

    .table th:last-child {
        width: 75px;
        padding: 6px 2px;
    }

    .asset-id, .serial-number {
        font-size: 0.65rem;
        padding: 1px 3px;
    }

    .badge {
        font-size: 0.6rem;
        padding: 2px 6px;
    }

    /* Stats grid for mobile */
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
        margin-bottom: 20px;
    }

    .stat-card {
        padding: 10px;
    }

    .stat-number {
        font-size: 1.6rem;
        margin: 3px 0;
    }

    .stat-label {
        font-size: 0.8rem;
    }

    .stat-icon {
        font-size: 1rem;
        margin-bottom: 3px;
    }

    /* Adjust column widths for mobile */
    .table td:nth-child(1), .table th:nth-child(1) { width: 60px; }  /* Asset ID */
    .table td:nth-child(2), .table th:nth-child(2) { width: 55px; }  /* Type */
    .table td:nth-child(3), .table th:nth-child(3) { width: 45px; }  /* Brand */
    .table td:nth-child(4), .table th:nth-child(4) { width: 55px; }  /* Model */
    .table td:nth-child(5), .table th:nth-child(5) { width: 50px; }  /* Tag */
    .table td:nth-child(6), .table th:nth-child(6) { width: 65px; }  /* Department */
    .table td:nth-child(7), .table th:nth-child(7) { width: 45px; }  /* Status */
    .table td:nth-child(8), .table th:nth-child(8) { width: 65px; }  /* Serial Number */
    .table td:nth-child(9), .table th:nth-child(9) { width: 60px; }  /* Hostname */
    .table td:nth-child(10), .table th:nth-child(10) { width: 50px; } /* Date */
    .table td:nth-child(11), .table th:nth-child(11) { width: 60px; } /* Actions */

    /* Date column mobile styling */
    .table td:nth-child(10) {
        font-size: 0.5rem;
        padding: 3px 1px;
        max-width: 50px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        text-align: center;
    }

    .table th:nth-child(10) {
        font-size: 0.6rem;
        padding: 4px 2px;
        text-align: center;
    }

    /* Pagination responsive */
    .pagination-container {
        flex-direction: column;
        gap: 15px;
        padding: 15px;
    }

    .pagination-info {
        text-align: center;
        font-size: 0.8rem;
    }

    .pagination {
        justify-content: center;
        flex-wrap: wrap;
        gap: 3px;
    }

    .pagination-btn {
        min-width: 35px;
        height: 35px;
        padding: 6px 8px;
        font-size: 0.8rem;
    }

    /* Search form responsive */
    .search-row {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .search-row .form-group {
        margin-bottom: 10px;
    }

    .search-actions {
        justify-content: center;
        margin-top: 15px;
    }
}

/* Loading */
.loading {
    text-align: center;
    padding: 40px;
    color: #6c757d;
}

/* Empty state */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-state h3 {
    margin-bottom: 10px;
    color: #495057;
}

/* Log entries */
.log-entry {
    background: #f8f9fa;
    border-left: 4px solid #667eea;
    padding: 15px;
    margin-bottom: 10px;
    border-radius: 0 6px 6px 0;
}

.log-entry.create {
    border-left-color: #28a745;
}

.log-entry.update {
    border-left-color: #ffc107;
}

.log-entry.delete {
    border-left-color: #dc3545;
}

.log-meta {
    font-size: 12px;
    color: #6c757d;
    margin-top: 5px;
}

/* Dashboard Stats */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 15px;
    margin-bottom: 25px;
}

.stat-card {
    background: white;
    padding: 15px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    text-align: center;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.15);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card.success::before {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.stat-card.warning::before {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
}

.stat-card.danger::before {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
}

.stat-card.info::before {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
    margin: 5px 0;
    display: block;
    font-family: 'Sarabun', sans-serif;
    letter-spacing: 0;
}

.stat-label {
    font-size: 0.9rem;
    color: #718096;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0;
    font-family: 'Sarabun', sans-serif;
}

.stat-icon {
    font-size: 1.3rem;
    margin-bottom: 5px;
    opacity: 0.7;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 4px;
    justify-content: center;
    align-items: center;
    white-space: nowrap;
}

.action-buttons .btn {
    min-width: 32px;
    width: 32px;
    height: 32px;
    padding: 6px;
    font-size: 0.75rem;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.2s ease;
    pointer-events: auto;
    cursor: pointer;
    border: none;
    background: transparent;
}

.action-buttons .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* Quick Actions */
.quick-actions {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    margin-bottom: 25px;
    text-align: center;
}

.quick-actions h3 {
    margin: 0 0 20px 0;
    color: #2d3748;
    font-size: 1.1rem;
    font-weight: 600;
    letter-spacing: -0.01em;
}

.quick-actions .btn {
    margin: 5px;
    min-width: 150px;
}

/* Pagination */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: white;
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 15px 15px;
}

.pagination-info {
    color: #6c757d;
    font-size: 0.9rem;
}

.pagination {
    display: flex;
    gap: 5px;
    align-items: center;
}

.pagination-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    height: 40px;
    padding: 8px 12px;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    color: #495057;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.pagination-btn:hover {
    background: #f8f9fa;
    border-color: #adb5bd;
    color: #495057;
    transform: translateY(-1px);
}

.pagination-btn.active {
    background: #007bff;
    border-color: #007bff;
    color: white;
    font-weight: 600;
}

.pagination-btn.active:hover {
    background: #0056b3;
    border-color: #0056b3;
    transform: translateY(-1px);
}

/* Enhanced Empty State */
.empty-state {
    text-align: center;
    padding: 80px 20px;
    color: #6c757d;
}

.empty-state .icon {
    font-size: 4rem;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-state h3 {
    margin-bottom: 15px;
    color: #495057;
    font-size: 1.5rem;
}

.empty-state p {
    font-size: 1.1rem;
    margin-bottom: 25px;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
    font-weight: 400;
}

/* Code and Monospace */
code, pre, .code {
    font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', 'Courier New', monospace;
    font-weight: 500;
    font-size: 0.85rem;
    background-color: #f7fafc;
    padding: 2px 6px;
    border-radius: 4px;
    border: 1px solid #e2e8f0;
}

pre {
    padding: 12px 16px;
    background-color: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    overflow-x: auto;
    line-height: 1.5;
}

/* Asset ID and Serial Number styling */
.asset-id, .serial-number {
    font-family: 'JetBrains Mono', monospace;
    font-weight: 500;
    font-size: 0.75rem;
    background-color: #f7fafc;
    padding: 2px 4px;
    border-radius: 3px;
    border: 1px solid #e2e8f0;
    display: inline-block;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    pointer-events: auto;
}

/* Profile Modal Specific Styles */
.profile-modal {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.profile-modal .modal-header {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 25px 30px;
    color: white;
}

.profile-modal .modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.profile-modal .modal-header .close {
    color: white;
    font-size: 2rem;
    opacity: 0.8;
    transition: all 0.3s ease;
}

.profile-modal .modal-header .close:hover {
    opacity: 1;
    transform: scale(1.1);
}

.profile-modal .modal-body {
    background: white;
    padding: 0;
}

.profile-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40px 30px 30px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    position: relative;
}

.profile-info::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    z-index: 1;
}

.profile-avatar {
    position: relative;
    z-index: 2;
    margin-bottom: 25px;
}

.avatar-circle {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: white;
    margin: 0 auto;
    box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
    border: 5px solid white;
    position: relative;
    overflow: hidden;
}

.avatar-circle::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.profile-user-info {
    text-align: center;
    position: relative;
    z-index: 2;
    margin-bottom: 30px;
}

.profile-username {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 5px;
}

.profile-role-display {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 6px 12px;
    border-radius: 18px;
    font-size: 0.8rem;
    font-weight: 600;
    box-shadow: 0 3px 12px rgba(102, 126, 234, 0.3);
    min-width: fit-content;
    white-space: nowrap;
    line-height: 1.2;
}

.profile-details {
    width: 100%;
    max-width: 450px;
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 2;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #f1f5f9;
    transition: all 0.3s ease;
}

.detail-row:hover {
    background: rgba(102, 126, 234, 0.02);
    margin: 0 -15px;
    padding-left: 15px;
    padding-right: 15px;
    border-radius: 8px;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-row label {
    font-weight: 600;
    color: #475569;
    margin: 0;
    min-width: 140px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.95rem;
}

.detail-row label::before {
    content: '';
    width: 4px;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
}

.detail-row span {
    color: #1e293b;
    text-align: right;
    flex: 1;
    font-weight: 500;
    font-size: 0.95rem;
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.role-badge {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    padding: 2px 6px;
    border-radius: 6px;
    font-size: 0.7rem;
    font-weight: 600;
    box-shadow: 0 1px 3px rgba(16, 185, 129, 0.2);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 2px;
    margin-left: auto;
    white-space: nowrap;
    min-width: fit-content;
    line-height: 1;
    text-align: center;
    height: fit-content;
}

.role-badge.admin {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    box-shadow: 0 1px 3px rgba(245, 158, 11, 0.2);
}

.status-badge {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    padding: 2px 6px;
    border-radius: 6px;
    font-size: 0.7rem;
    font-weight: 600;
    box-shadow: 0 1px 3px rgba(16, 185, 129, 0.2);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 2px;
    margin-left: auto;
    white-space: nowrap;
    min-width: fit-content;
    line-height: 1;
    text-align: center;
    height: fit-content;
}

.status-badge.inactive {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    box-shadow: 0 1px 3px rgba(239, 68, 68, 0.2);
}

/* Badge container for proper alignment */
.detail-row .badge-container {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    width: 100%;
}

.form-text {
    font-size: 0.875rem;
    color: #64748b;
    margin-top: 8px;
    display: block;
    font-style: italic;
}

/* Enhanced Form Styles */
.profile-edit-form {
    padding: 30px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%);
}

.profile-edit-form .form-group {
    margin-bottom: 25px;
    position: relative;
}

.profile-edit-form .form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #374151;
    font-size: 0.95rem;
}

.profile-edit-form .form-control {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.profile-edit-form .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    outline: none;
    transform: translateY(-1px);
}

.profile-edit-form .form-control[readonly] {
    background: #f8fafc;
    color: #64748b;
    cursor: not-allowed;
}

/* Enhanced Modal Footer */
.profile-modal .modal-footer {
    padding: 25px 30px;
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
    display: flex;
    gap: 15px;
    justify-content: flex-end;
}

.profile-modal .btn {
    padding: 12px 24px;
    border-radius: 10px;
    font-weight: 600;
    font-size: 0.95rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.profile-modal .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.profile-modal .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.profile-modal .btn-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.profile-modal .btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.profile-modal .btn-secondary {
    background: #e2e8f0;
    color: #475569;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.profile-modal .btn-secondary:hover {
    background: #cbd5e1;
    transform: translateY(-1px);
}

/* Edit User Form Styles */
.edit-user-form {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 25px;
}

.form-row .form-group {
    margin-bottom: 0;
}

.edit-user-form .form-group {
    margin-bottom: 25px;
}

.edit-user-form .form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #374151;
    font-size: 0.95rem;
}

.edit-user-form .form-control {
    width: 100%;
    padding: 15px 20px;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.edit-user-form .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    outline: none;
    transform: translateY(-1px);
}

.edit-user-form select.form-control {
    cursor: pointer;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 30px;
    padding-top: 25px;
    border-top: 1px solid #e5e7eb;
}

.form-actions .btn {
    padding: 12px 24px;
    border-radius: 10px;
    font-weight: 600;
    font-size: 0.95rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.form-actions .btn-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.form-actions .btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
}

.form-actions .btn-secondary {
    background: #e2e8f0;
    color: #475569;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-actions .btn-secondary:hover {
    background: #cbd5e1;
    transform: translateY(-1px);
}

/* Info Card Styles */
.info-card {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
}

.info-card h3 {
    margin: 0 0 20px 0;
    color: #374151;
    font-size: 1.2rem;
    font-weight: 600;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f1f5f9;
}

.info-item:last-child {
    border-bottom: none;
}

.info-item label {
    font-weight: 600;
    color: #475569;
    margin: 0;
    min-width: 140px;
}

.info-item span {
    color: #1e293b;
    font-weight: 500;
    text-align: right;
    flex: 1;
}

/* Form Header Styles */
.form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #e5e7eb;
}

.form-header h2 {
    margin: 0;
    color: #374151;
    font-size: 1.5rem;
    font-weight: 600;
}

.form-header .btn {
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.9rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .form-actions {
        flex-direction: column;
    }

    .form-actions .btn {
        width: 100%;
        justify-content: center;
    }

    .form-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }
}

/* Asset Modal Styles */
.asset-modal {
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
}

.asset-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin: 25px 0;
    padding: 0 10px;
}

.asset-details .detail-row {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 16px 20px;
    background: #f8fafc;
    border-radius: 10px;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.asset-details .detail-row:hover {
    background: #f1f5f9;
    border-color: #cbd5e1;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.asset-details .detail-row label {
    font-weight: 600;
    color: #475569;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.asset-details .detail-row span {
    color: #1e293b;
    text-align: left;
    font-weight: 500;
    font-size: 1rem;
    word-break: break-word;
    background: white;
    padding: 8px 12px;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
}

/* Asset Edit Form Styles */
.asset-modal .form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.asset-modal .form-group {
    margin-bottom: 20px;
}

.asset-modal .form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #374151;
    font-size: 0.95rem;
}

.asset-modal .form-control {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.asset-modal .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    outline: none;
    transform: translateY(-1px);
}

.asset-modal select.form-control {
    cursor: pointer;
}

.asset-modal textarea.form-control {
    resize: vertical;
    min-height: 80px;
}

/* Responsive Asset Modal */
@media (max-width: 768px) {
    .asset-modal {
        max-width: 95vw;
        margin: 10px;
    }

    .asset-modal .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .asset-details {
        grid-template-columns: 1fr;
        gap: 15px;
        padding: 0 5px;
    }

    .asset-details .detail-row {
        padding: 12px 15px;
    }

    .asset-details .detail-row label {
        font-size: 0.8rem;
    }

    .asset-details .detail-row span {
        font-size: 0.9rem;
        padding: 6px 10px;
    }
}

.modal-content {
    background-color: white;
    margin: 1% auto;
    padding: 0;
    border-radius: 15px;
    width: 85%;
    max-width: 1200px;
    max-height: 95vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
    position: relative;
    z-index: 1001;
    pointer-events: auto;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 25px;
    border-radius: 15px 15px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.close {
    color: white;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.close:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

/* Modal responsive */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 5% auto;
        max-height: 85vh;
    }

    .modal-header {
        padding: 15px 20px;
    }

    .modal-header h2 {
        font-size: 1.3rem;
    }

    .modal-body {
        padding: 20px;
    }

    .modal-footer {
        flex-direction: column;
        gap: 10px;
    }

    .modal-footer .btn {
        width: 100%;
    }
}

/* Breadcrumb */
.breadcrumb {
    background: #f8f9fa;
    padding: 10px 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-size: 0.9rem;
    color: #6c757d;
}

.breadcrumb a {
    color: #667eea;
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

/* Form Actions */
.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

@media (max-width: 768px) {
    .form-actions {
        flex-direction: column;
    }

    .form-actions .btn {
        width: 100%;
    }
}

/* Alert Styles */
.alert {
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-size: 0.9rem;
    border: 1px solid transparent;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

/* Typography improvements */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Sarabun', sans-serif;
    font-weight: 600;
    line-height: 1.3;
    color: #2d3748;
}

h1 { font-size: 2.5rem; font-weight: 700; }
h2 { font-size: 2rem; font-weight: 600; }
h3 { font-size: 1.5rem; font-weight: 600; }
h4 { font-size: 1.25rem; font-weight: 600; }
h5 { font-size: 1.1rem; font-weight: 600; }
h6 { font-size: 1rem; font-weight: 600; }

p {
    font-weight: 400;
    line-height: 1.6;
    color: #4a5568;
}

/* Better text selection */
::selection {
    background-color: rgba(102, 126, 234, 0.2);
    color: #2d3748;
}

/* View Asset Modal Styles */
.view-asset-modal {
    max-width: 1000px !important;
    width: 80vw !important;
    max-height: 85vh !important;
}

.view-asset-modal .modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 18px 24px;
    border-radius: 12px 12px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-content {
    display: flex;
    align-items: center;
    gap: 10px;
}

.header-icon {
    font-size: 1.2rem;
    background: rgba(255, 255, 255, 0.2);
    padding: 6px;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.header-text h2 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.header-text p {
    margin: 2px 0 0 0;
    opacity: 0.9;
    font-size: 0.75rem;
}

.close-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    font-size: 1.4rem;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    cursor: pointer;
    display: flex !important;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    z-index: 1002;
    position: relative;
    pointer-events: auto;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* Fallback for Font Awesome not loading */
.close-btn i.fas.fa-times::before {
    content: "×";
    font-family: inherit;
    font-size: 1.4rem;
    font-weight: bold;
}

/* Show fallback if Font Awesome fails */
.close-btn span {
    display: none;
}

.close-btn:not(.fa-loaded) span {
    display: inline;
    font-size: 1.4rem;
    font-weight: bold;
}

.close-btn:not(.fa-loaded) i {
    display: none;
}

/* Asset View Container */
.asset-view-container {
    padding: 20px;
    background: white;
}

/* Asset Form Grid */
.asset-form-grid {
    font-family: 'TH Sarabun New', sans-serif;
}

.asset-form-grid .form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.asset-form-grid .form-group {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.asset-form-grid .form-group.full-width {
    flex: 1 1 100%;
}

.asset-form-grid .form-group label {
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.asset-form-grid .form-group label i {
    font-size: 14px;
    width: 16px;
    text-align: center;
}

.asset-form-grid .form-group label i.fa-desktop {
    color: #6f42c1;
}

.asset-form-grid .form-group label i.fa-tag {
    color: #fd7e14;
}

.asset-form-grid .form-group label i.fa-cube {
    color: #e83e8c;
}

.asset-form-grid .form-group label i.fa-building {
    color: #20c997;
}

.asset-form-grid .form-group label i.fa-circle {
    color: #6c757d;
}

.asset-form-grid .form-group label i.fa-server {
    color: #17a2b8;
}

.asset-form-grid .form-group label i.fa-compact-disc {
    color: #6f42c1;
}

.asset-form-grid .form-group label i.fa-barcode,
.asset-form-grid .form-group label i.fa-id-card {
    color: #495057;
}

.asset-form-grid .form-group label i.fa-calendar-alt,
.asset-form-grid .form-group label i.fa-calendar-plus,
.asset-form-grid .form-group label i.fa-calendar-edit {
    color: #28a745;
}

.asset-form-grid .form-group label i.fa-layer-group {
    color: #fd7e14;
}

.asset-form-grid .form-group label i.fa-file-text {
    color: #6c757d;
}

.asset-form-grid .form-group label i.fa-user-plus,
.asset-form-grid .form-group label i.fa-user-edit {
    color: #007bff;
}

.form-control-static {
    padding: 12px 16px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    font-size: 15px;
    font-weight: 600;
    color: #212529;
    min-height: 48px;
    display: flex;
    align-items: center;
}

.status-badge {
    display: inline-block;
    padding: 6px 16px;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 600;
    letter-spacing: 0.5px;
    background-color: #6c757d;
    color: white;
}

.status-badge.active {
    background-color: #28a745;
    color: white;
}

.status-badge.damaged {
    background-color: #dc3545;
    color: white;
}

.status-badge.spare {
    background-color: #ffc107;
    color: #212529;
}

/* Asset Status Card */
.asset-status-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    padding: 12px 14px;
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid #dee2e6;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #6c757d;
    animation: pulse 2s infinite;
}

.status-dot.active {
    background: #28a745;
}

.status-dot.damaged {
    background: #dc3545;
}

.status-dot.spare {
    background: #ffc107;
}

.status-text {
    font-weight: 600;
    font-size: 0.9rem;
    color: #495057;
}

.asset-id-display {
    text-align: right;
}

.asset-id-label {
    display: block;
    font-size: 0.7rem;
    color: #6c757d;
    margin-bottom: 3px;
}

.asset-id-value {
    font-size: 1rem;
    font-weight: 700;
    color: #495057;
    font-family: 'Courier New', monospace;
}

/* Content Grid */
.content-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 12px;
}

.info-card {
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    overflow: hidden;
    transition: all 0.3s ease;
}

.info-card:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    transform: translateY(-1px);
}

.info-card.full-width {
    grid-column: 1 / -1;
}

.card-header {
    background: #f8f9fa;
    padding: 8px 12px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    margin: 0;
    font-size: 0.8rem;
    font-weight: 600;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 5px;
}

.card-header h3 i {
    color: #667eea;
    font-size: 0.75rem;
}

.card-body {
    padding: 12px;
}

/* Info Grid */
.info-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 6px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 0;
    border-bottom: 1px solid #f3f4f6;
}

.info-item:last-child {
    border-bottom: none;
}

.info-item label {
    font-size: 0.7rem;
    font-weight: 600;
    color: #6c757d;
    display: flex;
    align-items: center;
    gap: 4px;
    min-width: 80px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.info-item label i {
    color: #667eea;
    width: 10px;
    font-size: 0.65rem;
}

.info-item span {
    font-size: 0.75rem;
    color: #495057;
    font-weight: 500;
    text-align: right;
    flex: 1;
    margin-left: 8px;
    word-break: break-word;
}

/* Description Content */
.description-content {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 10px;
    border: 1px solid #e9ecef;
    min-height: 40px;
}

.description-content span {
    color: #495057;
    line-height: 1.4;
    font-size: 0.8rem;
}

/* Audit Grid */
.audit-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.audit-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid #667eea;
}

.audit-icon {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
}

.audit-icon.create {
    background: #d4edda;
    color: #155724;
}

.audit-icon.update {
    background: #d1ecf1;
    color: #0c5460;
}

.audit-content {
    flex: 1;
}

.audit-content label {
    display: block;
    font-size: 0.65rem;
    font-weight: 600;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 2px;
}

.audit-content span {
    display: block;
    font-size: 0.75rem;
    font-weight: 500;
    color: #495057;
    margin-bottom: 1px;
}

.audit-content small {
    font-size: 0.65rem;
    color: #6c757d;
    margin-top: 2px;
    display: block;
}

/* Responsive Design for Asset View */
@media (max-width: 768px) {
    .view-asset-modal {
        width: 95vw !important;
        max-height: 90vh !important;
    }

    .view-asset-modal .modal-header {
        padding: 12px 15px;
    }

    .header-content {
        gap: 8px;
    }

    .header-icon {
        width: 35px;
        height: 35px;
        font-size: 1.3rem;
    }

    .header-text h2 {
        font-size: 1.1rem;
    }

    .header-text p {
        font-size: 0.7rem;
    }

    .content-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .asset-status-card {
        flex-direction: column;
        gap: 12px;
        text-align: center;
        padding: 12px;
    }

    .info-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .audit-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .card-header {
        padding: 10px 12px;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .card-body {
        padding: 12px;
    }

    .info-item span {
        padding: 5px 8px;
        font-size: 0.8rem;
    }

    .audit-item {
        padding: 10px;
        gap: 10px;
    }

    .audit-icon {
        width: 30px;
        height: 30px;
        font-size: 0.9rem;
    }
}
