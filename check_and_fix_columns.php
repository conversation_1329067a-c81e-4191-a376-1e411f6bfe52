<?php
// ตรวจสอบและแก้ไขโครงสร้างตาราง assets
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>🔧 ตรวจสอบและแก้ไขโครงสร้างตาราง Assets</h2>";

// เชื่อมต่อฐานข้อมูล
try {
    $pdo = new PDO("mysql:host=localhost;dbname=asset_management;charset=utf8", 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p style='color: green;'>✅ เชื่อมต่อฐานข้อมูลสำเร็จ</p>";
} catch (Exception $e) {
    die("<p style='color: red;'>❌ ไม่สามารถเชื่อมต่อฐานข้อมูล: " . $e->getMessage() . "</p>");
}

// ตรวจสอบโครงสร้างตารางปัจจุบัน
echo "<h3>1. โครงสร้างตารางปัจจุบัน:</h3>";
try {
    $stmt = $pdo->query("DESCRIBE assets");
    $columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; background: white;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 10px;'>Field</th>";
    echo "<th style='padding: 10px;'>Type</th>";
    echo "<th style='padding: 10px;'>Null</th>";
    echo "<th style='padding: 10px;'>Key</th>";
    echo "<th style='padding: 10px;'>Default</th>";
    echo "<th style='padding: 10px;'>Extra</th>";
    echo "</tr>";
    
    $existingColumns = [];
    foreach ($columns as $column) {
        $existingColumns[] = $column['Field'];
        echo "<tr>";
        echo "<td style='padding: 10px;'><strong>{$column['Field']}</strong></td>";
        echo "<td style='padding: 10px;'>{$column['Type']}</td>";
        echo "<td style='padding: 10px;'>{$column['Null']}</td>";
        echo "<td style='padding: 10px;'>{$column['Key']}</td>";
        echo "<td style='padding: 10px;'>{$column['Default']}</td>";
        echo "<td style='padding: 10px;'>{$column['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<p><strong>คอลัมน์ที่มีอยู่:</strong> " . implode(', ', $existingColumns) . "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ ไม่สามารถดูโครงสร้างตาราง: " . $e->getMessage() . "</p>";
    die();
}

// ตรวจสอบคอลัมน์ที่ต้องการ
echo "<h3>2. ตรวจสอบคอลัมน์ที่ต้องการ:</h3>";

$requiredColumns = [
    'id' => 'INT AUTO_INCREMENT PRIMARY KEY',
    'type' => 'VARCHAR(100)',
    'brand' => 'VARCHAR(100)',
    'model' => 'VARCHAR(100)',
    'tag' => 'VARCHAR(100)',
    'department' => 'VARCHAR(100)',
    'status' => "VARCHAR(50) DEFAULT 'ใช้งาน'",
    'hostname' => 'VARCHAR(100)',
    'operating_system' => 'VARCHAR(100)',
    'serial_number' => 'VARCHAR(100)',
    'asset_id' => 'VARCHAR(100)',
    'warranty_expire' => 'DATE',
    'description' => 'TEXT',
    'set_name' => 'VARCHAR(100)',
    'created_by' => 'VARCHAR(100)',
    'updated_by' => 'VARCHAR(100)',
    'created_date' => 'DATETIME DEFAULT CURRENT_TIMESTAMP',
    'updated_date' => 'DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
];

$missingColumns = [];
$wrongColumns = [];

echo "<ul>";
foreach ($requiredColumns as $columnName => $columnDefinition) {
    if (in_array($columnName, $existingColumns)) {
        echo "<li style='color: green;'>✅ <strong>$columnName</strong> - มีอยู่</li>";
    } else {
        echo "<li style='color: red;'>❌ <strong>$columnName</strong> - ไม่มี</li>";
        $missingColumns[$columnName] = $columnDefinition;
    }
}

// ตรวจสอบคอลัมน์เก่าที่ควรเปลี่ยนชื่อ
$oldColumns = [
    'person_added' => 'created_by',
    'person_modified' => 'updated_by',
    'date_added' => 'created_date',
    'date_modified' => 'updated_date'
];

foreach ($oldColumns as $oldName => $newName) {
    if (in_array($oldName, $existingColumns)) {
        echo "<li style='color: orange;'>⚠️ <strong>$oldName</strong> - ควรเปลี่ยนเป็น <strong>$newName</strong></li>";
        $wrongColumns[$oldName] = $newName;
    }
}
echo "</ul>";

// แก้ไขโครงสร้างตาราง
if ($_POST && isset($_POST['fix_structure'])) {
    echo "<h3>3. กำลังแก้ไขโครงสร้างตาราง...</h3>";
    
    $fixedCount = 0;
    
    // เปลี่ยนชื่อคอลัมน์เก่า
    foreach ($wrongColumns as $oldName => $newName) {
        try {
            if (strpos($newName, 'date') !== false) {
                $sql = "ALTER TABLE assets CHANGE $oldName $newName DATETIME";
                if ($newName == 'created_date') {
                    $sql .= " DEFAULT CURRENT_TIMESTAMP";
                } elseif ($newName == 'updated_date') {
                    $sql .= " DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP";
                }
            } else {
                $sql = "ALTER TABLE assets CHANGE $oldName $newName VARCHAR(100)";
            }
            
            $pdo->exec($sql);
            echo "<p style='color: green;'>✅ เปลี่ยนชื่อคอลัมน์ '$oldName' เป็น '$newName' สำเร็จ</p>";
            $fixedCount++;
            
            // อัพเดทรายการ
            unset($missingColumns[$newName]);
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ ไม่สามารถเปลี่ยนชื่อคอลัมน์ '$oldName': " . $e->getMessage() . "</p>";
        }
    }
    
    // เพิ่มคอลัมน์ที่ขาดหายไป
    foreach ($missingColumns as $columnName => $columnDefinition) {
        try {
            $sql = "ALTER TABLE assets ADD COLUMN $columnName $columnDefinition";
            $pdo->exec($sql);
            echo "<p style='color: green;'>✅ เพิ่มคอลัมน์ '$columnName' สำเร็จ</p>";
            $fixedCount++;
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ ไม่สามารถเพิ่มคอลัมน์ '$columnName': " . $e->getMessage() . "</p>";
        }
    }
    
    if ($fixedCount > 0) {
        echo "<p style='color: green;'><strong>✅ แก้ไขโครงสร้างตารางเสร็จสิ้น ($fixedCount การเปลี่ยนแปลง)</strong></p>";
        echo "<p><a href='check_and_fix_columns.php'>รีเฟรชหน้าเพื่อดูผลลัพธ์</a></p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ ไม่มีการเปลี่ยนแปลงโครงสร้างตาราง</p>";
    }
}

// ทดสอบ INSERT หลังแก้ไข
if ($_POST && isset($_POST['test_insert'])) {
    echo "<h3>4. ทดสอบ INSERT หลังแก้ไข:</h3>";
    
    try {
        $sql = "INSERT INTO assets (type, brand, model, tag, department, status, hostname, operating_system, serial_number, asset_id, warranty_expire, description, set_name, created_by, updated_by, created_date, updated_date) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";
        
        echo "<h4>SQL ที่ใช้:</h4>";
        echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 0.9rem;'>";
        echo htmlspecialchars($sql);
        echo "</div>";
        
        $stmt = $pdo->prepare($sql);
        $result = $stmt->execute([
            'Desktop',
            'Dell',
            'OptiPlex 7090',
            'TAG-TEST',
            'IT Department',
            'ใช้งาน',
            'PC-TEST',
            'Windows 10',
            'SN123456789',
            'ASSET-TEST',
            '2025-12-31',
            'Test Asset Description',
            'Test Set',
            'admin',
            'admin'
        ]);
        
        if ($result) {
            $newId = $pdo->lastInsertId();
            echo "<p style='color: green;'>✅ INSERT สำเร็จ! ID: $newId</p>";
            
            // แสดงข้อมูลที่เพิ่ม
            $checkStmt = $pdo->prepare("SELECT * FROM assets WHERE id = ?");
            $checkStmt->execute([$newId]);
            $newRecord = $checkStmt->fetch();
            
            echo "<h4>ข้อมูลที่เพิ่ม:</h4>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%; background: white;'>";
            echo "<tr style='background: #f8f9fa;'><th style='padding: 8px;'>Field</th><th style='padding: 8px;'>Value</th></tr>";
            foreach ($newRecord as $key => $value) {
                if (!is_numeric($key)) {
                    echo "<tr><td style='padding: 8px;'><strong>$key</strong></td><td style='padding: 8px;'>$value</td></tr>";
                }
            }
            echo "</table>";
            
            // ลบข้อมูลทดสอบ
            $deleteStmt = $pdo->prepare("DELETE FROM assets WHERE id = ?");
            $deleteStmt->execute([$newId]);
            echo "<p style='color: blue;'>🗑️ ลบข้อมูลทดสอบแล้ว</p>";
            
        } else {
            echo "<p style='color: red;'>❌ INSERT ไม่สำเร็จ</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ INSERT error: " . $e->getMessage() . "</p>";
        echo "<p style='color: red;'>Error Code: " . $e->getCode() . "</p>";
    }
}

// แสดงคำสั่ง SQL สำหรับสร้างตารางใหม่
echo "<h3>5. คำสั่ง SQL สำหรับสร้างตารางใหม่:</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 0.9rem;'>";
echo htmlspecialchars("
CREATE TABLE assets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    type VARCHAR(100),
    brand VARCHAR(100),
    model VARCHAR(100),
    tag VARCHAR(100),
    department VARCHAR(100),
    status VARCHAR(50) DEFAULT 'ใช้งาน',
    hostname VARCHAR(100),
    operating_system VARCHAR(100),
    serial_number VARCHAR(100),
    asset_id VARCHAR(100),
    warranty_expire DATE,
    description TEXT,
    set_name VARCHAR(100),
    created_by VARCHAR(100),
    updated_by VARCHAR(100),
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
");
echo "</div>";

?>

<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ตรวจสอบและแก้ไขโครงสร้างตาราง Assets</title>
    <style>
        body {
            font-family: 'TH Sarabun New', Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h2 {
            background: #e74c3c;
            color: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        h3 {
            background: #34495e;
            color: white;
            padding: 10px;
            border-radius: 5px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .action-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        button {
            background: #3498db;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
        }
        button:hover {
            background: #2980b9;
        }
        .btn-success {
            background: #27ae60;
        }
        .btn-success:hover {
            background: #219a52;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>

<?php if (!empty($missingColumns) || !empty($wrongColumns)): ?>
<div class="action-section">
    <h3>🔧 แก้ไขโครงสร้างตาราง</h3>
    <p>พบปัญหาโครงสร้างตาราง กรุณาคลิกเพื่อแก้ไข:</p>
    <ul>
        <?php foreach ($wrongColumns as $oldName => $newName): ?>
            <li>เปลี่ยนชื่อ <strong><?= $oldName ?></strong> เป็น <strong><?= $newName ?></strong></li>
        <?php endforeach; ?>
        <?php foreach ($missingColumns as $columnName => $columnDefinition): ?>
            <li>เพิ่มคอลัมน์ <strong><?= $columnName ?></strong></li>
        <?php endforeach; ?>
    </ul>
    <form method="POST">
        <input type="hidden" name="fix_structure" value="1">
        <button type="submit" class="btn-success">🔧 แก้ไขโครงสร้าง</button>
    </form>
</div>
<?php else: ?>
<div class="action-section">
    <h3>✅ โครงสร้างตารางถูกต้อง</h3>
    <p>โครงสร้างตาราง assets ถูกต้องแล้ว</p>
</div>
<?php endif; ?>

<div class="action-section">
    <h3>🧪 ทดสอบ INSERT</h3>
    <p>ทดสอบการเพิ่มข้อมูลหลังจากแก้ไขโครงสร้างแล้ว</p>
    <form method="POST">
        <input type="hidden" name="test_insert" value="1">
        <button type="submit">🧪 ทดสอบ INSERT</button>
    </form>
</div>

<div class="warning">
    <strong>⚠️ หมายเหตุ:</strong> การแก้ไขจะเปลี่ยนแปลงโครงสร้างตาราง assets ไม่มีการลบหรือแก้ไขข้อมูลที่มีอยู่
</div>

<div style="margin: 20px 0;">
    <a href="final_test_add_asset.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">🎯 ทดสอบ Final</a>
    <a href="add_asset.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">➕ หน้าเพิ่ม Asset</a>
    <a href="index.php" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;">🏠 หน้าหลัก</a>
</div>

</body>
</html>
